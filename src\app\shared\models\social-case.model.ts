export interface SocialCase {
    Id: string;
    RequestName: string;
    CaseId?: string;
    Template?: {
        TemplateId: string;
        TemplateName: string;
        TemplateNameAr: string;
    };
    CreatedDate: string;
    Status: {
        Key?: string;
        Value: string | number;
        label?: string;
        labelAr?: string;
    };
    EligibleForEdit?: boolean;
    EligibleForAppeal?: boolean;
    IsNomiatedInflationCase?: boolean;
    TemplateId?: string;
    ServiceCatalogue: string;
    ServiceCatalogueName?: string;
    ServiceCatalogueNameAR?: string;
    FinalComment?: string;
    EstablishmentID?: string;
    EntityName?: string;
}

export interface StatusOption {
    value: string;
    label: string;
    labelAr?: string;
}

export interface EditReason {
    id: number;
    label: string;
    labelAr?: string;
    value?: string;
}
