import { Component, Input, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { SortEvent } from '../../../shared/directives/sortable.diractive';
import { DataService } from '../../../shared/services/data.service';
import { AlertService } from '../../../shared/services/alert.service';
import { LanguageService } from '../../../shared/services/language.service';
import { AuthService } from '../../../shared/services/auth.service';
import { getValueFromName } from '../../../shared/enums/application-status.enum';
import { finalize } from 'rxjs/operators';
import { SocialCase } from '../../../shared/models/social-case.model';

@Component({
  selector: 'app-my-social-cases',
  templateUrl: './my-social-cases.component.html',
  styleUrls: ['./my-social-cases.component.scss']
})
export class MySocialCasesComponent implements OnInit {

  @Input() status: string = '-1';
  @Input() disabled: boolean = true;

  userInfo: any;
  statusId = "-1";
  page = 1;
  pageSize = 10;

  filteredData: SocialCase[] = [];
  filteredCount: number = 0;
  sortBy = 'CreatedDate';
  sortOrder = 'desc';
  searchTerm = '';

  // Loading state
  isLoading = false;

  constructor(
    private dataService: DataService,
    private alert: AlertService,
    private router: Router,
    private route: ActivatedRoute,
    public lang: LanguageService,
    public auth: AuthService
  ) { }

  ngOnInit(): void {
    const status = this.route.snapshot.paramMap.get('status');
    this.disabled = false;
    if (status) {
      this.status = status;
    } else {
      this.status = '-1';
    }
    this.statusId = getValueFromName(this.status)?.toString() ?? '-1';
    this.userInfo = this.auth.getUserInfo();
    this.getData();
  }

  onSort({ column, direction }: SortEvent) {
    this.sortBy = column;
    this.sortOrder = direction;
    this.applyFilters();
  }

  search() {
    this.applyFilters();
  }

  //::TODO Replace static Id with CRM_USER_ID (Heaiba)
  getData() {
    this.isLoading = true;


    // Use the same approach as React implementation
    const requestBody = {};
    const params: any = {
      _emirateId: this.userInfo?.userInfo?.idn
    };

    // Add comprehensive query parameters
    if (this.statusId && this.statusId !== '-1') {
      params['statusId'] = this.statusId;
    }

    // Add pagination parameters
    params['page'] = this.page;
    params['pageSize'] = this.pageSize;

    // Add sorting parameters
    params['sortColumn'] = this.sortBy;
    params['sortDirection'] = this.sortOrder;

    // Add search term if provided
    if (this.searchTerm && this.searchTerm.trim()) {
      params['searchTerm'] = this.searchTerm.trim();
    }

    // Convert params to query string for the URL
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    this.dataService
      .post(`swpproxy/Request/RetrieveAllRequests?${queryString}`, requestBody)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (res) => {
          if (res && (res.Data || res.data)) {
            // Handle both Data and data response formats
            this.filteredData = res.Data || res.data;
            this.filteredCount = res.dataCount || res.DataCount || this.filteredData.length;
            // Sort data by creation date (newest first) like in React version
            if (this.filteredData && Array.isArray(this.filteredData)) {
              this.filteredData.sort((a, b) =>
                new Date(b.CreatedDate).getTime() - new Date(a.CreatedDate).getTime()
              );
            }
          } else {
            this.filteredData = [];
            this.filteredCount = 0;
          }
        },
        error: () => {
          this.filteredData = [];
          this.filteredCount = 0;
          this.alert.showMessage('error', 'Failed to load applications. Please try again.');
        }
      });
  }

  onPageChange() {
    this.applyFilters();
  }
  onSelectChange() {
    this.applyFilters();
  }
  onStatusChange() {
    this.applyFilters();
  }

  // Handle edit click for different allowance types
  // Parameter structure matches React StatusPill: (e, isHousingEducationTopup, isHousingTopup, isEducationTopup)
  handleEditClick(event: Event, app: SocialCase, isHousingEducationTopup: boolean = false, isHousingTopup: boolean = false, isEducationTopup: boolean = false) {
    event.stopPropagation();

    // Navigate to the appropriate edit page with additional parameters
    if (app.CaseId) {
      const navigationExtras: any = {
        queryParams: {}
      };

      // Add query parameters based on the type of allowance being applied for
      if (isHousingEducationTopup) {
        navigationExtras.queryParams.applyHousingEducation = 'true';
      } else if (isHousingTopup) {
        navigationExtras.queryParams.applyHousing = 'true';
      } else if (isEducationTopup) {
        navigationExtras.queryParams.applyEducation = 'true';
      }

      // Navigate based on the service type
      if (this.isInflationTemplate(app)) {
        this.router.navigate(['/e-services/inflation-service', app.CaseId], navigationExtras);
      } else {
        // Fallback to general edit route
        this.router.navigate(['/e-services/social-aid', app.CaseId], navigationExtras);
      }
    } else {
      this.alert.showMessage('error', 'Unable to process request. Case ID not found.');
    }
  }

  // Check if application is inflation template (hide top-up options)
  isInflationTemplate(app: SocialCase): boolean {
    const INFLATION_TEMPLATE_ID = '94b2a9e5-d2ae-ee11-a568-000d3a6c23a9';
    const INFLATION_TEMPLATE_ID_2 = 'c1b5dedd-62e8-40a6-9a3a-997355dda8ec';
    const templateId = app?.TemplateId || app?.Template?.TemplateId || '';
    return templateId === INFLATION_TEMPLATE_ID || templateId === INFLATION_TEMPLATE_ID_2;
  }

  // Page size change handler
  onPageSizeChange() {
    this.page = 1; // Reset to first page
    this.applyFilters();
  }

  // Get status display information
  getStatusInfo(status: any): { color: string; label: string } {
    if (!status) return { color: '#C3C6CB', label: 'Unknown' };

    const statusValue = status.value || status.Value || status;

    switch (statusValue) {
      case 'Draft':
      case 1:
        return { color: '#C3C6CB', label: this.lang.IsArabic ? 'مسودة' : 'Draft' };
      case 'Submitted':
      case 100000000:
        return { color: '#4A9D5C', label: this.lang.IsArabic ? 'مقدم' : 'Submitted' };
      case 'In Progress':
      case 100000001:
        return { color: '#F8C027', label: this.lang.IsArabic ? 'قيد الإجراء' : 'In Progress' };
      case 'Returned':
      case 100000002:
        return { color: '#D83731', label: this.lang.IsArabic ? 'تم الإرجاع' : 'Returned' };
      case 'Approved':
      case 100000003:
        return { color: '#4A9D5C', label: this.lang.IsArabic ? 'موافقة' : 'Approved' };
      case 'Rejected':
      case 100000004:
        return { color: '#eb0505', label: this.lang.IsArabic ? 'مرفوض' : 'Rejected' };
      case 'Pending Payment':
      case 100000005:
        return { color: '#286CFF', label: this.lang.IsArabic ? 'بإنتظار الدفع' : 'Pending Payment' };
      default:
        return { color: '#C3C6CB', label: String(statusValue) };
    }
  }

  // Apply filters and refresh data
  applyFilters() {
    this.page = 1; // Reset to first page when applying filters
    this.getData();
  }
}
