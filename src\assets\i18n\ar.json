{"hello": "مرحبا", "home": "صفحتي الشخصية", "languages": "اللغات", "More": "المزيد", "about": "عن", "servicesHeader": "الخدمات والمعلومات", "Welcome": "مرحبا، ", "service_uae_national_only": "يمكن للمواطن الإماراتي فقط التقدم لهذه الخدمة", "Sign out": "تسجيل خروج", "First Name": "الاسم الأول", "Last Name": "اسم العائلة", "Summary": "ملخص", "Next": "التالي", "Full Name": "الاسم", "Your UAE Pass": "معلومات الهوية الرقمية", "Phone number": "رقم الموبايل", "Date of Birth": "تاريخ الميلاد", "Previous": "السابق", "Attachments": "المرفقات", "Submit": "قدم الطلب", "Pay": "دفع", "Close": "اغلاق", "Application Number": "رق<PERSON> الطلب", "Help": "مساعدة", "Need Help?": "بحاجة لمساعدة؟", "Your preferred mobile": "رقم الموبايل المفضل لك", "FAQs": "الاسئلة الشائعة", "Contact us": "اتصل بنا", "8000000": "8000000", "Customer Charter": "ميثاق العملاء", "loading": "جاري التحميل...", "Search": "ب<PERSON><PERSON>", "itemsPerPage": "لكل صفحة", "Total count": "الع<PERSON><PERSON> الكلي", "Email": "الب<PERSON>يد الإلكتروني", "Mobile": "رقم الموبايل", "MobileV2": "رقم الموبايل (5000000000 (971))", "Preferred Email": "البريد الاكتروني المفضل لك", "Preferred Mobile": "رقم الموبايل المفضل لك", "Preferred MobileV2": "رقم الموبايل المفضل لك (5000000000 (971))", "City": "المدينة", "Address": "العنوان", "Country": "البلد", "Emirate": "الإمارة", "Emirate with search": "الامارة مع خاصية البحث", "Gender": "النوع", "Choose a file": "ا<PERSON><PERSON><PERSON> ملف", "format": "نوع الملفات المسموحة: PDF, JPG, PNG  الحد الأقصى لحجم الملف: 5MB", "formatPhoto": "نوع الملفات المسموحة: JPG, PNG  الحد الأقصى لحجم الملف: 5MB", "formatOnly": "نوع الملفات المسموحة: ", "MaxFileSizeOnly": "الحد الأقصى لحجم الملف: ", "Emirates ID": "الهوية الإماراتية", "Passport": "جواز السفر", "Please enter": "<PERSON><PERSON><PERSON>ل ", "Please select...": "ير<PERSON>ى اختيار ", "PleaseSelect": "ير<PERSON>ى اختيار ", "Search...": "بحث...", "langToggle": "ع", "The title of the card": "عنوان البطاقة", "Apply for": "أتقدم لـ", "Applicant Information": "معلومات مقدم الطلب", "Step One": "الخطوة الأولى", "Step Two": "الخطوة الثانية", "Step Three": "الخطوة الثالثة", "Fill the form": "تعبئة النموذج", "Summary & Submit": "ملخص الطلب والتقديم", "noMatchingDataFound": "لم يتم العثور على بيانات مطابقة", "Name": "اسم الملف", "Type": "النوع", "Size": "الحجم", "Action": "إجراء", "bytes": "بايت", "Service": "خدمة", "Start Service": "إ<PERSON><PERSON><PERSON> الخدمة", "Service Card": "بطاقة الخدمة", "Some quick example text": "مثال لشرح الخدمة", "In progress": "قيد الاجراء", "dashboard-description": "هنا، يمكنك الوصول بسهولة إلى طلباتك ومدفوعاتك وتلقي اقتراحات ذكية بناءً على بياناتك من الهوية الرقمية.", "Completed": "تم الانتهاء", "Returned": "مرجع", "Ready To Pay": "جاهز للدفع", "Draft": "مسودة", "View All": "عر<PERSON> الكل", "Progress": "نسبة الإنجاز", "Service Form": "نموذج الطلب", "Additional Information": "معلومات اضافية", "Add Member": "اضافة عضو", "Add Objective": "اضافة هدف", "Add Example Of Activities": " إضافة نشاط/برنامج", "Add Purpose / Activity": "إضافة غرض / نشاط", "Add Room Administrator": "إضافة مسؤول غرفة العبادة", "Add Extension Request": "إضافة طلب تمديد", "Add FundServices": "إضافة خدمة جديدة", "Conditions": "شروط", "consentText": "أو<PERSON><PERSON><PERSON> على الشروط والأحكام", "readTermsAndConditions": "لقد قرأت ووافقت على", "termsAndConditions": "الشروط والأحكام", "acknowledgementAndUndertaking": "الإقرار والتعهد", "confirmAccurateInformation": "أؤكد أن المعلومات والبيانات الواردة صحيحة ودقيقة", "Edit Member": "تعديل العضو", "Cancel": "الغاء", "Reopened": "إعادة فتح", "problemSolved": "تم حل المشكلة", "Closed": "مغلق", "inProgress": "قيد التنفيذ", "InformationProvided": "تم تزويد المعلومات", "saveAsDraft": "ح<PERSON>ظ كمسودة", "submitRequest": "تقديم الطلب", "Continue": "متابعة", "Proposed": "مق<PERSON><PERSON><PERSON>", "Name (Google)": "(جوجل)", "DoYouAcceptThisTranslation": "هل تقبل بهذه الترجمة؟", "yes": "نعم", "English": " الإنجليزي", "Arabic": " العربي", "Other Languages": "لغات اخرى", "French": "الفرنسية", "Spanish": "الاسبانية", "German": "الالمانية", "Portuguese": "البرتغالية", "Russian": "الروسية", "otherLanguageText": "تستخدم قائمة 'اللغات الأخرى' المذكورة أعلاه خدمة ترجمة جوجل لإنشاء ترجمة آلية للمحتوى لغرض العرض. دقة الترجمة الآلية للمحتوى غير مضمونة.", "dashboard": {"welcome": "عزيزي المتعامل نرحب بكم في بوابة الخدمات الموحدة الجديدة والتي يمكنكم التقديم من خلالها على الخدمات", "link": "من خلال الرابط"}, "sort": {"sortBy": "<PERSON><PERSON><PERSON> حسب", "mostRelevant": "الأكثر صحة", "oldestFirst": "الأقدم أولا", "newestFirst": "الأحدث أولا"}, "footer": {"About MoCD": "عن الوزارة", "Cool stuff": "أشياء رائعة", "Random feature": "الميزة العشوائية", "Team feature": "ميزة الفريق", "Support": "الدعم", "Resource": "الموارد", "Resource name": "اسم المورد", "About Website": "عن الموقع", "Team": "المصطلحات", "Locations": "المواقع", "Privacy": "الخصوصية", "Terms": "المصطلحات", "Guidelines": "القواعد الإرشادية", "Contact Us": "اتصل بنا", "The site is best viewed in screen resolution 1920 x 1080. Support, Microsoft Edge, Firefox 10+, Google Chrome 12+. Safari 3+": "لأفضل عرض لهذا الموقع استخدام دقة الشاشة 1920 × 1080. يدعم، Microsoft Edge, Firefox 10+, Google Chrome 12+. Safari 3+", "© Copyright 2025. Ministry of Community Empowerment - United Arab Emirates": "جميع الحقوق محفوظة 2025. وزارة تمكين المجتمع - الإمارات العربية المتحدة.", "Last updated on": "اخر تحديث في", "Desclaimer": "إخلاء المسؤولية", "Privacy Policy": "سياسة الخصوصية", "Terms & Conditions": "الشروط والأحكام", "Copyrights": "حقو<PERSON> النسخ", "Mobile App": "تطبيق الموبايل", "Channel Programs": "برامج القناة", "Sitemap": "خريطة الموقع", "Archive": "الأرشيف", "Careers": "الوظائف", "Frequently Asked Questions": "الأسئلة الأكثر شيوعاً", "Accessibility Statement": "بيان إمكانية الوصول", "Whistleblower Form (IAO)": "الإبلاغ عن المخالفات- مكتب التدقيق الداخلي", "Remote Access": "العمل عن بعد", "Employee Mail": "البريد الالكتروني للموظفين", "Communicate with leadership": "التواصل مع القيادة", "Customer Happiness Centers": "مراكز سعادة المتعاملين", "Customer Happiness Charter": "ميثاق إسعاد المتعاملين", "UAE Promise Guidelines": "نموذج وعد حكومة دولة الامارات", "copyrights": "كل الحقوق محفوظة. وزارة تمكين المجتمع - الإمارات العربية المتحدة", "Follow us on": "تابعنا على", "Laws, Legislation and Policies": "القوانين والتشريعات والسياسات", "Disclaimer": "إخلاء المسؤولية", "About the website": "عن الموقع", "Information and support": "المعلومات والدعم", "About MOCD": "عن الوزارة", "About the minister": "نبذة عن الوزير", "About the ministry": "عن الوزارة", "Toll free": "الرقم المجاني", "The UAE charter for Future Services": "ميثاق الإمارات العربية المتحدة للخدمات المستقبلية", "Awards": "الجوائز", "Procurement": "شراء", "The Ministry": "الوزارة", "Using the website": "استخدام الموقع", "Terms and conditions": "الشروط والأحكام", "Accessibility": "إمكانية الوصول", "Digital participation policy": "سياسة المشاركة الرقمية", "Services catalogue": "كتالوج الخدمات", "Media centre": "مركز الاعلام", "FAQ’s": "الأسئلة الشائعة", "Feedback and complaints": "الملاحظات والشكاوى", "References": "مراجع", "Regulations": "أنظمة", "Media kit": "مجموعة الوسائط", "Abbreviations and glossary": "الاختصارات والمصطلحات", "Number of visitors": "<PERSON><PERSON><PERSON> الزوار", "Load Time": "وقت التحميل", "AboutUs": "عن الوزارة", "CopyRights": "حقوق الطبع و النشر"}, "attachment": {"PersonalPhoto": "تحميل الصورة الشخصية", "MedicalReport": "تحميل التقرير الطبي", "PsychologicalEducationalReport": "تقرير نفسي/تعليمي", "Other": "مستند داعم آخر"}, "applications": {"ApplicationNumber": "رق<PERSON> الطلب", "ServiceName": "إسم الخدمة", "Status": "حالة الطلب", "SubmissionDate": "تاريخ التقديم", "FinalComment": "ملاحظات", "Actions": "إجراءات"}, "fundraisingPermitList": {"title": "قائمة تصاريح جمع التبرعات", "permitNumber": "رقم التصريح", "establishmentName": "اسم المؤسسة", "legalForm": "الصيغة القانونية", "location": "الموقع", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "applicationStatus": "حالة الطلب", "permitStatus": "حالة التصريح", "actions": "الإجراءات", "extendFundraisingPermit": "تمديد تصريح جمع التبرعات", "appealToPermitRejection": "الاستئناف لرفض الترخيص"}, "compliants": {"complaintNumber": "رقم الأستفسار / الاقتراح", "complaintRequestType": "نوع الأستفسار / الاقتراح ", "raisedAt": "التاريخ", "status": "الحالة", "reopenRequest": "إعادة فتح الطلب", "reopenReason": "سبب إعادة فتح الطلب", "ticketNumber": "الرقم المرجعي", "title": "العنوان"}, "status": {"all": "جميع الطلبات", "Completed": "من<PERSON><PERSON>", "InProgress": "قيد الاجراء", "Submitted": "المرسل", "Draft": "مسودة", "OnHold": "في الانتظار", "Rejected": "مرفو<PERSON>", "Approved": "تمت الموافقة", "Returned": "مرجع", "PendingPayment": "بانتظار الدفع"}, "payment": {"Status": "الحالة", "Success": "نجاح العملية", "Failed": "فشل العملية", "Payment ID": "رقم مرجعي للدفعة", "Payment Provider Message": "رسالة مزود خدمة الدفع", "Payment Date": "تاريخ العملية", "Payment Amount": "قيمة الدفعة"}, "services": {"title": "الخدمات", "service1": {"title": "خدمة تجريبة 1", "Service Form": "نموذج الطلب"}, "service2": {"title": "خدمة تجريبة 2", "Service Form": "نموذج الطلب"}, "inflationService": {"title": "علاوة بدل التضخم", "Form": {"caseInformation": {"title": "", "fields": {"reasonForApply": "سبب تقديمي للطلب"}}}}, "podNewCard": {"title": "خدمة إصدار بطاقة جديدة لأصحاب الهمم", "Service Form": "نموذج الطلب", "Labor Sector": "القطاع", "Employer": "جهة العمل", "Job Title": "المسمى الوظيفي", "Employment Status": "الحالة الوظيفية", "Are you employed?": "هل انت موظف؟", "Are you student?": "هل انت طالب", "Educational Status": "الحالة التعليمية", "Educational Level": "المستوى التعليمي / المؤهلات", "Registration Status": "حالة التسجيل", "School": "اسم المدرسة / المركز / الجامعة / المعهد", "Grade": "المرحلة / الصف", "Support?": "هل يتلقى دعم التربية الخاصة؟", "Is there a shadow teacher?": "هل يوجد معلم بديل؟", "Disability Type": "نوع الإعاقة", "Disability Information": "معلومات الإعاقة", "Card Delivery": "توصيل البطاقة", "deliveryAddress": "عنوان التوصيل", "Want card delivery?": "هل ترغب بتوصيل البطاقة؟", "Terms and Conditions": "الشروط والأحكام", "1": "تعتبر بطاقة أصحاب الهمم وثيقة رسمية دالة على أن حاملها من ذوي الإعاقة، بما يكفل له كافة الحقوق والخدمات المنصوص عليها في قانون اتحادي 29 لسنة 2006 بشأن حقوق المعاقين المعدل بقانون اتحادي رقم ( 14 ) لسنة 2009.", "2": "يرجى قراءة الشروط التالية بعناية قبل إتمام عملية التقدم لبطاقة (أصحاب الهمم).", "3": "تسري الشروط هذه اعتباراً من تاريخ نشرها على الموقع الإلكتروني، وتحتفظ وزارة تمكين المجتمع بحق تعديل الشروط من وقت لآخر دون الإخطار عن أية تعديلات ويتحمل مقدم الطلب مسؤولية الاطلاع على الشروط من وقت لآخر للتأكد من التزامه بها.", "4": "بضغط المتقدم على زر إتمام عملية التقدم لبطاقة (أصحاب الهمم) فإنه يوافق على ما يلي:  ", "5": "اطلاع المختصين بدراسة الطلبات على جميع بيانات مقدم الطلب الواردة في طلبه", "6": "طلب بيانات خاصة بمقدم الطلب (تقارير طبية، نفسية والخ) من جهات أخرى  ", "7": "تحويل الطلب إلى فريق التقييم في المركز الوطني للتشخيص والتقييم التابع لوزارة تمكين المجتمع.", "8": "الغاء الطلب في حال لم يتم متابعة أو الاستجابة من طرف مقدم الطلب بتزويد النظام – الطلب بالمعلومات الإضافية أو الوثائق المطلوبة لهذا الاجراء وذلك بعد مرور ستة أشهر من طلب هذه المعلومات أو الوثائق.", "9": "في حال رفض طلبك، يجوز لك طلب تظلم من قرار الرفض عن طريق الموقع الالكتروني وذلك بتقديم تقارير وأدلة تبرر التظلم خلال فترة لا تزيد عن 60 يوم من رفض الطلب ولمرة واحدة فقط.", "10": "مشاركة معلومات مقدم الطلب مع جهات حكومية أخرى للضرورة ولمصلحة حامل البطاقة.", "11": "عرض بيان الإعاقة (في حال الموافقة على الطلب) على بطاقة الهوية الإماراتية من الجهة الخلفية.", "12": "عدم استغلال البطاقة لغير الأغراض المصرح بها قانونياً", "13": "الإبلاغ عن وفاة صاحب البطاقة أو انتقاله للعيش الدائم خارج الدولة", "14": "يمكن الحصول على أية معلومات إضافية عن طريق الاتصال بـ 800 623"}, "marriageGrant": {"title": "من<PERSON><PERSON> الزواج", "desc": "طل<PERSON> الحصول على منحة الزواج", "gotoMyApplications": "الانتقال لطلباتي", "NotEligible": "غير مؤهل", "tabs": {"title": {"serviceFormTabTitle": "نموذج الخدمة", "husbandPersonalInfoTab": "المعلومات الشخصية للزوج", "wifePersonalInfoTab": "المعلومات الشخصية للزوجة", "abstractEnrollmentInfoTab": "معلومات خلاصة القيد", "statementOfWorkTab": "بيان العمل", "incomeStatementTab": "الدخل وبيان البنك", "attachmentTab": "المرفقات", "summaryTab": "ملخص"}}, "forms": {"husbandPersonalInfo": {"dateOfMarriageContract": "تاريخ عقد الزواج", "shariaCourtName": "اسم المحكمة الشرعية", "NameAr": "الا<PERSON><PERSON> (عربي)", "NameEn": "الاسم (الإنجليزية)", "dob": "تاريخ الميلاد", "emiratesId": "رقم الهوية", "educationLevel": "مستوى التعليم", "email": "الب<PERSON>يد الإلكتروني", "mobile1": "رقم الموبايل", "mobile2": "رقم الموبايل 2"}, "wifePersonalInfo": {"NameAr": "الا<PERSON><PERSON> (عربي)", "NameEn": "الاسم (الإنجليزية)", "dob": "تاريخ الميلاد", "emiratesId": "رقم الهوية", "educationLevel": "مستوى التعليم", "email": "الب<PERSON>يد الإلكتروني", "mobile": "رقم الموبايل"}, "abstractEnrollmentInfo": {"familyBookNumber": "رقم خلاصة القيد", "townNumber": "المدينة", "familyNumber": "رقم العائلة", "dateOfIssuanceFamilyBook": "تاريخ إصدار دفتر العائلة", "placeOfIssuanceFamilyBook": "مكان إصدار خلاصة القيد"}, "statementOfWork": {"employerCategory": "فئة العمل", "employer": "جهة العمل", "placeOfWork": "الإمارة"}, "incomeStatement": {"totalMonthlyIncome": "إجمالي الدخل الشهري", "bankName": "اسم البنك", "iban": "الرقم المصرفي الدولي (IBAN)"}, "attachment": {"incomeStatement": "قوائم الدخل", "realstateEvidence": "وثائق الملكية", "bankLetter": "إفادة برقم الحساب الدولي من البنك (IBAN)", "bankStatement": "كشف حساب مصرفي لآخر 6 شهور من تاريخ التقديم", "salaryCertificate": "شهادة راتب تفصيلية", "salaryCertificateDesc": " تتضمن الأتي: الراتب الأساسي والعلاوات وخصم التقاعد وعلاوة السكن (إن وجدت)", "propertyDocuments": "شهادة بالأملاك", "propertyDocumentsDesc": "للمتقدمون من إمارة الشارقة الرجاء إحضار شهادة الأملاك من التسجيل العقاري ودائرة التخطيط والمساحة", "Other": "مستند داعم آخر"}, "summaryForm": {"consentText": "يرجى الموافقة على الشروط والأحكام", "termConditions": "يمكنك قراءة الشروط والأحكام هنا", "terms": "أقر وأتعهد بأن كافة البيانات والمستندات المقدمة مني لوزارة تمكين المجتمع والخاصة بطلب منحة الزواج صحيحة ومعتمدة وفقاً للاصول، وأتعهد بإخطار الوزارة عند حدوث أية تغيرات في هذه البيانات أو المستندات وذلك خلال مدة لا تتجاوز أسبوعين من حدوث التغيير أو التعديل و لغاية استلامي فعلياً منحة الزواج كاملة، هذا ويحق لوزارة تمكين المجتمع الاتصال والتواصل هاتفياً أو عن طريق البريد الإلكتروني أو مخاطبة جهة العمل أو أي جهة أخرى، قبل وبعد استلامي لمنحة الزواج وذلك لمتابعة حالتي الاجتماعية ، كما أقر بأنني اطلعت على شروط وضوابط استحقاق منحة الزواج واتعهد بالالتزام التام بما ورد فيها. وللوزارة الحق في اتخاذ كافة الاجراءات القانونية التي تكفل لها استرداد المنحة التي صرفت في حال عدم التزامي بما ورد بشروط وضوابط المنحة كاملة، أو في حال عدم تقديم الاقرار الدخول الشرعي أو ما يفيد استمرارية الزواج والدخول بالزوجة خلال المدة المحدد بالشروط والضوابط (سنة ميلادية من تاريخ استلام المنحة)."}}}, "podLostCard": {"title": "خدمة إصدار بطاقة بدل فاقد وتالف لأصحاب الهمم", "Service Form": "نموذج الطلب", "Mobile": "رقم الموبايل", "deliveryAddress": "عنوان التوصيل", "Search Type": "نوع البحث", "Search By": "البحث بواسطة", "Card Number": "رقم البطاقة", "Emirates Id": "الهوية الإماراتية", "Expiry Date": "تاريخ الانتهاء", "Person Name": "اسم الشخص", "Note": " ملاحظة", "No Data Found": "لا توجد بيانات", "NoCardNumberSelected": "لا يوجد بيانات بطاقة مختارة", "Payment Amount": "المبلغ", "PaymentNote": "سيتم إضافة رسوم دفع إضافية من قبل مزود الدفع."}, "npo": {"basicData": {"title": "إشهار مؤسسات النفع العام", "Basic Data": "البيانات الأساسية", "Service Form": "نموذج الطلب", "First Proposed Name (English)": "الاسم المقترح الأول (باللغة الإنجليزية)", "First Proposed Name (Arabic)": "الاسم المقترح الأول (باللغة العربية)", "Second Proposed Name (English)": "الاسم المقترح الثاني (باللغة الإنجليزية)", "Second Proposed Name (Arabic)": "الاسم المقترح الثاني (باللغة العربية)", "Third Proposed Name (English)": "الاسم المقترح الثالث (باللغة الإنجليزية)", "Third Proposed Name (Arabic)": "الاسم المقترح الثالث (باللغة العربية)", "Authority Feedback For Name (English)": "توجيهات الجهة المرخصة للاسم (باللغة الإنجليزية)", "Authority Feedback For Name (Arabic)": "توجيهات الجهة المرخصة للاسم (باللغة العربية)", "Landline Number": "رقم الهاتف الثابت", "PO Box": "صندوق البريد", "Email": "الب<PERSON>يد الإلكتروني", "Website": "الموقع الإلكتروني", "Address": "العنوان", "Geographic Location": "الموقع الجغرافي", "Emirate": "الإمارة", "Licensing Authority": "جهة الترخيص", "Coordinates (X, Y)": "الإحداثيات (X, Y)"}, "objectives": {"Objective (English)": "الهدف (باللغة الإنجليزية)", "Objective (Arabic)": "الهدف (باللغة العربية)", "Means of Achieving Objective (English)": "وسائل تحقيق الهدف (باللغة الإنجليزية)", "Means of Achieving Objective (Arabic)": "وسائل تحقيق الهدف (باللغة العربية)", "Means of Achieving Objective": "وسائل تحقيق الهدف", "Id": "رقم", "Objectives": "الأهداف", "Action": "إجراء"}, "foundingMembers": {"Founding Meeting Data": "بيانات الاجتماع المؤسسي", "Founding Meeting Place": "مكان اجتماع التأسيس", "Founding Meeting Emirate": "إمارة اجتماع التأسيس", "Founding Meeting Date and Time": "التاريخ والوقت لاجتماع التأسيس", "Founding Meeting Agenda": "جدول أعمال اجتماع التأسيس", "EID Number": "رقم الهوية", "Founding Members Number": "عدد الأعضاء المؤسسين", "Full Name in English": "الاسم الكامل باللغة الإنجليزية", "Full Name in Arabic": "الاسم الكامل باللغة العربية", "Preferred Email": "الإيميل المفضل", "Preferred Phone Number": "رقم الهاتف المفضل", "Preferred Mobile Number": "رقم الهاتف المفضل", "Preffered Mobile Number": "الأرقام المفضلة", "Passport Number": "رقم جواز السفر", "Upload Personal Photo": "تحميل صورة شخصية", "Upload Passport Copy": "تحميل نسخة عن جواز السفر", "Nationality": "الجنسية", "Name": "اسم", "EID": "رقم الهوية", "Approval Status": "حالة الموافقة", "Action": "إجراء"}, "interimCommittee": {"Interim Committee Meeting Data": "بيانات اجتماع اللجنة المؤقتة", "Interim Committee Members Meeting Place": "مكان اجتماع اللجنة المؤقتة", "Interim Committee Meeting Emirate": "إمارة اجتماع اللجنة الؤقتة", "Interim Committee Meeting Date and Time": "التاريخ والوقت لاجتماع اللجنة المؤقتة", "Recommendations of the meeting of the Interim Committee": "توصيات اجتماع اللجنة المؤقتة"}, "membership": {"Membership Data": "بيانات العضوية", "More Membership Conditions": "شروط عضوية أخرى", "Membership Fees": "رسوم العضوية", "Membership Fees Due Date Type": "تاريخ استحقاق الاشتراك السنوي", "Joining Fees (if any)": "رسوم الالتحاق (إن وجد)"}, "boardofdirector": {"Board of Directors": "مجلس الإدارة", "Frequency of Board Meetings": "دورية انعقاد اجتماعات مجلس الإدارة", "Number of Board Members": " عدد أعضاء مجلس الإدارة ", "Can Board Members Be Renominated for Another Term?": " هل يجوز إعادة ترشيح أعضاء مجلس الإدارة لفترة أخرى ", "Administrative Positions of the Board": " المناصب الإدارية للمجلس ", "Number of Permissible Terms": "عد<PERSON> الفترات المسموح بها", "Board Numbers (between 5 to 11)": "عدد أعضاء مجلس الإدارة ", "Justification if Board number exceeds 11": "تبرير إذا كان عدد مجلس الإدارة أكثر من 11", "Board Election Cycle (Year)": "دورية انتخاب المجلس", "Allow Member Re-Election?": "هل يجوز إعادة ترشيح أعضاء مجلس الإدارة لفترة أخرى ", "Allowed Periods": "عد<PERSON> الفترات المسموح بها ", "Permanent Advance (AED)": "السلفة المستديمة ", "More Nomination Conditions": "إضافة شروط ترشح أخرى", "Administrative Positions": "المناصب الإدارية للمجلس", "More Administrative Positions": "إضافة مناصب إدارية أخرى", "More Administrative Positions of the Board of Directors": "إضافة مناصب إدارية أخرى لمجلس الإدارة"}, "Conditions that must be met by the founding members": "الشروط الواجب توافرها في الأعضاء المؤسسين", "The number of founders must not be less than 7 member": "ألا يقل عدد المؤسسين عن 7 أعضاء", "The percentage of members residing in the country should not exceed 30% of the total number of founders": "ألا تتجاوز نسبة الأعضاء المقيمين في الدولة عن 30% من إجمالي عدد المؤسسين", "The resident member's period of residence in the country must not be less than three years": "ألا تقل مدة إقامة العضو  المقيم  في الدولة عن ثلاثة سنوات", "That the resident member does not hold diplomatic status": "ألايحمل العضو المقيم الصفة الدبلوماسية", "The member's age must not be less than (21) Gregorian years when elected": "ألا يقل سن العضو عن (21) سنة ميلادية عند انتخابه", "Founding meeting agenda": "جدول أعمال اجتماع التأسيس", "Discussing the establishment of a public benefit institution": "مناقشة إنشاء مؤسسة النفع العام", "Preparing the draft statute": "اعتماد النظام الأساسي", "Election of members of the temporary committee": "انتخاب أعضاء اللجنة المؤقتة", "Conditions that must be met by the interim committee members": "الشروط الواجب توافرها في أعضاء اللجنة المؤقتة", "The number of members of the interim committee shall not be less than 3 members and not exceed 7 members": "عدد أعضاء اللجنة المؤقتة يجب ألا يقل عن 3 أعضاء ولا يزيد عن 7 أعضاء", "Recommendations of the meeting of the Interim Committee": "توصيات اجتماع اللجنة المؤقتة", "Interim Committee meeting agenda": "جدول أعمال اجتماع اللجنة المؤقتة", "Define the administrative positions for the members of the interim committee": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة", "Appoint the commissioner of the interim committee": "تسمية المفوض عن اللجنة المؤقتة", "Membership Conditions": "أنواع و شروط العضوية", "The number of founding members shall not be less than (7) seven members": "ألا يقل عدد الأعضاء المؤسسين عن (7) سبعة أعضاء", "The percentage of the founding members holding the nationality of the state shall not be less than (70%) of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls": "ألا تقل نسبة عدد الأعضاء المؤسسين الحاملين لجنسية الدولة عن (70%) من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفق الضوابط الآتية", "Their number should not exceed 30% of the total number of founding members": "ألا يتجاوز نسبة عددهم عن (30%) من إجمالي عدد الأعضاء المؤسسين", "The member does not hold diplomatic status": "ألا يحمل العضو الصفة الدبلوماسية", "He must have a valid residence permit in the country for a period of no less than (3) three years": "أن تكون له إقامة سارية في الدولة لمدة لا تقل عن (3) ثلاث سنوات", "The founding member must be of the age of majority in accordance with the legislation in force in the country": "أن يكون العضو المؤسس بالغ سن الرشد وفق التشريعات السارية في الدولة", "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated": "أن يكون العضو المؤسس محمود السيرة حسن السمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو في جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره", "Conditions of the members of the Board of Directors": "شروط أعضاء مجلس الإدارة", "The number of members shall not be less than (5) members and not more than (11) members": "ألا يقل عدد الأعضاء عن (5) أعضاء ولا يزيد على (11) عضو", "The number of members holding state nationality should not be less than (70%) of the total number of council members": "ألا يقل عدد الأعضاء الحاملين لجنسية الدولة عن (70%) من اجمالي عدد أعضاء المجلس", "Nomination Conditions for the Board of Directors": "شروط الترشح لعضوية مجلس الإدارة", "Administrative positions of the board members": "المناصب الإدارية لأعضاء مجلس الإدارة", "Chairman of the Board of Directors": "رئيس مجلس الإدارة", "Vice President": "نائب الرئيس", "General Secretary": "أمين السر العام", "Treasurer": "أمين الصندوق"}, "massWedding": {"title": "عرس جماعي", "desc": "تنظيم الأعراس الجماعية التي يتم من خلالها تزويج أعداد من الشباب إلى أعداد مماثلة من الشابات بهدف خفض تكاليف الزواج", "gotoMyApplications": "الانتقال لطلباتي", "NotEligible": "غير مؤهل", "tabs": {"title": {"husbandPersonalInfoTab": "المعلومات الشخصية للزوج", "wifePersonalInfoTab": "المعلومات الشخصية للزوجة", "abstractEnrollmentInfoTab": "معلومات خلاصة القيد", "statementOfWorkTab": "بيان العمل", "incomeStatementTab": "الدخل وبيان البنك", "attachmentTab": "المرفقات", "summaryTab": "ملخص"}}, "forms": {"husbandPersonalInfo": {"dateOfMarriageContract": "تاريخ عقد الزواج", "shariaCourtName": "اسم المحكمة الشرعية", "NameAr": "الا<PERSON><PERSON> (عربي)", "NameEn": "الاسم (الإنجليزية)", "dob": "تاريخ الميلاد", "emiratesId": "هويه الإمارات", "educationLevel": "مستوى التعليم", "email": "بريد إلكتروني", "mobile1": "رقم الموبايل 1", "mobile2": "رقم الموبايل 2"}, "wifePersonalInfo": {"NameAr": "الا<PERSON><PERSON> (عربي)", "NameEn": "الاسم (الإنجليزية)", "dob": "تاريخ الميلاد", "emiratesId": "هويه الإمارات", "educationLevel": "مستوى التعليم", "mobile": "رقم الموبايل", "email": "بريد إلكتروني"}, "abstractEnrollmentInfo": {"familyBookNumber": "رقم خلاصة القيد", "townNumber": "المدينة", "familyNumber": "رقم العائلة", "dateOfIssuanceFamilyBook": "تاريخ إصدار دفتر العائلة", "placeOfIssuanceFamilyBook": "مكان إصدار خلاصة القيد"}, "statementOfWork": {"employerCategory": "فئة صاحب العمل", "employer": "جهة العمل", "placeOfWork": "الامارة"}, "incomeStatement": {"totalMonthlyIncome": "إجمالي الدخل الشهري", "bankName": "اسم البنك", "iban": "الرقم المصرفي الدولي (IBAN)"}, "attachment": {"commercialLicense": "بيان الرخصة التجارية", "monthlyIncomeCertificate": "إثبات الدخل الشهري (شهادة الراتب)", "proofOfResidence": "دليل الإقامة", "ibanLetter": "خطاب رقم الحساب المصرفي الدولي", "bankStatement": "كشف حسا<PERSON> بنكى"}, "summaryForm": {"consentText": "يرجى الموافقة على الشروط والأحكام", "termConditions": "يمكنك قراءة الشروط والأحكام هنا"}}}, "complaint": {"title": "شكوى", "desc": "قدم شكوى بخصوص خدمة حديثة تم استخدامها.", "Service Form": "نموذج الخدمة", "Topic": "الموضوع", "Complaint Service": "الخدمة", "Complaint Sub Service": "الخدمة الفرعية", "Complaint Title": "عنوان الشكوى", "Complaint Details": "تفاصيل الشكوى"}, "inquiry": {"title": "استفسار", "desc": "طلب المعلومات المتعلقة بالخدمة التي تهمك.", "Service Form": "نموذج الخدمة", "Topic": "الموضوع", "Inquiry Service": "الخدمة", "Inquiry Sub Service": "الخدمة الفرعية", "Inquiry Title": "عنوان الاستفسار", "Inquiry Details": "تفاصيل الاستفسار"}, "suggestion": {"title": "اقتراح", "desc": "قدم اقتراح حول كيف يمكننا تحسين تجربتك.", "Service Form": "نموذج الخدمة", "Topic": "الموضوع", "Suggestion Service": "الخدمة", "Suggestion Sub Service": "الخدمة الفرعية", "Suggestion Title": "عنوان الاقتراح", "Suggestion Details": "تفاصيل الاقتراح"}, "thankyou": {"title": "تقديم شكر", "desc": "التعبير عن التقدير تجاه الموظف أو الخدمة المستخدمة.", "Service Form": "نموذج الخدمة", "Topic": "الموضوع", "Thankyou Title": "الموضوع", "Thankyou Service": "الخدمة", "Thankyou Sub Service": "الخدمة الفرعية", "Thankyou Details": "تفاصيل"}, "earlyIntervention": {"title": "رعاية وتأهيل ذوي الإعاقة ' أصحاب الهمم'", "desc": "تتتمثل هذه الخدمة في تسجيل الطفل في مركز التدخل المبكرر للحصول على التدريب والتأهيل اللازم لتمكينه من الدمج في المجتمع", "Child Details": "تفاصيل الطفل", "Guardian Details": "تفاصيل ولي الأمر", "Application ID": "رق<PERSON> الطلب", "Child Emirate ID": "رقم هوية الطفل", "Child Name in Arabic": "اسم الطفل باللغة العربية", "Child Name in English": "اسم الطفل باللغة الإنجليزية", "Date of Birth": "تاريخ الميلاد", "Gender": "النوع", "Center Name": "اسم المر<PERSON>ز", "Emirate": "الإمارة", "Residency Emirate": "إمارة الإقامة", "Guardian Emirate ID": "رقم هوية ولي الأمر", "Guardian Name in Arabic": "اسم ولي الأمر باللغة العربية", "Guardian Name in English": "اسم ولي الأمر باللغة الإنجليزية", "Relationship": "صلة القرابة", "Mobile": "الهات<PERSON> المتحرك", "Email": "الب<PERSON>يد الإلكتروني", "Preferred Mobile": "الهاتف المتحرك المفضل", "Preferred Email": "البريد الإلكتروني المفضل", "Area": "المنطقة", "Address": "العنوان", "Personal Photo": "صورة شخصية", "Passport Copy": "صورة جواز السفر", "Emirate ID": "الهوية الإماراتية", "Nationality": "الجنسية", "Nationality Status": "نوع الجنسية", "Number of Siblings": "ع<PERSON><PERSON> الأشقاء", "Home Languages": "اللغات", "Sibilings Health Status": "حالة صحة الأشقاء", "Mobile 1": "الهاتف 1", "Mobile 2": "الهاتف 2", "Disability Type": "نوع الإعاقة", "Hearing Test Report": "تقرير الاختبار السمعي", "Vision Test Report": "تقرير الاختبار العناية", "Person Type": "تصنيف الجنسية", "Birth Certificate": "شهادة الميلاد", "consentText": "أو<PERSON><PERSON><PERSON> على الشروط والإحكام", "Medical Report": "تقرير الطبي", "Child Name (Arabic)": "اسم الطفل باللغة العربية", "Child Name (English)": "اسم الطفل باللغة الإنجليزية", "Guardian Name (English)": "اسم ولي الأمر باللغة الإنجليزية", "Applicant is not eligible to apply": "عمر الطفل أكثر من خمس سنوات.لم يتم بعد بدء التسجيل في مراكز الرعاية والتأهيل - أصحاب الهمم.سيتم إعلامكم في وقت لاحق", "Siblings Health Status": "حالة صحة الأشقاء", "Guardian Information": "معلومات ولي الأمر", "Guardian Name (Arabic)": "اسم ولي الأمر باللغة العربية", "Select Guardian": "<PERSON><PERSON><PERSON> ولي الأمر", "Guardian": "ولي الأمر", "Guardian Date of Birth": "تاريخ الميلاد", "Guardian Dob": "تاريخ الميلاد", "Terms and Conditions": "الشروط والأحكام", "terms": {"1": "أن يكون من الأطفال المتأخرين نمائياً , ذو الإعاقة ، المعرضين لخطر التأخر النمائي ، العمر: من 0 إلى 5 سنوات ، للمواطنين وأبناء المواطنات", "2": "أن تكون جميع الأوراق الثبوتيه للطفل سارية المفعول (الجواز، الهوية، الجنسيه، صوره شخصيه حديثه)", "3": "صورة عن التقرير الطبي (إن وجد)", "4": "صورة عن بطاقة ذوي الاعاقة (أصحا<PERSON> الهمم) صادرة من الوزارة (إن وجد)", "5": "في حال عدم توفر أحد الشروط السابقه لا يتم تسجيل الحاله ويتم توجيه ولي الأمر لمراجعتنا بعد استيفاء جميع الشروط والمتطلبات"}}, "podCenter": {"title": "التسجيل في مركز رعاية وتأهيل أصحاب الهمم", "desc": "تتمثل هذه الخدمة في تسجيل الطفل في مراكز رعاية و تأهيل أصحاب الهمم للحصول على التدريب والتأهيل اللازم لتمكينه من الدمج في المجتمع", "Child Details": "تفاصيل الطفل", "Application ID": "رق<PERSON> الطلب", "Child Emirate ID": "رقم هوية الطفل", "PoD Card Number": "رقم بطاقة أصحاب الهمم", "Child Name in Arabic": "اسم الطفل باللغة العربية", "Child Name in English": "اسم الطفل باللغة الإنجليزية", "Date of Birth": "تاريخ الميلاد", "Gender": "الجنس", "Disability Type": "نوع الإعاقة", "Center Name": "اسم المر<PERSON>ز", "Emirate": "الإمارة", "Guardian Details": "تفاصيل ولي الأمر", "Guardian Emirate ID": "رقم هوية ولي الأمر", "Guardian Date of Birth": "تاريخ الميلاد", "Guardian Name in Arabic": "اسم ولي الأمر باللغة العربية", "Guardian Name in English": "اسم ولي الأمر باللغة الإنجليزية", "Relationship": "صلة القرابة", "Mobile": "الهات<PERSON> المتحرك", "Email": "الب<PERSON>يد الإلكتروني", "Preferred Mobile": "الهاتف المتحرك المفضل", "Preferred Email": "البريد الإلكتروني المفضل", "Area": "المنطقة", "Address": "العنوان", "Attachments": "المرفقات", "Personal Photo": "صورة شخصية", "Passport Copy": "صورة جواز السفر", "Emirate ID": "الهوية الإماراتية", "Medical Report": "التقرير الطبي", "Submit": "تقديم", "Terms and Conditions": "الشروط والأحكام", "consentText": "أو<PERSON><PERSON><PERSON> على الشروط والأحكام", "Location Information": "معلومات الموقع", "Siblings Number": "<PERSON><PERSON><PERSON> الاختيارين"}, "swp": {"title": "تقديم طلب لبرنامج الدعم الاجتماعي", "desc": "هو برنامج دعم حكومي يهدف إلى تعزيز جودة حياة الأفراد والأسر الإماراتية من ذوي الدخل المنخفض، حيث يوفر البرنامج مخصصات جديدة لدعم المستحقين تشمل مجموعة من العلاوات، وتصرف وفق الشروط والأحكام التي تُحدد حسب الحالة الاجتماعية.", "Service Form": "Service Form", "NotEligible": "غير مؤهل", "applyForHousingAllowance": "التقدم لعلاوة السكن", "applyForAcademicExcellenceAllowance": "التقدم لعلاوة التفوق الدراسي للتعليم العالي", "applyForHousingEducationTopup": "التقدم للعلاوتين: السكن والتفوق الدراسي للتعليم العالي", "forms": {"Non-ResidentialError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه لكونه غير سكني. الرجاء إدخال رقم حساب الخدمات آخر، أو المتابعة إلى القسم التالي دون تقديم طلب للحصول على علاوة بدل الكهرباء والمياه", "Non-EmiratiError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه لكونه لغير مواطن. الرجاء إدخال رقم حساب الخدمات آخر، أو المتابعة إلى القسم التالي دون تقديم طلب للحصول على علاوة بدل الكهرباء والمياه", "ReceivingUtilityAidError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه لحصوله على دعم آخر. الرجاء إدخال رقم حساب الخدمات آخر، أو المتابعة إلى القسم التالي دون تقديم طلب للحصول على علاوة بدل الكهرباء والمياه", "InactiveError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه لكونه غير فعال. الرجاء إدخال رقم حساب الخدمات آخر، أو المتابعة إلى القسم التالي دون تقديم طلب للحصول على علاوة بدل الكهرباء والمياه", "utlityAllowanceError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه. الرجاء إدخال رقم حساب الخدمات آخر، أو المتابعة إلى القسم التالي", "accomadations": "السكن", "actions": "تحديث", "Action": "الإجراءات", "additionalAttachments": "وثائق إضافية", "additionalDocuments": "وثائق إضافية", "additionalDocumentsSubtext": "يرجى تحميل وثائق داعمة إضافية مع طلبك، يمكنك القيام بذلك أدناه.", "addMoreIncome": "أ<PERSON><PERSON> مص<PERSON>ر دخل أخرى", "addPension": "أض<PERSON> المعا<PERSON> التقاعدي", "addRentalIncome": "أض<PERSON> د<PERSON>ل الإيجار", "address": "العنوان (المنطقة ، رقم الشارع ، رقم المنزل)", "addTradeLicense": "أضف رخصة تجارية", "AlternativeEmail": "البريد الإلكتروني المفضل", "alternativeNumber": "رقم الهاتف المفضل", "area": "المنطقة", "attachedDocuments": "المستندات المرفقة", "attachedDocumentsFarmer": "مستندات مرفقة", "attachedDocumentsSubtext": "قم بتحميل المستندات المطلوبة أدناه", "beneficiaryEducationTooltip": "يُرجى تحديد أعلى شهادة أو مؤهل علمي حصلت عليه", "caseID": "رقم الحالة", "caseReason": "سب<PERSON> الحالة", "center": "المركز التابع له", "comment": "الاقتراح / الملاحظة", "companyName": "جهة العمل", "complaintDetails": "ير<PERSON>ى ادخال تفاصيل ال{{inquiryType}}", "complaintSuccessBody": "شكرا لك على تقديم {{inquiryType}}.يمكنك التحقق من حالة ال{{inquiryType}} في أي وقت من خلال صفحة الأستفسارات / الاقتراحات، سنقوم بالتواصل معك لاحقاً في حال احتجنا اي معلومات اضافية", "complaintSuccessBodyForAnonymous": "نشكرك على تقديم {{inquiryType}}. سنتصل بك لاحقًا إذا كنا بحاجة إلى أي معلومات.", "complaintSuccessCaseNumberTitle": "رقم ال{{inquiryType}}", "complaintSuccessDateTitle": "تم التقديم", "complaintSuccessTitle": "تم تقديم {{inquiryType}} بنجاح وهي قيد المراجعة", "complaintTitle": "عنوان ال{{inquiryType}}", "complaintType": "نوع ال{{inquiryType}}", "complete": "مكتمل", "ContractEndDate": "تاريخ نهاية العقد", "ContractNo": "رقم العقد", "ContractStartDate": "تاريخ بداية العقد", "countryOfBirth": "مكان الولادة", "dateOfBirth": "تاريخ الولادة", "deleteCantBeUndone": "لا يمكنك التراجع عن هذا الإجراء.", "deleteFamilyMember": "حذف فرد من العائلة", "deleteFamilyMemberText": "هل أنت متأكد أنك تريد حذف عضو العائلة؟", "deleteIncome": "إلغاء مصدر الدخل", "deleteIncomeText": "هل أنت متأكد أنك تريد إلغاء الدخل الإضافي؟", "deletePension": "إلغاء المعاش التقاعدي", "deletePensionText": "هل أنت متأكد أنك تريد إلغاء المعاش التقاعدي الاضافي؟", "deleteRentalIncome": "إلغاء دخل الإيجار", "deleteRentalIncomeText": "هل أنت متأكد أنك تريد إلغاء دخل الإيجار الإضافي؟", "deleteTradeLicense": "إلغاء الرخصة التجارية", "deleteTradeLicenseText": "هل أنت متأكد أنك تريد إلغاء الرخصة التجارية الاضافية؟", "deletingFile": "<PERSON><PERSON><PERSON> الملف", "docd-ad": "هيئة أبوظبي للدعم الاجتماعي", "DescriptionLimitationMsg": "يجب أن يكون الوصف 700 حرفًا على الأكثر", "docd-dubait": "هيئة تنمية المجتمع - دبي", "InvalidNumberOfChildren": "يجب أن تكون القيمة أكبر من صفر أو اكبر من أو تساوي عدد المعايير المحددة", "PleaseEnterNumbergraterThanZero": "الرجاء إدخال رقم أكبر من الصفر", "documents": "المستندات", "doesFamilyMemberContribute": "هل يساهم هذا الفرد في دخل الأسرة؟", "doesFamilyMemberContributeTooltip": "دخل العمل ، بما في ذلك المدفوعات غير المنتظمة مثل العمولات والمكافآت وما إلى ذلك. يرجى إضافة مصادر دخل إضافية بما في ذلك الدخل من التوظيف الذاتي / العمل الحر / اتفاقيات العقود ، بما في ذلك المدفوعات غير المنتظمة مثل الدخل من المشاريع المستقلة ، إلخ.", "doesFamilyMemberPension": "هل يحصل هذا الفرد من الأسرة على تقاعد أو معاش؟", "doesFamilyMemberPensionTooltip": "الدخل من أنظمة المعاشات التقاعدية (العامة) لجميع أفراد الأسرة بما في ذلك رب الأسرة والمساهمين (مثل الزوجات)", "doesFamilyMemberRental": "هل هذا الفرد من الأسرة لديه دخل إيجار؟", "doesFamilyTradeLicense": "هل هذا الفرد من العائلة لديه رخصة تجارية؟", "dropFile": "أو اسحب الملفات", "editFamilyMembersInformation": "تعديل معلومات أفراد العائلة", "education": "التعليم", "Educations": "المؤهل العلمي", "Emirate": "الإمارة", "Emirates": "الإمارة", "emiratesID": "رقم بطاقة الهوية الإماراتية", "EmiratesID": "رقم بطاقة الهوية الإماراتية", "endDateCantStartBeforeStartDate": "تاريخ النهاية يجب ان يكون بعد تاريخ البداية.", "EnterEmirateIdForEWE": "يرجى ادخال رقم الهوية الإماراتية المسجلة برقم حساب المزرعة من شركة الاتحاد للماء وكهرباء", "enterRequestDetails": "أدخل تفاصيل الطلب", "EntityReceivedFrom": "من أي جهة؟", "EWEBill": "رقم فاتورة من شركة الاتحاد للماء والكهرباء", "eweErrorMessage": "يرجى إدخال رقم فاتورة صحيح المكون من ١٢ رقماً", "familyMemberInformation": "معلومات أفراد الأسرة", "familyMembersInformation": "المعلومات المتعلقة بأفراد الأسرة", "housingInformation": "علاوة بدل السكن", "systemValidation": "التحقق من النظام", "documentGeneration": "إنشاء المستندات", "inflationInformation": "معلومات التضخم", "educationInformation": "علاوة بدل التفوق الدراسي", "familyMembersInformationFarmer": "معلومات أفراد الأسرة", "familyMembersInformationSubtext": "يرجى إدخال جميع المعلومات المطلوبة أدناه. جميع البيانات التي تحمل علامة النجمة (*) إلزامية.", "farmerAidInformation": "المعلومات المتعلقة بدعم أصحاب المزارع", "farmerAidRquestAddedBody2": "طلبك قيد المراجعة سيتم التواصل معك قريباً.", "farmerRequestAdded": "تم تقديم طلبك بنجاح", "filesType": "{{extensions}} نوع الملفات", "fileUploadErrorFileSize": "يرجى تحميل ملف بحجم أقل من {{size}}", "fileUploadErrorFileTypes": "يرجى تحميل ملف بالامتدادات التالية: ‎{{extensions}}", "firstName": "الاسم الأول", "FirstName": "الاسم الأول", "Fullname": "الاسم الكامل", "FullName": "الاسم الكامل", "gender": "الجنس", "healthCardInsurance": "تأمين البطاقة الصحية ساري المفعول", "householdHeadContributes": "هل لدى رب الأسرة دخل مالي من العمل؟", "householdHeadContributesTooltip": "دخل العمل، ويشمل ما تستلمه كمدفوعات مالية غير منتظمة كالعمولات والمكآفات، الخ. يُرجى إضافة مصادر الدخل الإضافي والتي تشمل الدخل من المهن الحرة والعمل الحر (الفريلانس) واتفاقيات التعاقد التي تشمل المدفوعات غير المنتظمة كدخل تستلمه نظير العمل في المشاريع بنظام التعاقد الحر، الخ.", "householdHeadPension": "هل يستلم رب الأسرة دخلاً كمعاش تقاعدي؟", "householdHeadPensionTooltip": "الدخل من أنظمة معاشات التقاعد لجميع أفراد الأسرة بما في ذلك رب الأسرة والمساهمين (مثل الزوجات)", "householdHeadTradeLicense": "هل لدى رب الأسرة رخصة تجارية؟", "householdRentalIncomes": "هل لدى رب الأسرة دخل من إيجار؟", "HowToKnowEWE": "كيفية معرفة رقم الحساب من شركة الاتحاد للماء والكهرباء.", "IDNBackNumber": "رقم بطاقة الهوية الإماراتية (الموجود خلف البطاقة)", "income": "الدخل", "incomeAmount": "قيمة الدخل الشهري (درهم إماراتي)", "Income": "قيمة الدخل الشهري (درهم إماراتي)", "incomeAmountToolTip": "يرجى تحديد مبلغ الدخل لجميع أفراد الأسرة بما في ذلك رب الأسرة والمساهمين (مثل الزوجات)", "incomeInformation": "المعلومات المتعلقة بالدخل", "incomeInformationFarmer": "معلومات الدخل", "incomeInformationSubtext": "يرجى إدخال جميع المعلومات المطلوبة أدناه. جميع البيانات التي تحمل علامة النجمة (*) إلزامية.", "incomeSource": "مصدر الدخل", "IncomeSource": "مصدر الدخل", "IncomeSourceText": "مصدر الدخل", "incomeSourceToolTip": "يرجى تحديد نوع مصدر الدخل لجميع أفراد الأسرة بما في ذلك رب الأسرة والمساهمين (مثل الزوجات)", "IncomeTypes": "مصدر الدخل", "informationRequired": "المعلومات مطلوبة", "isRequired": "{{fieldName}} هو حقل إلزامي.", "PleaseEntera1or2-digit": "الرجاء إدخال رقم مكون من خانة او خانتين", "PleaseEnteraPositivenumber": " هذه الخانة يجب أن تحتوي على رقم صحيح أكبر من أو يساوي 1", "PleaseEnteranIntegerNumber": "الرجاء إدخال رقم صحيح", "isIntegerAndhasOneOrTwoDigits": "هذه الخانة يجب أن تحتوي على رقم صحيح فقط", "NumberofPoDSiblings": "يجب أن يكون عدد الأشقاء من اصحاب الهمم أقل من أو يساوي عدد الأشقاء", "NumberofPoDChildren": "يجب أن يكون عدد الأطفال من اصحاب الهمم أقل من أو يساوي عدد الأطفال الإماراتيين", "PleaseEnterNumberBiggerThanZero": "الرجاء يدخل رقم أكبر من الصفر", "ThisFieldShouldbeNumber": "هذه الخانة يجب أن تحتوي على رقم صحيح فقط", "lessThanOrEqual4": " يجب ان يكون هذا الرقم اقل من او يساوي 4 ", "numberOfPODSpouses": "عد<PERSON> الزوجات من اصحاب الهمم", "numberOfChildren": "ع<PERSON><PERSON> الأط<PERSON>ال الاماراتيين", "totalIncome": "إجمالي الدخل", "numberOfSpouses": "<PERSON><PERSON><PERSON> الزوجات", "jobTitle": "المسمى الوظيفي", "lastName": "اسم العائلة", "LastName": "اسم العائلة", "last": "الاسم الكامل", "localNumberFormatSubtext": "رقم هاتف إم<PERSON>اتي", "MaritalStatus": "الحالة الاجتماعية", "maxSize": "الح<PERSON> الأقصى {{size}}", "memberName": "اسم الفرد", "mocd": "وزارة تمكين المجتمع", "mustBeNumber": "هذا الحقل يجب ان يكون رقماً.", "mustBePositive": "عُذراً، يجب أن تكون قيمة المبلغ أكبر من 0.", "next": "التالي", "noDocumentsUpload": "لا توجد مستندات مطلوبة ، يرجى المتابعة إلى الخطوة التالية.", "noEWENoFarmerDesc": " هذه الخدمة مخصصة للمسجلين في شركة الاتحاد للماء و الكهرباء", "noEWENoFarmerTitle": "لا يمكنك التقديم لهذه الخدمة", "noFamilyMembersData": "لا توجد بيانات متاحة لأفراد العائلة ، يرجى المتابعة إلى الخطوة التالية.", "noEducationMembersData": "لا يوجدأبناء فوق ال ١٦ عام ، يرجى المتابعة إلى الخطوة التالية.", "numberOfHousehold": "<PERSON><PERSON><PERSON> أفراد الأسرة", "Occupations": "الحالة الوظيفية", "ownerEWEBill": "هل أنت مالك حساب المزرعة من شركة الاتحاد للماء والكهرباء", "passportCopy": "صورة عن جواز السفر", "passportNo": "رقم جواز السفر.", "PassportNumber": "رقم جواز السفر", "pension": "معاش التقاعد", "pensionAmount": "مبلغ معاش التقاعد الشهري (بالدرهم الإماراتي)", "PensiontAuthority": "هيئة التقاعد", "PensiontAuthorityText": "هيئة التقاعد", "PensionType": "نوع معاش التقاعد", "PensionAuthority": "هيئة المعاشات التقاعدية", "personalDocuments": "مستندات شخصية", "personalInformation": "معلومات شخصية", "personalInformationSubtext": "يرجى إدخال جميع المعلومات المطلوبة أدناه. جميع البيانات التي تحمل علامة النجمة (*) إلزامية.", "phoneNumber": "رقم الهاتف", "PleaseEnterEWENumber": "ير<PERSON>ى إدخال رقم حساب المزرعة من شركة الاتحاد للماء والكهرباء", "pleaseEnterValidEid": "يرجى ادخال رقم هوية إماراتية فعالة", "PreferredEmail": "الب<PERSON>يد الإلكتروني", "PreferredEmailUsed": " البريد الإلكتروني (سيتم إرسال الشهادة إلى البريد الالكتروني أدناه)", "PreferredPhoneNumber": "رقم الهاتف", "ReceiveInflationAllowance": "هل تتلقى علاوة تضخم؟", "ReceiveSocialAid": "هل تتلقى مساعدات اجتماعية؟", "RegisteredWithEWE": "هل أنت مسجل لدى شركة الاتحاد للماء والكهرباء؟", "RelatedEmiratesID": "رقم بطاقة الهوية الإماراتية المسجل برقم الحساب من شركة الاتحاد مياه وكهرباء", "relationship": "العلاقة", "RentalIncome": "دخل الإيجار", "RentalIncomes": "دخل الإيجار", "rentalSource": "مصدر الإيجار", "RentalSource": "مصدر الإيجار", "RentalSourceText": "مصدر الإيجار", "RentAmount": "مقدار الإيجار الشهري (درهم إماراتي)", "requestAddedBody1": "شكرا لك على تقديم طلب للحصول على خدمة المساعدة الاجتماعية.", "requestAddedBody1Inflation": "شكرا لك على تقديم طلب للحصول على برنامج التضخم الاقتصادي .", "requestAddedBody1Farmer": " شكراً على تقديم طلب الحصول على دعم استهلاك الكهرباء لمالكي المزارع. ", "requestAddedBody2": "تم تقديم طلبك للخدمة وهو قيد المراجعة. ستتواصل وزارة تمكين المجتمع قريبا", "requestAddedTitle": "تم تقديم طلبك بنجاح وهو قيد المراجعة", "requestEditedBody1": "شكرا لك على تعديل طلب للحصول على خدمة المساعدة الاجتماعية.", "requestEditedBody1Farmer": "شكرا لك على تعديل طلب للحصول على دعم أصحاب المزارع.", "requestEditedBody2": "تم تعديل طلبك للخدمة وهو قيد المراجعة. ستتواصل وزارة تمكين المجتمع قريبا", "requestEditedTitle": "تم تعديل طلبك بنجاح وهو قيد المراجعة", "requestNumber": "رق<PERSON> الطلب", "requestSubmitted": "تم تقديم الطلب", "reviewDetails": "مراجعة البيانات", "reviewDetailsFarmer": "مراجعة التفاصيل", "reviewDetailsSubtext": "يرجى مراجعة المعلومات والمتابعة كما هو مطلوب.", "selectFiles": "اختر الملف", "socialAidInformation": "المعلومات المتعلقة بالدعم الاجتماعي", "socialAidInformationFarmer": "معلومات المساعدة الاجتماعية", "socialAidInformationSubtext": "يرجى إدخال جميع المعلومات المطلوبة أدناه. جميع البيانات التي تحمل علامة النجمة (*) إلزامية.", "socialServices-sharjah": "دائرة الخدمات الاجتماعية - الشارقة", "status": "الحالة", "StatusCode": "الحالة", "submitRequest": "تقديم الطلب", "submittedOn": "Submitted On", "thankYouForFeedback": "شكرًا لك على ملاحظاتك.", "thisField": "هذا الحقل", "tradeLicense": "الرخصة التجارية", "tradeLicenseAmount": "مبلغ الرخصة التجارية الشهري (بالدرهم الإماراتي)", "TradeSourceText": "مصدر الرخصة", "uaeMobileNumberError": "ير<PERSON>ى إدخال رقم هاتف بالصيغة التالية ‎05XXXXXXXX", "uaeIDNumberError": "الرجاء إدخال رقم إماراتي صحيح", "uaeResidenceVisa": "تأشيرة الإقامة الإماراتية", "universityDegree": "شهادة معتمدة من الجامعة", "uploadingFile": "تحميل الملف", "useLengthError": "الرجاء ادخال رقم مكون من ١٢", "validWorkContractFamily": "عقد عمل ساري المفعول", "wrongEmailAddress": "عُذراً، يُرجى إدخال بريد إلكتروني صحيح.", "Area": "المنطقة", "Category": "السبب الرئيسي لطلب المساعدة", "SubCategory": "السبب الفرعي", "EmiratesResd": "إمارة السكن", "Center": "المركز التابع له", "youHaveToReadTerms": "يجب عليك قراءة الإقرار والتعهد", "addMoreDocs": "إضافة مستندات أُخرى", "addingMoreDocuments": "إضافة مستندات أُخرى", "IsDraftedinMilitaryService": "تجند في الخدمة العسكرية منذ أن بلغ من العمر 21 عامًا", "IsPursuingHigherEducation": "متابعة التعليم العالي (أعلى من الثانوية) منذ بلوغ سن 21 عامًا", "IsActiveStudent": "هل أنت مستمر بالدراسة ؟", "MilitaryServiceStatus": "الخدمة الوطنية", "Terminated": "الوظيفة السابقة", "jobseekerErrorMsg": "هذه المساعدة مؤقتة لمدة ستة أشهر– وبإمكانك التقدم لها مرتان فقط خلال الـ خمس سنوات القادمة", "notApplicableErrorMsg": "لا يمكنك تحديد قيمة أخرى مع خيار غير قابلة للتطبيق", "feedbackRecived": "تم إستلام ملاحظتك، شكراً لك.", "swfProgram": "برنامج الدعم الاجتماعي", "InflationProgram": "برنامج التضخم الاقتصادي ", "HaveChildrenCustody": "هل لديك حضانه لأطفالك ؟", "ReceivedLocalSupport": "هل تتلقى أنت أو زوجتك أي دعم اجتماعي محلي آخر؟", "guardianIncome": "دخل ولي الأمر (الدرهم الإماراتي)", "PursuingHigherEducation": "هل تابعت التعليم العالي منذ أن بلغت 21 عامًا؟", "PursuingMilitaryService": "هل تابعت الخدمة العسكرية منذ أن كان عمرك 21 عامًا؟", "GuardianEmiratesID": "رقم بطاقة الهوية الإماراتية للوصي", "PensiontAmount": "مبلغ المعاش الشهري (بالدرهم)", "noDataFound": "لايوجد بيانات", "theService": "الخدمة", "completeInfo": "اكمل المعلومات", "selectDocType": "اختر نوع الملف الذي تريد تحميله", "previous": "السابق", "Topic": "الموضوع", "FamilyHousholdName": "أسم رب الأسرة", "khulasitQaidNumber": "رقم خلاصة القيد", "ChildEligibilityforWomeninDifficulty": "هل لديك حضانة طفل واحد على الأقل يستوفي المعايير التالية؟", "select": "ير<PERSON>ى التحديد إن أمكن", "isRequiredField": "هذا حقل إلزامي.", "NumberOfChildren": "كم عدد الأطفال الذين يستوفون المعايير المذكورة أعلاه؟", "NumberOfChildrenLessThan25": "كم عدد الأطفال الذين أعمارهم أقل من 25؟", "complaintServices": "خدمة", "complaintSubServices": "خدمة فرعية", "generatingFamilyBook": "يرجى الانتظار… يتم استرجاع خلاصة القيد", "thisFieldshouldbeLess": "يجب أن تكون القيمه أكبر من صفر أو أقل أو تساوي عدد الأطفال الذين يستوفون الشروط أعلاه", "greaterThanZero": "يجب أن تكون القيمه أكبر من صفر", "localSupText": "(من أي من هذه الجهات التالية: هيئة أبوظبي للدعم الاجتماعي، هيئة تنمية المجتمع، دائرة الخدمات الاجتماعية بالشارقة)", "localSupText2": "(برنامج الشيخ زايد للإسكان، هيئة أبوظبي للإسكان، مؤسسة محمد بن راشد للإسكان، دائرة الإسكان في الشارقة)", "Verified": "تم التحقق", "PendingVerification": "في انتظار التحقق", "complaintHeader": "عنوان الشكوى", "inquiryHeader": "عنوان الاستفسار", "suggestionHeader": "عنوان الاقتراح", "thankYouTitleHeader": "عنوان الشكر", "thankYouTitle": "شكر", "numberOfSiblingsError": "الرجاء إدخال رقم مكون من رقمين لعدد الأشقاء", "ApplyHousingAllowance": "هل ترغب في تقديم طلب للحصول على علاوة بدل السكن؟", "IsHouseholOwnerResidentialProperty": "<PERSON><PERSON><PERSON> أفراد الأسرة هو المالك الوحيد للعقار السكني المبني", "ReceivingFederalLocalhousingsupport": "هل تتلقى أنت أو أي من أفراد أسرتك دعم سكني من أي من البرامج الاتحادية أو المحلية التالية؟", "ReceivingHousingAllowanceFromEmployer": "هل تحصل أنت أو أي من أفراد أسرتك العاملين على علاوات بدل السكن من رب العمل؟ (لا تشمل زيادة بدل السكن التي تكون جزء من الراتب الشهري)", "IsUtilityBillIssuedForFullyOwnedProperty": "هل تم إصدار فاتورة خدمات (ماء/كهرباء) للعقار السكني المملوك بالكامل؟", "FullOwnershipResidentialProperty": "هل تملك أنت أم أي من افراد الأسرة عقارات سكنية بشكل كلي أو جزئي؟", "LivingSituation": "ما هو وضع المعيشة الخاص بك؟", "ApplyEducationAllowance": "هل تريد التقدم بطلب للحصول على علاوة التفوق الدراسي لهذا الابن/الشقيق؟", "childCompletedSemesterInUniversity": "هل أكمل هذا الابن/الشقيق أكثر من فصل دراسي واحد في جامعة معتمدة؟", "highSchoolCurriculuim": "يرجى تحديد منهاج التعليم الثانوي", "enrolledEducationStream": "ما هو مسار التعليم العام الذي التحق به الطفل/الأخ؟", "EmSATorAdvancedPlacementScores": "هل ترغب في تحميل درجات اختبار (EmSAT) أو تحديد المستوى المتقدم؟", "Age": "العمر", "ApplyEducation": "تقدم بطلب للحصول على علاوة التفوق الدراسي", "applyedtrue": "نعم", "applyedfalse": "لا", "ReceivesHousingSupportFromHusband": "هل تحصلين على دعم سكني من طليقكِ بموجب حكم المحكمة؟", "ApplyInflationAllowance": "هل ترغب في تقديم طلب للحصول على علاوة بدل التضخم؟", "ApplyUtilityAllowance": "هل ترغب في تقديم طلب للحصول على علاوة بدل الكهرباء والمياه أيضًا؟", "UtilityProvider": "يرجى تحديد شركة الكهرباء/المياه", "UtilityAccountNumber": "ير<PERSON>ى إدخال رقم حساب الخدمات الذي ترغب في حصوله على بدل علاوة الكهرباء والمياه", "IsEnrolledInNationalService": "هل الطفل ملتحق حالياً بالخدمة الوطنية؟", "complaint": "شكوى", "inquiry": "استفسار", "suggestion": "اقتراح", "informationForm": "سب<PERSON> التقديم", "InflationCategory": "سب<PERSON> التقديم", "discliamer": " يمكن تقديم الطلب للحصول على علاوة التفوق الدراسي عند إتمام الخدمة الوطنية ", "disabledFieldMessage": "يمكنك تحديث الهاتف المفضل أو البريد الإلكتروني المفضل ضمن ملفي الشخصي", "womanOver45": "يرجى إرفاق هويات المحضونين وإثبات الحضانة إن وجد"}}, "npoLicenseDeclaration": {"title": "طلب إنشاء مؤسسات النفع العام", "desc": "طلب إنشاء مؤسسات النفع العام", "titleByDecree": "تسجيل مؤسسة نفع عام", "descByDecree": "تسجيل مؤسسة نفع عام", "forms": {"legalTypePage": {"title": "طلب إنشاء مؤسسات النفع العام", "titleByDecree": "طلب تسجيل مؤسسات النفع العام", "selectNpoLegalForm": "اختر النموذج القانوني لمؤسسة النفع العام", "description": "باعتبارك ممثلاً لمؤسسة نفع عام، يمكنك الآن التقدم بطلب إنشاء ممؤسسة نفع عام من أجل الحصول على تأكيد الأعضاء المؤسسين لمؤسسة النفع العام حتى أتمكن من تقديم طلب ترخيص وإعلان مؤسسة النفع العام إلى وزارة التنمية الاجتماعية للموافقة عليه."}, "npoLegalForm": {"title": "الشكل القانوني"}, "basicInformation": {"title": "المعلومات الاساسية", "npoLegalForm": "الشكل القانوني", "localDecreeLawNumber": "رقم المرسوم/القانون المحلي", "issuanceDate": "تاريخ الإصدار", "associationClassification": "تصنيف الجمعية", "localDecreeLawCopy": "نسخة القانون المحلي/المرسوم", "proposedNameEn": "الاسم المقترح (انجليزي)", "proposedNameAr": "الاسم المقترح (عربي)", "NameEn": "الاسم (الإنجليزية)", "NameAr": "الا<PERSON><PERSON> (عربي)", "addProposedName": "إضافة اسم مقترح", "proposedNamesList": "قائمة الأسماء المقترحة", "landlineNumber": "رقم الهاتف الثابت", "poBox": "صندوق البريد", "faxNumber": "رقم الفاكس", "email": "الب<PERSON>يد الإلكتروني", "website": "الموقع الإلكتروني", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "emirate": "الإمارة", "logo": "الشعار", "permanentAdvance": "السلفة المستديمة (درهم)", "fundsAllocationAmount": "قيمة الأموال المخصصة للمؤسسة", "add": "إضافة", "id": "رقم", "actions": "الإجراءات", "edit": "تعديل", "clone": "نسخ", "remove": "<PERSON><PERSON><PERSON>", "npoName": "اسم مؤسسة النفع العام", "npoContactDetails": "تفاصيل الاتصال بمؤسسة النفع العام", "fundsAllocation": "تخصيص الأموال", "status": "الحاله", "threeProposedNames": "3 اسماء مقترحة", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "<PERSON><PERSON><PERSON> حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "تاريخ (الاقدم)", "Date (Newest)": "تاريخ (الاحد<PERSON>)", "help": "المساعدة", "helpMessage": "لكي تتمكن من تقديم الطلب يجب عليك إضافة اسمين مقترحين وبحد أقصى ثلاثة أسماء", "targetGroupsAssociation": "الفئات المستهدفة من أنشطة الجمعية", "targetGroupsNationalSociety": "الفئات المستهدفة من أنشطة الجمعية", "addTargetGroup": "إضافة فئة مستهدفة", "targetGroupNameEn": "اسم الفئة المستهدفة (إنجليزي)", "targetGroupNameAr": "اسم الفئة المستهدفة (عربي)", "fundbelongsDetails": "بيانات الجهة التي يتبع لها الصندوق", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (بالإنجليزية)", "EntityNameAr": "اسم الجهة (بالعربية)", "EntityType": " تصنيف الجهة ", "NumberOfEmployees": "<PERSON><PERSON><PERSON> الموظفين", "confirm": "تأكيد الاسم المقترح", "ExamplesOfActivitiesAndProgramsOfferedByTheNPO": "أمثلة على الأنشطة والبرامج التي تقدمها مؤسسة النفع العام", "exampleOfActivitiesAr": "عنوان النشاط/البرنامج (عربي)", "exampleOfActivitiesEn": "عنوان النشاط/البرنامج (إنجليزي)", "activitiesAndPrograms": "أمثلة على الأنشطة والبرامج التي تقدمها مؤسسة النفع العام"}, "objectives": {"ObjectiveEn": "الهدف (بالإنجليزية)", "ObjectiveAr": "الهدف (بالعربية)", "FundServiceObjectiveEn": "أنشطة وأهداف الصندوق (بالانجليزية)", "FundServiceObjectiveAr": "أنشطة وأهداف الصندوق (بالعربية)", "MeansOfAchievingObjectiveEn": "وسائل تحقيق الهدف (بالإنجليزية)", "MeansOfAchievingObjectiveAr": "وسائل تحقيق الهدف (بالعربية)", "MeansOfAchievingObjective": "وسائل تحقيق الهدف", "Id": "رقم", "Title": "الأهداف", "FundServiceTitle": "أنشطة وأهداف الصندوق", "Actions": "الإجراءات", "editObjective": "تعديل", "cloneObjective": "نسخ", "removeObjective": "<PERSON><PERSON><PERSON>", "npoObjectives": "أهداف مؤسسة النفع العام", "objectives": "أهداف", "fundServiceObjectives": "أنشطة وأهداف الصندوق", "fundServiceAddObjective": "إضافة نشاط/هدف", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "<PERSON><PERSON><PERSON> حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "تاريخ (الاقدم)", "Date (Newest)": "تاريخ (الاحد<PERSON>)", "helpMessage": "لكي تتمكن من تقديم الطلب يجب عليك إضافة خمسة أهداف على الأقل", "helpMessageByDecree": "لكي تتمكن من تقديم الطلب يجب عليك إضافة هدف على الأقل", "status": "الحالة"}, "foundingMembers": {"title": "الأعضاء المؤسسون", "foundingMembersLessThan70Percent": "الأعضاء المؤسسون الذين يحملون جنسية الدولة أقل من (70%)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "exceptionReasonAr": "سبب الاستثناء (عربي)", "foundingMembersLessThanSeven": "يقل عدد الأعضاء المؤسسين عن (7) سبعة أعضاء", "foundingMembersLessThanSevenHint": "يجب عليك إضافة ثلاثة (3) أعضاء مؤسسين على الأقل للمتابعة", "numberOfFounders": "عدد المؤسسين", "founderEmiratesID": "رقم الهوية", "founderDateOfBirth": "تاريخ الميلاد", "addFoundingMember": "إضافة عضو مؤسس", "FoundingMember": "الأعضاء المؤسسين", "founderNameEnglish": "اسم المؤسس (انجليزي)", "founderNameArabic": "اسم المؤسس (العربية)", "founderEmail": "الب<PERSON>يد الإلكتروني", "founderMobileNumber": "رقم الهات<PERSON> المتحرك", "founderEmirate": "الإمارة", "founderPassportNumber": "رقم الجواز", "founderResidencyIssuanceDate": "تاريخ إصدار الإقامة", "founderResidencyExpiryDate": "تاريخ انتهاء الإقامة", "founderAcademicQualification": "المؤهل العلمي", "founderJobTitle": "المسمى الوظيفي", "founderEmployer": "جهة العمل", "founderPassportPhoto": "صورة عن جواز السفر", "founderPersonalPhoto": "صورة شخصية", "foundingMembersList": "قائمة الأعضاء المؤسسين", "removeFoundingMember": "حذف عضو مؤسس", "foundersMeetingPlace": "المكان", "foundersMeetingEmirate": "الامارة", "founderDecisionDate": "تاريخ قرار التأسيس", "foundersMeetingDate": "التاريخ والوقت", "agendaItems": "محضر الاجتماع", "foundersMeetingAgenda": "أعمال اجتماع", "emiratesId": "رقم الهوية الإماراتية", "editFoundingMember": "تعديل ", "cloneFoundingMember": "نسخ", "dateOfBirth": "تاريخ الميلاد", "actions": "الإجراءات", "id": "رقم", "exceptionCases": "حالات الاستثناء", "foundersMeeting": "اجتماع المؤسسين", "founderEmiratesId": "رقم الهوية الإماراتية للمؤسس", "founderBOD": "تاريخ الميلاد", "founderNameEn": "الاسم", "founderNationality": "الجنسية", "responseDate": "تاريخ الرد", "status": "الحالة", "nationalityType": "نوع الجنسية", "foundingMemberAgeIsLessThan21YearsOld": "عمر العضو المؤسس أقل من 21 عامًا", "foundingMemberResidencyIsLessThan3Years": "إقامة العضو المؤسس أقل من 3 سنوات", "foundingMemberResidency": "إقامة هذا العضو المؤسس أقل من 3 سنوات، الرجاء إدخال سبب الاستثناء", "foundingMemberHasDiplomaticStatus": "العضو المؤسس لديه وضع دبلوماسي", "UAELocal": "الحصول على المعلومات", "percentageText": "نسبة الأعضاء المواطنين هي", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "<PERSON><PERSON><PERSON> حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "تاريخ (الاقدم)", "Date (Newest)": "تاريخ (الاحد<PERSON>)", "addFoundingMembersHelpMessage": "بمجرد رد العضو المؤسس على طلب التأكيد، ستتمكن من رؤية اسمه وجنسيته، وكذلك رده وتاريخ الرد.", "foundersMeetingAgendaHelpMessage": "جدول أعمال اجتماع المؤسسين:", "foundersMeetingAgendaHelpMessage1": "مناقشة إنشاء مؤسسة النفع العام.", "foundersMeetingAgendaHelpMessage2": "اعتماد النظام الأساسي.", "foundersMeetingAgendaHelpMessage3": "انتخاب أعضاء اللجنة المؤقتة.", "foundingMemberAgeLessThan21": "عمر العضو المؤسس أقل من 21 عامًا، الرجاء إدخال سبب الاستثناء", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "مناقشة إنشاء مؤسسة النفع العام", "PreparingTheDraftStatute": "اعتماد النظام الأساسي", "ElectionOfMembersOfTheTemporaryCommittee": "أعضاء اللجنة المؤقتة", "editNPO": "تعديل الرقم الموحد لمؤسسة النفع العام", "cloneNPO": "نسخ الرقم الموحد لمؤسسة النفع العام", "removeNPO": "حذف الرق<PERSON> الموحد لمؤسسة النفع العام", "npoName": "اسم مؤسسة النفع العام", "npoContactDetails": "تفاصيل الاتصال بمؤسسة النفع العام", "associationsNationalSocieties": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "addFoundingMemberUnifiedNumber": "اضافة الرقم الموحد للعضو المؤسس", "foundingMemberUnifiedNumber": "الرقم الموحد للعضو المؤسس", "npoEstablishmentNameEN": "اسم المؤسسة (إنجليزي)", "npoEstablishmentNameAr": "اسم المؤسسة (عربي)", "npoEstablishmentDate": "تاريخ التأسيس", "npoEstablishmentLegalFormEN": "الشكل القانوني (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني (عربي)", "AccountId": "معر<PERSON> الح<PERSON>اب", "associationsOrNationalSocietiesFromTheUnionList": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "npoUnifiedNumber": "الرقم الموحد", "addNPOUnifiedNumber": "إضافة الرقم الموحد لمؤسسة النفع العام", "npoUnifiedNumberOrNPOName": "الاسم او الرقم الموحد لمؤسسة النفع العام", "socialSolidarityFundsHelpMessage": "لا يقل عدد الأعضاء المنتسبين للصندوق عند الإنشاء عن (25) عضو", "unionFoundersMeetingHelpMessage": "لتتمكن من تقديم طلب ترخيص واشهار اتحاد، يجب عليك إضافة عدد لا يقل عن (5) جمعيات أو مؤسسات أهلية.  لا يجوز الجمع بين الجمعيات والمؤسسات الأهلية عند تكوين الإتحاد."}, "interimCommittee": {"Id": "رقم", "temporaryCommitteeMembersMeetingPlace": "مكان الاجتماع", "temporaryCommitteeMembersMeetingEmirate": "الإمارة", "temporaryCommitteeMembersMeetingDateTime": "التاريخ والوقت", "temporaryCommitteeMembersMeetingAgenda": "جدول الأعمال", "temporaryCommitteeMember": "عضو اللجنة المؤقتة", "temporaryCommitteeMemberPosition": "المنصب", "addCommitteeMember": "إضافة عضو لجنة", "committeeMemberList": "قائمة أعضاء اللجنة", "removeTemporaryCommitteeMember": "حذف عضو لجنة مؤقتة", "Title": "اللجنة المؤقتة", "agendaItems": "محضر الاجتماع", "Actions": "الإجراءات", "editMember": "تعديل ", "cloneMember": "نسخ", "removeMember": "<PERSON><PERSON><PERSON> ", "interimCommitteeMembers": "أعضاء اللجنة المؤقتة", "interimCommitteeInformation": "معلومات اجتماع اللجنة المؤقتة", "temporaryCommitteeMemberEID": "رقم الهوية", "temporaryCommitteeMemberName": "الاسم", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "<PERSON><PERSON><PERSON> حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "تاريخ (الاقدم)", "Date (Newest)": "تاريخ (الاحد<PERSON>)", "selectNpoLegalForm": "اختر النموذج القانوني لمؤسسة النفع العام", "interimCommitteeMembersHelpMessage": "ولا يجوز أن يقل عدد أعضاء اللجنة المؤقتة عن ثلاثة أعضاء", "interimCommitteeInformationHelpMessage": "جدول أعمال اجتماع اللجنة المؤقتة", "interimCommitteeInformationHelpMessage1": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة.", "interimCommitteeInformationHelpMessage2": "تعيين مفوض اللجنة المؤقتة.", "Definetheadministrativepositions": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة", "Appointthecommissioner": "تعيين مفوض اللجنة المؤقتة", "status": "الحالة"}, "membership": {"title": "العضوية والتسجيل", "membershipFees": "رسوم العضوية الأساسية", "normalMembershipFees": "رسوم العضوية", "annualMembershipDueDate": "تاريخ استحقاق الاشتراك السنوي", "enrollmentFees": "رسوم الالتحاق", "membershipConditionEn": "شرط العضوية (بالإنجليزية)", "membershipConditionAr": "شرط العضوية (بالعربية)", "category": "الفئة", "addCondition": "إضافة شروط العضوية", "addConditionButton": "إضافة شرط", "membershipConditionsList": "قائمة أنواع و شروط العضوية", "Actions": "الإجراءات", "id": "رقم", "cancel": "إلغاء", "editCondition": "تعديل ", "cloneCondition": "نسخ", "removeCondition": "<PERSON><PERSON><PERSON> ", "membershipInformation": "معلومات العضوية", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "status": "الحالة", "beneficiaryMembershipFees": "رسوم العضوية المنتفعة او المستفيدة"}, "allocationOfFoundationFunds": {"title": "تخصيص أموال المؤسسة", "id": "رقم", "AllocationCycle": "دورية التخصيص", "FundsAmount": "قيمة الأموال", "DescriptionOfInKindFundsAr": "وصف الأموال العينية (عربي)", "DescriptionOfInKindFundsEn": "وصف الأموال العينية (انجليزي)", "NatureOfFundsAllocated": "طبيعة الأموال المخصصة", "foundingMemberEID": "العضو المؤسس", "addAllocationOfFoundationFunds": "إضافة تخصيص الأموال", "editAllocationOfFoundationFunds": "تعديل تخصيص الأموال", "Actions": "العمليات", "help": "مساعدة", "totalFundsAmountIs": "إجمالي مبلغ الأموال هو", "totalFundsAmountError": "الح<PERSON> الادنى يجب ان يكون 5000000", "status": "الحالة"}, "boardOfDirectors": {"Id": "رقم", "Title": "مجلس الادارة", "Actions": "الإجراءات", "boardMembersExceed11": "عدد أعضاء مجلس الإدارة يتجاوز 11 عضوا", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "exceptionReasonAr": "سبب الاستثناء (عربي)", "numberOfBoardMembers": " عدد أعضاء مجلس الإدارة ", "frequencyOfBoardMeetings": "دورية انعقاد اجتماعات مجلس الإدارة", "BoardElectionCycle": "دورية انتخاب مجلس الإدارة", "boardMemberReNomination": "هل يجوز إعادة انتخاب أعضاء مجلس الإدارة لفترة أخرى؟", "numberOfPermissibleTerms": "عد<PERSON> الفترات المسموح بها", "localBoardMembersPercentage": "نسبة أعضاء مجلس الإدارة من المواطنين", "electionMethod": "طريقة الانتخاب", "nominationConditionEnglish": "الشرط (انجليزي)", "nominationConditionArabic": "الشرط (عربي)", "addNominationCondition": "إضافة شرط", "nominationConditionsList": "قائمة شروط الترشح لعضوية مجلس الإدارة", "removeNominationCondition": "حذ<PERSON> شر<PERSON>", "adminPositionTitleEnglish": "المنصب الإداري (بالانجليزية)", "adminPositionTitleArabic": "المنصب الإداري (بالعربية)", "addPosition": "إضافة منصب", "boardAdminPositionsList": "قائمة المناصب الإدارية للمجلس", "editCondition": "تعديل ", "cloneCondition": "نسخ", "removeCondition": "<PERSON><PERSON><PERSON> ", "editPosition": "تعديل ", "clonePosition": "نسخ", "removePosition": "<PERSON><PERSON><PERSON> ", "exceptionRequests": "طلبات الاستثناء", "boardOfDirectorsInformation": "معلومات مجلس الإدارة", "boardOfDirectorsConditions": "الشروط الواجب توافرها في أعضاء المجلس", "boardOfDirectorsPositions": "المناصب الإدارية", "individualElection": "الانتخابات الفردية", "individualElectionCondition": "يقوم أمين السر مؤسسة النفع العام بإضافة المرشحين واحدًا تلو الآخر إلى قائمة المرشحين لمجلس الإدارة. يتحقق النظام من وجود ما لا يقل عن 5 مرشحين وأن نسبة المواطنين الإماراتيين تتوافق مع النظام الأساسي لمؤسسة النفع العام.", "slateElection": "الانتخابات القائمة", "slateElectionCondition": "يقوم أمين السر مؤسسة النفع العام بإنشاء قوائم، ويعطي كل قائمة عنوانًا باللغتين الإنجليزية والعربية، ويضيف المرشحين تحت كل قائمة. يتحقق النظام من أن كل قائمة تحتوي على ما لا يقل عن 5 مرشحين وأن نسبة المواطنين الإماراتيين تتوافق مع النظام الأساسي لمؤسسة النفع العام.", "specialElectionGCC": "انتخابات خاصة / مجلس التعاون الخليجي (9 مقاعد)", "specialElectionGCCCondition": "يعرض النظام 6 قوائم تمثل دول مجلس التعاون الخليجي و3 مقاعد إضافية متاحة لقائمة اضافية. يقوم أمين السر مؤسسة النفع العام بإضافة المرشحين لكل قائمة. يتحقق النظام من جنسية المرشحين ويضمن الحد الأدنى لعدد المرشحين لكل قائمة.", "twoStageElection1": "انتخاب على مرحلتين (رئيس مجلس الإدارة وعضو مجلس الإدارة).", "twoStageElection1Condition": "يعرض النظام قائمتين: رئيس مجلس الإدارة وعضو مجلس الإدارة. يقوم أمين السر مؤسسة النفع العام بإضافة المرشحين لكل قائمة. يتحقق النظام من وجود ما لا يقل عن 5 مرشحين في المجموع وأن نسبة المواطنين الإماراتيين تتوافق مع النظام الأساسي لمؤسسة النفع العام. لا يمكن أن يكون المرشحون في القائمتين في نفس الوقت.", "twoStageElection2": "انتخاب على مرحلتين (رئيس مجلس الإدارة أو عضو مجلس الإدارة)", "twoStageElection2Condition": "مشابه للطريقة السابقة، لكن يمكن أن يكون المرشحون في قائمة واحدة فقط. بعد التصويت لرئيس مجلس الإدارة، لا يمكن إضافة المرشحين غير الناجحين إلى قائمة أعضاء مجلس الإدارة.", "electionsWithFemaleQuota": "انتخابات مع حصة نسائية", "electionsWithFemaleQuotaCondition": "يقوم أمين السر مؤسسة النفع العام بإضافة المرشحين واحدًا تلو الآخر. يتحقق النظام من وجود ما لا يقل عن 5 مرشحين وأن نسبة المواطنين الإماراتيين تتوافق مع النظام الأساسي لمؤسسة النفع العام. يعطي النظام الأولوية للمرشحات الإناث اللواتي حصلن على أعلى الأصوات، بحد أقصى 3، ويكمل القائمة بالمرشحين الذكور.", "help": "المساعدة", "status": "الحالة"}, "FundServices": {"title": "خدمات الصندوق", "Header": " الخدمات التي يقدمها الصندوق", "help": "المساعدة", "ServiceTitle": "عنوان الخدمة (الإنجليزية)", "ServiceTitleAR": "عنوان الخدمة (عربي)", "Actions": "إجراءات", "helpMessage": "الخدمات التي يقدمها الصندوق", "Amount": "المبلغ", "editFundServices": "تعديل خدمة الصندوق", "status": "الحالة"}, "boardOfTrustees": {"title": "مجلس الأمناء", "frequencyOfMeetings": "دورية انعقاد اجتماعات مجلس الأمناء", "numberOfMembers": "عد<PERSON> أعضاء مجلس الأمناء", "frequencyOfAppointments": "دورية تعيين مجلس الأمناء", "conditionForNominationEn": "الشرط (انجليزي)", "conditionForNominationAr": "الشرط (عربي)", "addCondition": "إضافة شرط", "administrativePositionTitleEn": "المنصب الإداري (بالانجليزية)", "administrativePositionTitleAr": "المنصب الإداري (بالعربية)", "addPosition": "إضافة منصب", "addMember": "إضافة عضو", "memberPosition": "منصب عضو مجلس الأمناء", "foundingMember": "الأعضاء المؤسسين", "actions": "الإجراءات", "id": "رقم", "cancel": "إلغاء", "editCondition": "تعديل ", "cloneCondition": "نسخ", "removeCondition": "<PERSON><PERSON><PERSON> ", "editPosition": "تعديل ", "clonePosition": "نسخ", "removePosition": "<PERSON><PERSON><PERSON> ", "editMember": "تعديل ", "cloneMember": "نسخ", "removeMember": "<PERSON><PERSON><PERSON> ", "boardOfTrusteesInformation": "معلومات مجلس الأمناء", "boardOfTrusteesMembers": "أعضاء مجلس الأمناء", "boardOfTrusteesPositions": "مناصب مجلس الأمناء", "boardOfTrusteesConditions": "الشروط الواجب توافرها في أعضاء مجلس الأمناء", "conditions": "الشروط", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "Position": "من<PERSON><PERSON>", "foundingMembersLessThan70Percent": "الأعضاء  الذين يحملون الجنسية الدولة أقل من (70%)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "exceptionReasonAr": "سبب الاستثناء (عربي)", "foundingMembersLessThanSeven": "يقل عدد الأعضاء  عن (7) سبعة أعضاء", "numberOfFounders": "<PERSON><PERSON><PERSON> ", "founderEmiratesID": "رقم الهوية", "founderDateOfBirth": "تاريخ الميلاد", "addFoundingMember": "إضافة عضو", "founderNameEnglish": "اسم عضو (انجليزي)", "founderNameArabic": "اسم عضو (العربية)", "founderEmail": "الب<PERSON>يد الإلكتروني", "founderMobileNumber": "رقم الهات<PERSON> المتحرك", "founderEmirate": "الإمارة", "founderPassportNumber": "رقم الجواز", "founderResidencyIssuanceDate": "تاريخ إصدار الإقامة", "founderResidencyExpiryDate": "تاريخ انتهاء الإقامة", "founderAcademicQualification": "المؤهل العلمي", "founderJobTitle": "المسمى الوظيفي", "founderEmployer": "جهة العمل", "founderPassportPhoto": "صورة عن جواز السفر", "founderPersonalPhoto": "صورة شخصية", "foundingMembersList": "قائمة الأعضاء ", "removeFoundingMember": "حذف عضو مؤسس", "foundersMeetingPlace": "المكان", "foundersMeetingEmirate": "الامارة", "founderDecisionDate": "تاريخ قرار التأسيس", "foundersMeetingDate": "التاريخ والوقت", "agendaItems": "محضر الاجتماع", "foundersMeetingAgenda": "أعمال اجتماع", "emiratesId": "رقم الهوية الإماراتية", "editFoundingMember": "تعديل ", "cloneFoundingMember": "نسخ", "dateOfBirth": "تاريخ ميلاد ", "exceptionCases": "حالات الاستثناء", "foundersMeeting": "اجتماع ", "founderEmiratesId": "رقم الهوية الإماراتية", "founderBOD": "تاريخ الميلاد", "founderNameEn": "الاسم", "founderNationality": "الجنسية", "responseDate": "تاريخ الرد", "status": "الحالة", "nationalityType": "نوع الجنسية", "foundingMemberAgeIsLessThan21YearsOld": "عمر العضو  أقل من 21 عامًا", "foundingMemberResidencyIsLessThan3Years": "إقامة العضو  أقل من 3 سنوات", "foundingMemberResidency": "إقامة هذا العضو  أقل من 3 سنوات، الرجاء إدخال سبب الاستثناء", "foundingMemberHasDiplomaticStatus": "العضو  لديه وضع دبلوماسي", "UAELocal": "الحصول على المعلومات", "percentageText": "نسبة الأعضاء المواطنين هي", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "<PERSON><PERSON><PERSON> حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "تاريخ (الاقدم)", "Date (Newest)": "تاريخ (الاحد<PERSON>)", "addFoundingMembersHelpMessage": "بمجرد رد العضو  على طلب التأكيد، ستتمكن من رؤية اسمه وجنسيته، وكذلك رده وتاريخ الرد.", "foundersMeetingAgendaHelpMessage": "جدول أعمال اجتماع :", "foundersMeetingAgendaHelpMessage1": "مناقشة إنشاء مؤسسة النفع العام.", "foundersMeetingAgendaHelpMessage2": "اعتماد النظام الأساسي.", "foundersMeetingAgendaHelpMessage3": "انتخاب أعضاء اللجنة المؤقتة.", "foundingMemberAgeLessThan21": "عمر العضو  أقل من 21 عامًا، الرجاء إدخال سبب الاستثناء", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "مناقشة إنشاء مؤسسة النفع العام", "PreparingTheDraftStatute": "اعتماد النظام الأساسي", "ElectionOfMembersOfTheTemporaryCommittee": "أعضاء اللجنة المؤقتة", "npoMember": "أعضاء مجلس الأمناء", "helpMessageMember1": "يجب إضافة عضو مجلس أمناء واحد فقط لكل منصب من مناصب مجلس الأمناء.", "helpMessageMember2": "بالنسبة لمنصب 'عضو مجلس الأمناء' فقط يمكنك إضافة عدة أعضاء مجلس أمناء."}, "requestHistory": {"title": "سجل الطلب"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة."}, "reviewAndSubmit": {"ExamplesOfActivitiesAndProgramsOfferedByTheNPO": "أمثلة على الأنشطة والبرامج التي تقدمها مؤسسة النفع العام", "exampleOfActivitiesAr": "عنوان النشاط/البرنامج (عربي)", "exampleOfActivitiesEn": "عنوان النشاط/البرنامج (إنجليزي)", "activitiesAndPrograms": "أمثلة على الأنشطة والبرامج التي تقدمها مؤسسة النفع العام", "title": "مراجعة وتقديم الطلب", "downloadBylaws": "تحميل النظام الأساسي", "ministryComments": "ملاحظات الوزارة", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "npoLegalForm": "الشكل القانوني", "localDecreeLawNumber": "رقم المرسوم المحلي", "issuanceDate": "تاريخ الإصدار", "associationClassification": "تصنيف الجمعية", "npoName": "اسم الجمعية", "proposedNameEn": "الاسم المقترح (EN)", "proposedNameAr": "الاسم المقترح (AR)", "NameEn": "الاسم (انجليزي)", "NameAr": "الا<PERSON><PERSON> (عربي)", "npoContactDetails": "تفاصيل الاتصال بالجمعية", "Landline": "الهات<PERSON> الثابت", "POBox": "ص.ب", "FaxNumber": "رقم الفاكس", "Email": "الب<PERSON>يد الإلكتروني", "Website": "الموقع الإلكتروني", "Address": "العنوان", "GeographicLocation": "الموقع الجغرافي", "Emirate": "الإمارة", "permanentAdvance": "المبلغ المخصص", "fundsAllocation": "توزيع الأموال", "fundsAllocationAmount": "مبلغ توزيع الأموال", "associationsNationalSocieties": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "ObjectiveEn": "الهدف (بالإنجليزية)", "ObjectiveAr": "الهدف (بالعربية)", "MeansOfAchievingObjectiveEn": "وسائل تحقيق الهدف (بالإنجليزية)", "MeansOfAchievingObjectiveAr": "وسائل تحقيق الهدف (بالعربية)", "BasicInformation": "معلومات أساسية", "Objectives": "الأهداف", "foundingMembers": "الأعضاء المؤسسون", "interimCommittee": "اللجنة المؤقتة", "membership": "العضوية والتسجيل", "boardOfDirectors": "مجلس الإدارة", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "هل الأعضاء المؤسسين الذين يحملون جنسية الدولة أقل من (70%)؟", "ExceptionReasonFor70En": "سبب الاستثناء (بالإنجليزية)", "ExceptionReasonFor70Ar": "سبب الاستثناء (بالعربية)", "IsNumberOfFoundingMembersIsLessThan7Members": "ه<PERSON> <PERSON><PERSON>د الأعضاء المؤسسين أقل من 7؟", "nationalityType": "نوع الجنسية", "foundingMemberAgeIsLessThan21YearsOld": "هل عمر العضو المؤسس أقل من 21 عامًا؟", "foundingMemberResidencyIsLessThan3Years": "هل إقامة العضو المؤسس أقل من 3 سنوات؟", "foundingMemberHasDiplomaticStatus": "هل للعضو المؤسس وضع دبلوماسي؟", "founderEmiratesID": "الهوية الاماراتية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (بالعربية)", "exceptionReasonEn": "سبب الاستثناء (بالإنجليزية)", "boardOfTrustees": "مجلس الأمناء", "memberPosition": "منصب العضو", "MeetingPlace": "مكان الاجتماع", "Date": "التاريخ", "Agenda": "جدول الأعمال", "membershipConditionEn": "أنواع و شروط العضوية (بالإنجليزية)", "membershipConditionAr": "أنواع و شروط العضوية (بالعربية)", "MembershipFees": "رسوم العضوية الأساسية", "normalMembershipFees": "رسوم العضوية", "beneficiaryMembershipFees": "رسوم العضوية المنتفعة او المستفيدة", "AnnualMembershipDueDate": "تاريخ استحقاق العضوية السنوية", "EnrollmentFees": "رسوم الالتحاق", "adminPositionTitleEnglish": "عنوان المنصب الإداري (بالإنجليزية)", "adminPositionTitleArabic": "عنوان المنصب الإداري (بالعربية)", "nominationConditionEnglish": "شروط الترشيح (بالإنجليزية)", "nominationConditionArabic": "شروط الترشيح (بالعربية)", "FrequencyOfMonthlyBoardMeetings": "عد<PERSON> الاجتماعات الشهرية لمجلس الإدارة", "localBoardMembersPercentage": "نسبة أعضاء مجلس الإدارة من المواطنين", "ElectionMethod": "طريقة الانتخاب", "NumberOfPermissibleTerms": "عدد الدورات المسموح بها", "BoardElectionCycle": "دورية انتخاب مجلس الإدارة", "MemberIsExceeds11": "<PERSON><PERSON><PERSON> الأعضاء يتجاوز 11", "exceptionRequests": "طلبات الاستثناء", "boardOfDirectorsInformation": "معلومات مجلس الإدارة", "numberOfBoardMembers": "عدد أعضاء مجلس الإدارة", "boardOfDirectorsConditions": "الشروط الواجب توافرها في أعضاء المجلس", "boardOfDirectorsPositions": "المناصب الإدارية", "CanBeRenominated": "يمكن إعادة ترشيحه", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "foundingMember": "عضو مؤسس", "administrativePositionTitleAr": "عنوان المنصب الإداري (بالعربية)", "administrativePositionTitleEn": "عنوان المنصب الإداري (بالإنجليزية)", "conditionForNominationAr": "شروط الترشيح (بالعربية)", "conditionForNominationEn": "شروط الترشيح (بالإنجليزية)", "frequencyOfMeetings": "دورية انعقاد اجتماعات مجلس الأمناء", "numberOfMembers": "ع<PERSON><PERSON> الأعضاء", "frequencyOfAppointments": "دورية تعيين مجلس الأمناء", "boardOfTrusteesInformation": "معلومات مجلس الأمناء", "boardOfTrusteesConditions": "الشروط الواجب توافرها في أعضاء مجلس الأمناء", "boardOfTrusteesPositions": "مناصب مجلس الأمناء", "boardOfTrusteesMembers": "أعضاء مجلس الأمناء", "Means of Achieving Objective": "وسائل تحقيق الهدف", "Id": "رقم", "temporaryCommitteeMemberEID": "رقم الهوية", "temporaryCommitteeMemberName": "الاسم", "temporaryCommitteeMemberPosition": "المنصب", "status": "الحاله", "founderNameEnglish": "اسم المؤسس (انجليزي)", "founderNameArabic": "اسم المؤسس (العربية)", "founderNationality": "الجنسية", "responseDate": "تاريخ الرد", "AccountId": "معر<PERSON> الح<PERSON>اب", "npoUnifiedNumber": "الرقم الموحد لمؤسسة النفع العام", "npoEstablishmentNameEN": "اسم مؤسسة النفع العام (إنجليزي)", "npoEstablishmentNameAr": "اسم مؤسسة النفع العام (عربي)", "npoEstablishmentDate": "تاريخ تأسيس مؤسسة النفع العام", "npoEstablishmentLegalFormEN": "الشكل القانوني لمؤسسة النفع العام (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني لمؤسسة النفع العام (عربي)", "yes": "نعم", "no": "لا", "interimCommitteeMembers": "أعضاء اللجنة المؤقتة", "interimCommitteeInformation": "معلومات اجتماع اللجنة المؤقتة", "membershipInformation": "معلومات العضوية", "membershipConditions": "أنواع و شروط العضوية", "foundersMeeting": "اجتماع المؤسسين", "targetGroupsAssociation": "الفئات المستهدفة من أنشطة الجمعية", "targetGroupsNationalSociety": "الفئات المستهدفة من أنشطة الجمعية", "addTargetGroup": "إضافة فئة مستهدفة", "targetGroupNameEn": "اسم الفئة المستهدفة (إنجليزي)", "targetGroupNameAr": "اسم الفئة المستهدفة (عربي)", "FundServices": "خدمات الصندوق", "ServiceTitle": "عنوان الخدمة (الإنجليزية)", "ServiceTitleAR": "عنوان الخدمة (عربي)", "Actions": "إجراءات", "Amount": "المبلغ", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "مناقشة إنشاء مؤسسة النفع العام", "PreparingTheDraftStatute": "اعتماد النظام الأساسي", "ElectionOfMembersOfTheTemporaryCommittee": "أعضاء اللجنة المؤقتة", "Definetheadministrativepositions": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة", "Appointthecommissioner": "تعيين مفوض اللجنة المؤقتة", "id": "رقم", "AllocationCycle": "دورية التخصيص", "FundsAmount": "قيمة الأموال", "DescriptionOfInKindFundsAr": "وصف الأموال العينية (عربي)", "DescriptionOfInKindFundsEn": "وصف الأموال العينية (انجليزي)", "NatureOfFundsAllocated": "طبيعة الأموال المخصصة", "foundingMemberEID": "العضو المؤسس", "addAllocationOfFoundationFunds": "إضافة تخصيص الأموال", "editAllocationOfFoundationFunds": "تعديل تخصيص الأموال", "help": "مساعدة", "allocationOfFoundationFunds": "تخصيص أموال المؤسسة", "totalFundsAmountIs": "إجمالي مبلغ الأموال هو", "agendaItems": "محضر الاجتماع", "Position": "من<PERSON><PERSON>", "founderDecisionDate": "تاريخ قرار التأسيس", "fundbelongsDetails": "بيانات الجهة التي يتبع لها الصندوق", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (بالإنجليزية)", "EntityNameAr": "اسم الجهة (بالعربية)", "EntityType": " تصنيف الجهة ", "NumberOfEmployees": "<PERSON><PERSON><PERSON> الموظفين", "FundServiceObjectiveEn": "أنشطة وأهداف الصندوق (بالانجليزية)", "FundServiceObjectiveAr": "أنشطة وأهداف الصندوق (بالعربية)", "MeansOfAchievingObjective": "وسائل تحقيق الهدف", "fundServiceObjectives": "أنشطة وأهداف الصندوق", "category": "الفئة"}, "requestDetails": {"ExamplesOfActivitiesAndProgramsOfferedByTheNPO": "أمثلة على الأنشطة والبرامج التي تقدمها مؤسسة النفع العام", "exampleOfActivitiesAr": "عنوان النشاط/البرنامج (عربي)", "exampleOfActivitiesEn": "عنوان النشاط/البرنامج (إنجليزي)", "activitiesAndPrograms": "أمثلة على الأنشطة والبرامج التي تقدمها مؤسسة النفع العام", "title": "مراجعة وتقديم الطلب", "downloadBylaws": "تحميل النظام الأساسي", "ministryComments": "ملاحظات الوزارة", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "npoLegalForm": "الشكل القانوني", "localDecreeLawNumber": "رقم المرسوم المحلي", "issuanceDate": "تاريخ الإصدار", "associationClassification": "تصنيف الجمعية", "npoName": "اسم الجمعية", "proposedNameEn": "الاسم المقترح (EN)", "proposedNameAr": "الاسم المقترح (AR)", "NameEn": "الاسم (انجليزي)", "NameAr": "الا<PERSON><PERSON> (عربي)", "npoContactDetails": "تفاصيل الاتصال بالجمعية", "Landline": "الهات<PERSON> الثابت", "POBox": "ص.ب", "FaxNumber": "رقم الفاكس", "Email": "الب<PERSON>يد الإلكتروني", "Website": "الموقع الإلكتروني", "Address": "العنوان", "GeographicLocation": "الموقع الجغرافي", "Emirate": "الإمارة", "permanentAdvance": "المبلغ المخصص", "fundsAllocation": "توزيع الأموال", "fundsAllocationAmount": "مبلغ توزيع الأموال", "associationsNationalSocieties": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "ObjectivesEn": "الأهداف (الإنجليزية)", "ObjectivesAR": "الأهداف (العربية)", "MeansOfAchievingObjectiveEn": "وسائل تحقيق الهدف (الإنجليزية)", "MeansOfAchievingObjectiveAr": "وسائل تحقيق الهدف (العربية)", "BasicInformation": "معلومات أساسية", "Objectives": "الأهداف", "foundingMembers": "الأعضاء المؤسسون", "interimCommittee": "اللجنة المؤقتة", "membership": "العضوية والتسجيل", "boardOfDirectors": "مجلس الإدارة", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "هل الأعضاء المؤسسين الذين يحملون جنسية الدولة أقل من (70%)؟", "ExceptionReasonFor70En": "سبب الاستثناء (بالإنجليزية)", "ExceptionReasonFor70Ar": "سبب الاستثناء (بالعربية)", "IsNumberOfFoundingMembersIsLessThan7Members": "ه<PERSON> <PERSON><PERSON>د الأعضاء المؤسسين أقل من 7؟", "nationalityType": "نوع الجنسية", "foundingMemberAgeIsLessThan21YearsOld": "هل عمر العضو المؤسس أقل من 21 عامًا؟", "foundingMemberResidencyIsLessThan3Years": "هل إقامة العضو المؤسس أقل من 3 سنوات؟", "foundingMemberHasDiplomaticStatus": "هل للعضو المؤسس وضع دبلوماسي؟", "founderEmiratesID": "الهوية الاماراتية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (بالعربية)", "exceptionReasonEn": "سبب الاستثناء (بالإنجليزية)", "boardOfTrustees": "مجلس الأمناء", "memberPosition": "منصب العضو", "MeetingPlace": "مكان الاجتماع", "Date": "التاريخ", "Agenda": "جدول الأعمال", "membershipConditionEn": "أنواع و شروط العضوية (بالإنجليزية)", "membershipConditionAr": "أنواع و شروط العضوية (بالعربية)", "MembershipFees": "رسوم العضوية", "normalMembershipFees": "رسوم العضوية", "beneficiaryMembershipFees": "رسوم العضوية المنتفعة او المستفيدة", "AnnualMembershipDueDate": "تاريخ استحقاق العضوية السنوية", "EnrollmentFees": "رسوم الالتحاق", "adminPositionTitleEnglish": "عنوان المنصب الإداري (بالإنجليزية)", "adminPositionTitleArabic": "عنوان المنصب الإداري (بالعربية)", "nominationConditionEnglish": "شروط الترشيح (بالإنجليزية)", "nominationConditionArabic": "شروط الترشيح (بالعربية)", "FrequencyOfMonthlyBoardMeetings": "عد<PERSON> الاجتماعات الشهرية لمجلس الإدارة", "localBoardMembersPercentage": "نسبة أعضاء المجلس المحلي", "ElectionMethod": "طريقة الانتخاب", "NumberOfPermissibleTerms": "عدد الدورات المسموح بها", "BoardElectionCycle": "دورية انتخاب مجلس الإدارة", "MemberIsExceeds11": "<PERSON><PERSON><PERSON> الأعضاء يتجاوز 11", "exceptionRequests": "طلبات الاستثناء", "boardOfDirectorsInformation": "معلومات مجلس الإدارة", "numberOfBoardMembers": "عدد أعضاء مجلس الإدارة", "boardOfDirectorsConditions": "الشروط الواجب توافرها في أعضاء المجلس", "boardOfDirectorsPositions": "المناصب الإدارية", "CanBeRenominated": "يمكن إعادة ترشيحه", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "foundingMember": "عضو مؤسس", "administrativePositionTitleAr": "عنوان المنصب الإداري (بالعربية)", "administrativePositionTitleEn": "عنوان المنصب الإداري (بالإنجليزية)", "conditionForNominationAr": "شروط الترشيح (بالعربية)", "conditionForNominationEn": "شروط الترشيح (بالإنجليزية)", "frequencyOfMeetings": "دورية انعقاد اجتماعات مجلس الأمناء", "numberOfMembers": "ع<PERSON><PERSON> الأعضاء", "frequencyOfAppointments": "دورية تعيين مجلس الأمناء", "boardOfTrusteesInformation": "معلومات مجلس الأمناء", "boardOfTrusteesConditions": "الشروط الواجب توافرها في أعضاء مجلس الأمناء", "boardOfTrusteesPositions": "مناصب مجلس الأمناء", "boardOfTrusteesMembers": "أعضاء مجلس الأمناء", "Objective (English)": "الهدف (باللغة الإنجليزية)", "Objective (Arabic)": "الهدف (باللغة العربية)", "Means of Achieving Objective (English)": "وسائل تحقيق الهدف (باللغة الإنجليزية)", "Means of Achieving Objective (Arabic)": "وسائل تحقيق الهدف (باللغة العربية)", "Means of Achieving Objective": "وسائل تحقيق الهدف", "Id": "رقم", "temporaryCommitteeMemberEID": "رقم الهوية", "temporaryCommitteeMemberName": "الاسم", "temporaryCommitteeMemberPosition": "المنصب", "status": "الحاله", "founderNameEnglish": "اسم المؤسس (انجليزي)", "founderNameArabic": "اسم المؤسس (العربية)", "founderNationality": "الجنسية المؤسس", "responseDate": "تاريخ الرد", "AccountId": "معر<PERSON> الح<PERSON>اب", "npoUnifiedNumber": "الرقم الموحد لمؤسسة النفع العام", "npoEstablishmentNameEN": "اسم مؤسسة نفع عام (إنجليزي)", "npoEstablishmentNameAr": "اسم مؤسسة نفع عام (عربي)", "npoEstablishmentDate": "تاريخ تأسيس مؤسسة نفع عام", "npoEstablishmentLegalFormEN": "الشكل القانوني لمؤسسة النفع العام (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني لمؤسسة النفع العام (عربي)", "yes": "نعم", "no": "لا", "interimCommitteeMembers": "أعضاء اللجنة المؤقتة", "interimCommitteeInformation": "معلومات اجتماع اللجنة المؤقتة", "membershipInformation": "معلومات العضوية", "membershipConditions": "أنواع و شروط العضوية", "foundersMeeting": "اجتماع المؤسسين", "NPO_LICENSE_DECLARATION": "طلب إنشاء مؤسسات النفع العام", "NPO_ByDecree": "طلب تسجيل مؤسسات النفع العام", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "SUBMIT_DATE": "تاريخ التقديم", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإنشاء", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "COMMENT": "تعليق", "FundServices": "خدمات الصندوق", "ServiceTitle": "عنوان الخدمة (الإنجليزية)", "ServiceTitleAR": "عنوان الخدمة (عربي)", "Actions": "إجراءات", "Amount": "المبلغ", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "مناقشة إنشاء مؤسسة النفع العام", "PreparingTheDraftStatute": "اعتماد النظام الأساسي", "ElectionOfMembersOfTheTemporaryCommittee": "أعضاء اللجنة المؤقتة", "targetGroupsAssociation": "الفئات المستهدفة من أنشطة الجمعية", "targetGroupsNationalSociety": "الفئات المستهدفة من أنشطة الجمعية", "addTargetGroup": "إضافة فئة مستهدفة", "targetGroupNameEn": "اسم الفئة المستهدفة (إنجليزي)", "targetGroupNameAr": "اسم الفئة المستهدفة (عربي)", "Definetheadministrativepositions": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة", "Appointthecommissioner": "تعيين مفوض اللجنة المؤقتة", "id": "رقم", "AllocationCycle": "دورية التخصيص", "FundsAmount": "قيمة الأموال", "DescriptionOfInKindFundsAr": "وصف الأموال العينية (عربي)", "DescriptionOfInKindFundsEn": "وصف الأموال العينية (انجليزي)", "NatureOfFundsAllocated": "طبيعة الأموال المخصصة", "foundingMemberEID": "العضو المؤسس", "addAllocationOfFoundationFunds": "إضافة تخصيص الأموال", "editAllocationOfFoundationFunds": "تعديل تخصيص الأموال", "help": "مساعدة", "allocationOfFoundationFunds": "تخصيص أموال المؤسسة", "totalFundsAmountIs": "إجمالي مبلغ الأموال هو", "agendaItems": "محضر الاجتماع", "Position": "من<PERSON><PERSON>", "founderDecisionDate": "تاريخ قرار التأسيس", "fundbelongsDetails": "بيانات الجهة التي يتبع لها الصندوق", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (بالإنجليزية)", "EntityNameAr": "اسم الجهة (بالعربية)", "EntityType": " تصنيف الجهة ", "NumberOfEmployees": "<PERSON><PERSON><PERSON> الموظفين", "ObjectiveEn": "الهدف (بالإنجليزية)", "ObjectiveAr": "الهدف (بالعربية)", "fundServiceObjectives": "أنشطة وأهداف الصندوق", "FundServiceObjectiveEn": "أنشطة وأهداف الصندوق (بالانجليزية)", "FundServiceObjectiveAr": "أنشطة وأهداف الصندوق (بالعربية)", "category": "الفئة", "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "ص<PERSON><PERSON><PERSON> العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لمؤسسة النفع العام", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "عنوان البريد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لمؤسسة النفع العام", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لمؤسسة النفع العام"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "nonMuslimsWorshipPlaceLicense": {"title": "إصدار ترخيص دار عبادة لغير المسلمين", "desc": "إصدار ترخيص دار عبادة لغير المسلمين", "roomTitle": "طلب تخصيص غرفة عبادة لغير المسلمين", "roomDesc": "طلب تخصيص غرفة عبادة لغير المسلمين", "forms": {"legalTypePage": {"title": "إصدار ترخيص دار عبادة لغير المسلمين", "selectNpoLegalForm": "اختر ترخيص دار عبادة لغير المسلمين", "description": ""}, "basicInformation": {"title": "المعلومات الأساسية", "worshipPlaceName": "اسم دار العبادة", "worshipPlaceInformation": "معلومات دار العبادة", "parentWorshipPlaceInfo": "معلومات دار العبادة الأم", "religion": "الديانة", "proposedNamesList": "قائمة الأسماء المقترحة", "sect": "الطائفة", "belief": "العقيدة", "brief": "نبذة عن الديانة أو الطائفة أو العقيدة", "religiousPractices": "الممارسات والعبادات والشعائر  والطقوس الدينية", "programTimings": "برنامج ومواقيت إقامة العبادات والشعائر  والطقوس الدينية", "controlsAndProcedures": "ضوابط وإجراءات إقامة العبادات والشعائر والطقوس الدينية", "worshipPlaceType": "نوع دار العبادة", "typeName": "اسم نوع دار العبادة", "parentHouseOfWorshipPlaceNameEn": "اسم دار العبادة الأم (انجليزي)", "parentHouseOfWorshipPlaceNameAr": "اسم دار العبادة الأم (عربي)", "country": "الدولة", "proposedNameEn": "الاسم المقترح (انجليزي)", "proposedNameAr": "الاسم المقترح (عربي)", "addProposedName": "إضافة الاسم المقترح", "edit": "تعديل", "clone": "نسخ", "remove": "<PERSON><PERSON><PERSON>", "landlineNumber": "رقم الهاتف الثابت", "poBox": "صندوق البريد", "email": "الب<PERSON>يد الإلكتروني", "website": "الموقع الإلكتروني", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "emirate": "الإمارة", "logo": "الشعار", "add": "إضافة", "id": "رقم", "actions": "الإجراءات", "contactDetails": "تفاصيل الاتصال", "status": "الحالة", "threeProposedNames": "3 اسماء مقترحة", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "الترتيب حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "التاريخ (الأقدم)", "Date (Newest)": "التاريخ (الأحدث)", "membershipConditions": "أنواع و شروط العضوية", "help": "المساعدة", "helpMessage": "لتقديم الطلب يجب إضافة اسمين مقترحين و بحد أقصى ثلاثة أسماء", "worshipRoomName": "اسم غرفة العبادة", "worshipRoomInfo": "معلومات غرفة العبادة", "worshipRoomLocationType": "نوع مقر غرفة العبادة", "worshipRoomLocation": "مقر غرفة العبادة", "worshipRoomDetails": "تفاصيل غرفة العبادة", "worshipPlaceProposedNames": " الأسماء المقترحة", "geographicalLocationInTheAirport": "الموقع الجغرافي داخل المطار  ", "area": "المساحة (متر مربع) ", "capacity": "الطاقة الاستيعابية ", "worshipRoomDescriptionEn": "وصف الغرفة (الإنجليزية)", "worshipRoomDescriptionAr": "وصف الغرفة (العربية)"}, "purposesAndActivities": {"PurposesAndActivitiesEn": "الغرض والنشاط (انجليزي)", "PurposesAndActivitiesAr": "الغرض والنشاط (عربي)", "Id": "رقم", "status": "الحالة", "Title": "الأغراض والأنشطة", "Actions": "الإجراءات", "purposesAndActivitiesToolTip": "أغراض وأنشطة", "editPurposesAndActivities": "تعديل", "clonePurposesAndActivities": "نسخ", "removePurposeAndActivity": "<PERSON><PERSON><PERSON>", "purposesAndActivites": "الأغراض والأنشطة", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "الترتيب حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "التاريخ (الأقدم)", "Date (Newest)": "التاريخ (الأحدث)", "help": "المساعدة", "helpMessage": "لتقديم الطلب، يجب عليك إضافة خمسة (5) أغراض و/أو أنشطة على الأقل"}, "roomAdministrator": {"Title": "مسؤول غرفة العبادة", "foundingMembersLessThan70Percent": "مسؤول غرفة العبادة الذين يحملون جنسية الدولة أقل من (70%)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "exceptionReasonAr": "سبب الاستثناء (عربي)", "foundingMembersLessThanSeven": "يقل عدد  مسؤول غرفة العبادة عن (7) سبعة أعضاء", "foundingMembersLessThanSevenHint": "يجب عليك إضافة ثلاثة (3)  مسؤول غرفة العبادة على الأقل للمتابعة", "numberOfFounders": "عدد مسؤول غرفة العبادة", "founderEmiratesID": "رقم الهوية", "founderDateOfBirth": "تاريخ الميلاد", "addFoundingMember": "إضافة مسؤول غرفة العبادة", "FoundingMember": "مسؤول غرفة العبادة", "founderNameEnglish": "اسم مسؤول غرفة العبادة (انجليزي)", "founderNameArabic": "اسم مسؤول غرفة العبادة (العربية)", "founderEmail": "الب<PERSON>يد الإلكتروني", "founderMobileNumber": "رقم الهات<PERSON> المحمول", "founderEmirate": "الإمارة", "founderPassportNumber": "رقم الجواز", "founderResidencyIssuanceDate": "تاريخ إصدار الإقامة", "founderResidencyExpiryDate": "تاريخ انتهاء الإقامة", "founderAcademicQualification": "المؤهل العلمي", "founderJobTitle": "المسمى الوظيفي", "founderEmployer": "جهة العمل", "founderPassportPhoto": "صورة عن جواز السفر", "founderPersonalPhoto": "صورة شخصية", "foundingMembersList": "قائمة مسؤول غرفة العبادة", "removeFoundingMember": "حذف مسؤول غرفة العبادة", "foundersMeetingPlace": "المكان", "foundersMeetingEmirate": "الامارة", "founderDecisionDate": "تاريخ قرار التأسيس", "foundersMeetingDate": "التاريخ والوقت", "agendaItems": "محضر الاجتماع", "foundersMeetingAgenda": "أعمال اجتماع", "emiratesId": "رقم الهوية الإماراتية", "editFoundingMember": "تعديل ", "cloneFoundingMember": "نسخ", "dateOfBirth": "تاريخ الميلاد", "actions": "الإجراءات", "id": "رقم", "exceptionCases": "حالات الاستثناء", "foundersMeeting": "اجتماع مسؤول غرفة العبادة", "founderEmiratesId": "رقم الهوية الإماراتية للمؤسس", "founderBOD": "تاريخ الميلاد", "founderNameEn": "الاسم", "roomAdminName": "الاسم", "founderNationality": "الجنسية", "responseDate": "تاريخ الرد", "status": "الحالة", "nationalityType": "نوع الجنسية", "foundingMemberAgeIsLessThan21YearsOld": "عمر مسؤول غرفة العبادة أقل من 21 عامًا", "foundingMemberAgeIsLessThan40YearsOld": "عمر مسؤول غرفة العبادة أقل من 40 عامًا", "foundingMemberResidencyIsLessThan3Years": "إقامة مسؤول غرفة العبادة أقل من 3 سنوات", "foundingMemberResidencyIsLessThan5Years": "إقامة مسؤول غرفة العبادة أقل من 5 سنوات", "foundingMemberResidency": "إقامة هذا مسؤول غرفة العبادة أقل من 5 سنوات، الرجاء إدخال سبب الاستثناء", "foundingMemberHasDiplomaticStatus": "المسؤول غرفة العبادة لديه وضع دبلوماسي", "UAELocal": "الحصول على المعلومات", "percentageText": "نسبة الأعضاء المواطنين هي", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأمسؤول غرفة العبادة عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأمسؤول غرفة العبادة الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأمسؤول غرفة العبادة، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأمسؤول غرفة العبادة.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون المسؤول غرفة العبادة قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في المسؤول غرفة العبادة أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "<PERSON><PERSON><PERSON> حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "تاريخ (الاقدم)", "Date (Newest)": "تاريخ (الاحد<PERSON>)", "addFoundingMembersHelpMessage": "بمجرد رد المسؤول غرفة العبادة على طلب التأكيد، ستتمكن من رؤية اسمه وجنسيته، وكذلك رده وتاريخ الرد.", "minimumNumberOfFoundingMembersHelpMessage": "الحد الأدنى للأعضاء المؤسسين يجب أن لا يقل عن 20 عضو", "minimumAgeOfRoomAdminHelpMessage": "الح<PERSON> الأدنى لعمر مسئول دار العبادة يجب أن لا يقل عن 40 عضو", "foundersMeetingAgendaHelpMessage": "جدول أعمال اجتماع المؤسسين:", "foundersMeetingAgendaHelpMessage1": "مناقشة إنشاء دار عبادة لغير المسلمين", "foundersMeetingAgendaHelpMessage2": "اعتماد النظام الأساسي.", "foundersMeetingAgendaHelpMessage3": "انتخاب أعضاء اللجنة المؤقتة.", "foundingMemberAgeLessThan21": "عمر المسؤول غرفة العبادة أقل من 21 عامًا، الرجاء إدخال سبب الاستثناء", "foundingMemberAgeLessThan40": "عمر المسؤول غرفة العبادة أقل من 40 عامًا، الرجاء إدخال سبب الاستثناء", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "مناقشة إنشاء دار عبادة لغير المسلمين", "PreparingTheDraftStatute": "اعتماد النظام الأساسي", "ElectionOfMembersOfTheTemporaryCommittee": "أعضاء اللجنة المؤقتة", "editNPO": "تعديل الرقم الموحد لمؤسسة النفع العام", "cloneNPO": "نسخ الرقم الموحد لمؤسسة النفع العام", "removeNPO": "حذف الرق<PERSON> الموحد لمؤسسة النفع العام", "npoName": "اسم مؤسسة النفع العام", "npoContactDetails": "تفاصيل الاتصال بمؤسسة النفع العام", "associationsNationalSocieties": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "npoEstablishmentNameEN": "اسم المؤسسة (إنجليزي)", "npoEstablishmentNameAr": "اسم المؤسسة (عربي)", "npoEstablishmentDate": "تاريخ التأسيس", "npoEstablishmentLegalFormEN": "الشكل القانوني (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني (عربي)", "AccountId": "معر<PERSON> الح<PERSON>اب", "associationsOrNationalSocietiesFromTheUnionList": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "npoUnifiedNumber": "الرقم الموحد", "addNPOUnifiedNumber": "إضافة الرقم الموحد لمؤسسة النفع العام", "npoUnifiedNumberOrNPOName": "الاسم او الرقم الموحد لمؤسسة النفع العام", "socialSolidarityFundsHelpMessage": "لا يقل عدد الأعضاء المنتسبين للصندوق عند الإنشاء عن (25) عضو", "unionFoundersMeetingHelpMessage": "لتتمكن من تقديم طلب ترخيص واشهار اتحاد، يجب عليك إضافة عدد لا يقل عن (5) جمعيات أو مؤسسات أهلية.  لا يجوز الجمع بين الجمعيات والمؤسسات الأهلية عند تكوين الإتحاد.", "ByLawApproval": "اعتماد النظام الأساسي"}, "adminJobDetails": {"title": "تفاصيل وظيفة المسؤول عن غرفة العبادة", "airportDepartment": "الإدارة التابع لها المسؤول في المطار", "jobTitle": "المسمى الوظيفي للمسؤول", "jobAppointmentDate": "تاريخ تعيين المسؤول في المطار", "letterOfAuthorization": "كتاب تفويض بتمثيل الجهة التي تتبع لها غرفة العبادة"}, "foundingMembers": {"title": "الأعضاء المؤسسون", "foundingMembersLessThan70Percent": "الأعضاء المؤسسون الذين يحملون جنسية الدولة أقل من (70%)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "exceptionReasonAr": "سبب الاستثناء (عربي)", "foundingMembersLessThanSeven": "يقل عدد الأعضاء المؤسسين عن (7) سبعة أعضاء", "foundingMembersLessThanSevenHint": "يجب عليك إضافة ثلاثة (3) أعضاء مؤسسين على الأقل للمتابعة", "numberOfFounders": "عدد المؤسسين", "founderEmiratesID": "رقم الهوية", "founderDateOfBirth": "تاريخ الميلاد", "addFoundingMember": "إضافة عضو مؤسس", "FoundingMember": "الأعضاء المؤسسين", "founderNameEnglish": "اسم المؤسس (انجليزي)", "founderNameArabic": "اسم المؤسس (العربية)", "founderEmail": "الب<PERSON>يد الإلكتروني", "founderMobileNumber": "رقم الهات<PERSON> المتحرك", "founderEmirate": "الإمارة", "founderPassportNumber": "رقم الجواز", "founderResidencyIssuanceDate": "تاريخ إصدار الإقامة", "founderResidencyExpiryDate": "تاريخ انتهاء الإقامة", "founderAcademicQualification": "المؤهل العلمي", "founderJobTitle": "المسمى الوظيفي", "founderEmployer": "جهة العمل", "founderPassportPhoto": "صورة عن جواز السفر", "founderPersonalPhoto": "صورة شخصية", "foundingMembersList": "قائمة الأعضاء المؤسسين", "removeFoundingMember": "حذف عضو مؤسس", "foundersMeetingPlace": "المكان", "foundersMeetingEmirate": "الامارة", "founderDecisionDate": "تاريخ قرار التأسيس", "foundersMeetingDate": "التاريخ والوقت", "agendaItems": "محضر الاجتماع", "foundersMeetingAgenda": "أعمال اجتماع", "emiratesId": "رقم الهوية الإماراتية", "editFoundingMember": "تعديل ", "cloneFoundingMember": "نسخ", "dateOfBirth": "تاريخ الميلاد", "actions": "الإجراءات", "id": "رقم", "exceptionCases": "حالات الاستثناء", "foundersMeeting": "اجتماع المؤسسين", "founderEmiratesId": "رقم الهوية الإماراتية للمؤسس", "founderBOD": "تاريخ الميلاد", "founderNameEn": "الاسم", "founderNationality": "الجنسية", "responseDate": "تاريخ الرد", "status": "الحالة", "nationalityType": "نوع الجنسية", "foundingMemberAgeIsLessThan21YearsOld": "عمر العضو المؤسس أقل من 21 عامًا", "foundingMemberAgeIsLessThan40YearsOld": "عمر العضو المؤسس أقل من 40 عامًا", "foundingMemberResidencyIsLessThan3Years": "إقامة العضو المؤسس أقل من 3 سنوات", "foundingMemberResidencyIsLessThan5Years": "إقامة العضو المؤسس أقل من 5 سنوات", "foundingMemberResidency": "إقامة هذا العضو المؤسس أقل من 5 سنوات، الرجاء إدخال سبب الاستثناء", "foundingMemberHasDiplomaticStatus": "العضو المؤسس لديه وضع دبلوماسي", "UAELocal": "الحصول على المعلومات", "percentageText": "نسبة الأعضاء المواطنين هي", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "<PERSON><PERSON><PERSON> حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "تاريخ (الاقدم)", "Date (Newest)": "تاريخ (الاحد<PERSON>)", "addFoundingMembersHelpMessage": "بمجرد رد العضو المؤسس على طلب التأكيد، ستتمكن من رؤية اسمه وجنسيته، وكذلك رده وتاريخ الرد.", "minimumNumberOfFoundingMembersHelpMessage": "الحد الأدنى للأعضاء المؤسسين يجب أن لا يقل عن 20 عضو", "minimumAgeOfFoundingMembersHelpMessage": "يجب ألا يقل عمر الأعضاء المؤسسين عن 40 عامًا", "foundersMeetingAgendaHelpMessage": "جدول أعمال اجتماع المؤسسين:", "foundersMeetingAgendaHelpMessage1": "مناقشة إنشاء دار عبادة لغير المسلمين", "foundersMeetingAgendaHelpMessage2": "اعتماد النظام الأساسي.", "foundersMeetingAgendaHelpMessage3": "انتخاب أعضاء اللجنة المؤقتة.", "foundingMemberAgeLessThan21": "عمر العضو المؤسس أقل من 21 عامًا، الرجاء إدخال سبب الاستثناء", "foundingMemberAgeLessThan40": "عمر العضو المؤسس أقل من 40 عامًا، الرجاء إدخال سبب الاستثناء", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "مناقشة إنشاء دار عبادة لغير المسلمين", "PreparingTheDraftStatute": "اعتماد النظام الأساسي", "ElectionOfMembersOfTheTemporaryCommittee": "أعضاء اللجنة المؤقتة", "editNPO": "تعديل الرقم الموحد لمؤسسة النفع العام", "cloneNPO": "نسخ الرقم الموحد لمؤسسة النفع العام", "removeNPO": "حذف الرق<PERSON> الموحد لمؤسسة النفع العام", "npoName": "اسم مؤسسة النفع العام", "npoContactDetails": "تفاصيل الاتصال بمؤسسة النفع العام", "associationsNationalSocieties": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "npoEstablishmentNameEN": "اسم المؤسسة (إنجليزي)", "npoEstablishmentNameAr": "اسم المؤسسة (عربي)", "npoEstablishmentDate": "تاريخ التأسيس", "npoEstablishmentLegalFormEN": "الشكل القانوني (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني (عربي)", "AccountId": "معر<PERSON> الح<PERSON>اب", "associationsOrNationalSocietiesFromTheUnionList": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "npoUnifiedNumber": "الرقم الموحد", "addNPOUnifiedNumber": "إضافة الرقم الموحد لمؤسسة النفع العام", "npoUnifiedNumberOrNPOName": "الاسم او الرقم الموحد لمؤسسة النفع العام", "socialSolidarityFundsHelpMessage": "لا يقل عدد الأعضاء المنتسبين للصندوق عند الإنشاء عن (25) عضو", "unionFoundersMeetingHelpMessage": "لتتمكن من تقديم طلب ترخيص واشهار اتحاد، يجب عليك إضافة عدد لا يقل عن (5) جمعيات أو مؤسسات أهلية.  لا يجوز الجمع بين الجمعيات والمؤسسات الأهلية عند تكوين الإتحاد.", "ByLawApproval": "اعتماد النظام الأساسي"}, "interimCommittee": {"Id": "رقم", "temporaryCommitteeMembersMeetingPlace": "مكان الاجتماع", "temporaryCommitteeMembersMeetingEmirate": "الإمارة", "temporaryCommitteeMembersMeetingDateTime": "التاريخ والوقت", "temporaryCommitteeMember": "عضو اللجنة المؤقتة", "temporaryCommitteeMemberPosition": "المنصب", "addCommitteeMember": "إضافة عضو لجنة مؤقتة", "committeeMemberList": "قائمة أعضاء اللجنة", "removeTemporaryCommitteeMember": "حذف عضو لجنة مؤقتة", "Title": "اللجنة المؤقتة", "Actions": "الإجراءات", "editMember": "تعديل", "cloneMember": "نسح", "agendaItems": "محضر الاجتماع", "removeMember": "<PERSON><PERSON><PERSON>", "interimCommitteeMembers": "أعضاء اللجنة المؤقتة", "interimCommitteeInformation": "معلومات اجتماع اللجنة المؤقتة", "temporaryCommitteeMemberEID": "رقم الهوية", "temporaryCommitteeMemberName": "الاسم", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة.", "searchPlaceholder": "اكتب كلمات البحث ...", "Sort by": "الترتيب حسب", "Name (A-Z)": "اسم (أ - ي)", "Name (Z-A)": "اسم (ي - أ)", "Date (Oldest)": "التاريخ (الأقدم)", "Date (Newest)": "التاريخ (الأحدث)", "interimCommitteeMembersHelpMessage": "لا يجوز أن يقل عدد أعضاء اللجنة المؤقتة عن ثلاثة أعضاء", "chairmanRequiredHelpMessage": "يجب أن يكون هناك على الاقل عضو واحد فقط بمنصب رئيس اللجنة", "interimCommitteeInformationHelpMessage": "محضر الاجتماع", "interimCommitteeInformationHelpMessage1": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة", "interimCommitteeInformationHelpMessage2": "تعيين مفوض اللجنة المؤقتة.", "Definetheadministrativepositions": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة", "AppointCommiteeRep": "تعيين مفوض اللجنة المؤقتة", "Appointthecommissioner": "تعيين مفوض اللجنة المؤقتة", "status": "الحالة"}, "membership": {"title": "معلومات العضوية", "id": "رقم", "membershipFees": "رسوم العضوية", "membershipConditionEn": "شرط العضوية (بالإنجليزية)", "membershipConditionAr": "شرط العضوية (بالعربية)", "addCondition": "إضافة شروط العضوية", "membershipConditionsList": "قائمة أنواع و شروط العضوية", "Actions": "الإجراءات", "cancel": "إلغاء", "editCondition": "تعديل ", "cloneCondition": "نسخ", "removeCondition": "<PERSON><PERSON><PERSON> ", "membershipInformation": "معلومات العضوية", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة.", "status": "الحالة"}, "boardInformation": {"Id": "رقم", "Title": " معلومات المجلس", "Actions": "الإجراءات", "boardMembersExceed11": "عد<PERSON> أعضاء المجلس يتجاوز 11 عضوا", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "exceptionReasonAr": "سبب الاستثناء (عربي)", "numberOfBoardMembers": "ع<PERSON><PERSON> أعضاء المجلس", "frequencyOfBoardMeetings": "دورية انعقاد اجتماعات المجلس", "BoardElectionCycle": "دورية انتخاب المجلس", "boardMemberReNomination": "هل يجوز إعادة انتخاب أعضاء المجلس لفترة أخرى؟", "numberOfPermissibleTerms": "عد<PERSON> الفترات المسموح بها", "localBoardMembersPercentage": "نسبة أعضاء المجلس من المواطنين", "electionMethod": "طريقة الانتخاب", "nominationConditionEnglish": "الشرط (انجليزي)", "nominationConditionArabic": "الشرط (عربي)", "addNominationCondition": "إضافة شرط", "nominationConditionsList": "قائمة شروط الترشح لعضوية المجلس", "removeNominationCondition": "حذ<PERSON> شر<PERSON>", "adminPositionTitleEnglish": "المنصب الإداري (بالانجليزية)", "adminPositionTitleArabic": "المنصب الإداري (بالعربية)", "addPosition": "إضافة منصب", "boardAdminPositionsList": "قائمة المناصب الإدارية للمجلس", "editCondition": "تعديل ", "cloneCondition": "نسخ", "removeCondition": "<PERSON><PERSON><PERSON> ", "editPosition": "تعديل ", "clonePosition": "نسخ", "removePosition": "<PERSON><PERSON><PERSON> ", "exceptionRequests": "طلبات الاستثناء", "boardOfDirectorsInformation": "معلومات المجلس", "boardOfDirectorsConditions": "الشروط الواجب توافرها في أعضاء المجلس", "boardOfDirectorsPositions": "المناصب الإدارية", "help": "المساعدة", "status": "الحالة", "mustBeFiveMembersAtLeast": "يجب أن لا يكون عدد أعضاء مجلس الإدارة أقل من 5"}, "uploadDocuments": {"title": "المستندات", "uploadDocuments": "إرفاق المستندات", "SectionTitle1": "المستندات المطلوبة للموافقة المبدئية ", "SectionTitle2": "المستندات المطلوبة للموافقة النهائية ", "logoEn": "شعار دار عبادة لغير المسلمين (بالإنجليزية)", "logoAr": "شعار دار عبادة لغير المسلمين (بالعربية)", "npoLogo": "شعار دار عبادة لغير المسلمين", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "requiredDocuments": "المستندات المطلوبة للموافقة المبدئية", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار دار عبادة لغير المسلمين في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة."}, "initialApprovalExtension": {"ReasonDetailsEnPopup": "تفاصيل سبب التمديد (الإنجليزية)", "ReasonDetailsArPopup": "تفاصيل سبب التمديد (العربية)", "ReasonDetailsAr": "سبب طلب التمديد (عربي)", "ReasonDetailsEn": "سبب طلب التمديد (إنجليزي)", "Id": "المعرف", "Title": "تمديد الموافقة المبدئية", "Actions": "الإجراءات", "initialApprovalExtensionToolTip": "تمديدات الموافقة المبدئية لمكان العبادة", "editInitialApprovalExtension": "تعديل", "cloneInitialApprovalExtension": "نسخ", "removeInitialApproval": "إزالة", "initialApprovalExtensions": "تمديد الموافقة المبدئية", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة.", "searchPlaceholder": "اكتب كلمة البحث ...", "Sort by": "الترتيب حسب", "Name (A-Z)": "الاسم (أ-ي)", "Name (Z-A)": "الاسم (ي-أ)", "Date (Oldest)": "التاريخ (الأقدم)", "Date (Newest)": "التاريخ (الأحدث)", "extensionRequestReasonDetails": "سبب طلب التمديد", "reasonTitle": "عنوان السبب", "ExtensionRequestReason": "سبب طلب التمديد", "ReferenceNumber": "رق<PERSON> الطلب", "RequestDate": "تاريخ الطلب", "Status": "حالة الطلب", "AddExtensionRequest": "إضافة طلب تمديد", "UpdateExtensionRequest": "تعديل طلب التمديد"}, "reviewAndSubmit": {"roomAdminName": "الاسم", "adminJobDetails": "تفاصيل وظيفة المسؤول عن غرفة العبادة", "airportDepartment": "الإدارة التابع لها المسؤول في المطار", "jobTitle": "المسمى الوظيفي للمسؤول", "jobAppointmentDate": "تاريخ تعيين المسؤول في المطار", "letterOfAuthorization": "كتاب تفويض بتمثيل الجهة التي تتبع لها غرفة العبادة", "mobileNumber": "رقم الجوال", "title": "مراجعة وتقديم الطلب", "BasicInformation": "المعلومات الأساسية", "worshipPlaceName": "اسم دار العبادة", "worshipPlaceInformation": "معلومات دار العبادة", "parentWorshipPlaceInfo": "معلومات دار العبادة الأم", "religion": "الديانة", "sect": "الطائفة", "belief": "العقيدة", "EmiratesId": "رقم الهوية ", "NameEn": "الاسم (الإنجليزية)", "NameAr": "الا<PERSON><PERSON> (عربي)", "Name": "الاسم", "RoomAdministrator": "مسؤول غرفة العبادة", "worshipRoomName": "اسم غرفة العبادة", "worshipRoomInfo": "معلومات غرفة العبادة", "worshipRoomLocationType": "نوع مقر غرفة العبادة", "worshipRoomLocation": "مقر غرفة العبادة", "worshipRoomDetails": "تفاصيل غرفة العبادة", "brief": "نبذة عن الديانة أو الطائفة أو العقيدة", "religiousPractices": "الممارسات والعبادات والشعائر  والطقوس الدينية", "programTimings": "برنامج ومواقيت إقامة العبادات والشعائر  والطقوس الدينية", "controlsAndProcedures": "ضوابط وإجراءات إقامة العبادات والشعائر والطقوس الدينية", "worshipPlaceType": "نوع دار العبادة", "typeName": "اسم النوع", "parentHouseOfWorshipPlaceNameEn": "اسم دار العبادة الأم (انجليزي)", "parentHouseOfWorshipPlaceNameAr": "اسم دار العبادة الأم (عربي)", "country": "الدولة", "proposedNameEn": "الاسم المقترح (انجليزي)", "proposedNameAr": "الاسم المقترح (عربي)", "landlineNumber": "رقم الهاتف الثابت", "poBox": "صندوق البريد", "email": "الب<PERSON>يد الإلكتروني", "website": "الموقع الإلكتروني", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "emirate": "الإمارة", "contactDetails": "تفاصيل الاتصال", "status": "الحالة", "PurposesAndActivitiesEn": "الغرض والنشاط (انجليزي)", "PurposesAndActivitiesAr": "الغرض والنشاط (عربي)", "PurposesAndActivities": "أغراض وأنشطة", "foundingMembers": "الأعضاء المؤسسون", "nationalityType": "نوع الجنسية", "foundingMemberAgeIsLessThan40YearsOld": "عمر العضو المؤسس أقل من 40 سنة", "founderEmiratesID": "رقم الهوية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (انجليزي)", "exceptionReasonEn": "سبب الاستثناء (عربي)", "MeetingPlace": "مكان الاجتماع", "MeetingDate": "التاريخ والوقت", "agendaItems": "محضر الاجتماع", "founderNameEn": "اسم المؤسس (انجليزي)", "founderNameAr": "اسم المؤسس (العربية)", "nationality": "الجنسية", "responseDate": "تاريخ الرد", "foundersMeeting": "اجتماع المؤسسين", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "مناقشة إنشاء دار عبادة لغير المسلمين", "ByLawApproval": "اعتماد النظام الأساسي", "ElectionOfMembersOfTheTemporaryCommittee": "أعضاء اللجنة المؤقتة", "Actions": "الإجراءات", "temporaryCommitteeMembersMeetingPlace": "مكان الاجتماع", "temporaryCommitteeMembersMeetingEmirate": "الإمارة", "temporaryCommitteeMembersMeetingDateTime": "التاريخ والوقت", "temporaryCommitteeMember": "عضو لجنة مؤقتة", "committeeMemberList": "قائمة أعضاء اللجنة", "interimCommittee": "اللجنة المؤقتة", "interimCommitteeMembers": "أعضاء اللجنة المؤقتة", "interimCommitteeInformation": "معلومات اجتماع اللجنة المؤقتة", "temporaryCommitteeMemberEID": "رقم هوية عضو اللجنة المؤقتة", "temporaryCommitteeMemberName": "اسم عضو اللجنة المؤقتة", "temporaryCommitteeMemberPosition": "منصب عضو اللجنة المؤقتة", "Definetheadministrativepositions": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة", "AppointCommiteeRep": "تعيين مفوض اللجنة المؤقتة", "membership": "معلومات العضوية", "membershipFees": "رسوم العضوية", "membershipConditionEn": "شرط العضوية (بالإنجليزية)", "membershipConditionAr": "شرط العضوية (بالعربية)", "membershipConditionsList": "قائمة أنواع و شروط العضوية", "membershipInformation": "معلومات العضوية", "membershipConditions": "أنواع و شروط العضوية", "boardInformation": "معلومات المجلس", "numberOfBoardMembers": "ع<PERSON><PERSON> أعضاء المجلس", "frequencyOfBoardMeetings": "دورية انعقاد اجتماعات المجلس", "BoardElectionCycle": "دورية انتخاب المجلس", "boardMemberReNomination": "هل يجوز إعادة ترشيح أعضاء مجلس الإدارة لفترة أخرى؟", "NumberOfPermissibleTerms": "عد<PERSON> الفترات المسموح بها", "electionMethod": "طريقة الانتخاب", "nominationConditionEnglish": "شرط الترشيح (إنجليزي)", "nominationConditionArabic": "شرط الترشيح (عربي)", "nominationConditionsList": "قائمة شروط المجلس", "adminPositionTitleEnglish": "مسمى الوظيفة الإدارية (إنجليزي)", "adminPositionTitleArabic": "مسمى الوظيفة الإدارية (عربي)", "boardAdminPositionsList": "قائمة المناصب الإدارية للمجلس", "exceptionRequests": "طلبات الاستثناء", "boardConditions": "شروط المجلس", "boardPositions": "المناصب الإدارية", "requiredDocuments": "المستندات المطلوبة للموافقة المبدئية", "uploadDocuments": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "initialApprovalExtension": "تمديد الموافقة المبدئية", "ReasonDetailsEn": "تفاصيل سبب التمديد (الإنجليزية)", "ReasonDetailsAr": "تفاصيل سبب التمديد (العربية)", "initialApprovalExtensions": "تمديد الموافقة المبدئية", "extensionRequestReasonDetails": "تفاصيل سبب التمديد", "extensionRequestReasonEn": "سبب طلب التمديد (إنجليزي)", "extensionRequestReasonAr": "سبب طلب التمديد (عربي)", "extensionRequestsList": "قائمة طلبات التمديد", "referenceNumber": "رق<PERSON> الطلب", "requestDate": "تاريخ الطلب", "requestStatus": "حالة الطلب", "downloadBylaws": "تحميل النظام الأساسي", "getFoundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "localBoardMembersPercentage": "نسبة أعضاء المجلس المحلي", "CanBeRenominated": "يمكن إعادة ترشيحه", "FrequencyOfMonthlyBoardMeetings": "عد<PERSON> الاجتماعات الشهرية لمجلس الإدارة", "ElectionMethod": "طريقة الانتخاب", "memberPosition": "منصب عضو مجلس الأمناء", "Date": "التاريخ", "exceptionCases": "حالات الاستثناء", "isFoundingMemberAgeLessThan40": "هل عمر العضو المؤسس أقل من 40 عامًا، الرجاء إدخال سبب الاستثناء ؟", "ministryComments": "ملاحظات الوزارة", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "npoLegalForm": "الشكل القانوني", "localDecreeLawNumber": "رقم المرسوم المحلي", "issuanceDate": "تاريخ الإصدار", "associationClassification": "تصنيف الجمعية", "npoName": "اسم الجمعية", "npoContactDetails": "تفاصيل الاتصال بالجمعية", "Landline": "الهات<PERSON> الثابت", "POBox": "ص.ب", "FaxNumber": "رقم الفاكس", "Email": "الب<PERSON>يد الإلكتروني", "Website": "الموقع الإلكتروني", "Address": "العنوان", "GeographicLocation": "الموقع الجغرافي", "Emirate": "الإمارة", "permanentAdvance": "المبلغ المخصص", "fundsAllocation": "توزيع الأموال", "fundsAllocationAmount": "مبلغ توزيع الأموال", "associationsNationalSocieties": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "ObjectiveEn": "الهدف (بالإنجليزية)", "ObjectiveAr": "الهدف (بالعربية)", "MeansOfAchievingObjectiveEn": "وسائل تحقيق الهدف (بالإنجليزية)", "MeansOfAchievingObjectiveAr": "وسائل تحقيق الهدف (بالعربية)", "Objectives": "الأهداف", "boardOfDirectors": "مجلس الإدارة", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "هل الأعضاء المؤسسين الذين يحملون جنسية الدولة أقل من (70%)؟", "ExceptionReasonFor70En": "سبب الاستثناء (بالإنجليزية)", "ExceptionReasonFor70Ar": "سبب الاستثناء (بالعربية)", "IsNumberOfFoundingMembersIsLessThan7Members": "ه<PERSON> <PERSON><PERSON>د الأعضاء المؤسسين أقل من 7؟", "foundingMemberAgeIsLessThan21YearsOld": "هل عمر العضو المؤسس أقل من 21 عامًا؟", "foundingMemberResidencyIsLessThan3Years": "هل إقامة العضو المؤسس أقل من 3 سنوات؟", "foundingMemberHasDiplomaticStatus": "هل للعضو المؤسس وضع دبلوماسي؟", "boardOfTrustees": "مجلس الأمناء", "Agenda": "جدول الأعمال", "MembershipFees": "رسوم العضوية", "AnnualMembershipDueDate": "تاريخ استحقاق العضوية السنوية", "EnrollmentFees": "رسوم التسجيل", "boardOfDirectorsInformation": "معلومات مجلس الإدارة", "boardOfDirectorsConditions": "الشروط الواجب توافرها في أعضاء المجلس", "boardOfDirectorsPositions": "المناصب الإدارية", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "foundingMember": "عضو مؤسس", "administrativePositionTitleAr": "عنوان المنصب الإداري (بالعربية)", "administrativePositionTitleEn": "عنوان المنصب الإداري (بالإنجليزية)", "conditionForNominationAr": "شروط الترشيح (بالعربية)", "conditionForNominationEn": "شروط الترشيح (بالإنجليزية)", "frequencyOfMeetings": "دورية انعقاد اجتماعات مجلس الأمناء", "numberOfMembers": "ع<PERSON><PERSON> الأعضاء", "frequencyOfAppointments": "دورية تعيين مجلس الأمناء", "boardOfTrusteesInformation": "معلومات مجلس الأمناء", "boardOfTrusteesConditions": "الشروط الواجب توافرها في أعضاء مجلس الأمناء", "boardOfTrusteesPositions": "مناصب مجلس الأمناء", "boardOfTrusteesMembers": "أعضاء مجلس الأمناء", "Means of Achieving Objective": "وسائل تحقيق الهدف", "Id": "رقم", "founderNameEnglish": "اسم المؤسس (انجليزي)", "founderNameArabic": "اسم المؤسس (العربية)", "founderNationality": "الجنسية المؤسس", "AccountId": "معر<PERSON> الح<PERSON>اب", "npoUnifiedNumber": "الرقم الموحد لمؤسسة النفع العام", "npoEstablishmentNameEN": "اسم مؤسسة النفع العام (إنجليزي)", "npoEstablishmentNameAr": "اسم مؤسسة النفع العام (عربي)", "npoEstablishmentDate": "تاريخ تأسيس مؤسسة النفع العام", "npoEstablishmentLegalFormEN": "الشكل القانوني لمؤسسة النفع العام (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني لمؤسسة النفع العام (عربي)", "yes": "نعم", "no": "لا", "targetGroupsAssociation": "الفئات المستهدفة من أنشطة الجمعية", "targetGroupsNationalSociety": "الفئات المستهدفة من أنشطة الجمعية", "addTargetGroup": "إضافة فئة مستهدفة", "targetGroupNameEn": "اسم الفئة المستهدفة (إنجليزي)", "targetGroupNameAr": "اسم الفئة المستهدفة (عربي)", "FundServices": "خدمات الصندوق", "ServiceTitle": "عنوان الخدمة (الإنجليزية)", "ServiceTitleAR": "عنوان الخدمة (عربي)", "Amount": "المبلغ", "PreparingTheDraftStatute": "اعتماد النظام الأساسي", "Appointthecommissioner": "تعيين مفوض اللجنة المؤقتة", "id": "رقم", "AllocationCycle": "دورية التخصيص", "FundsAmount": "قيمة الأموال", "DescriptionOfInKindFundsAr": "وصف الأموال العينية (عربي)", "DescriptionOfInKindFundsEn": "وصف الأموال العينية (انجليزي)", "NatureOfFundsAllocated": "طبيعة الأموال المخصصة", "foundingMemberEID": "العضو المؤسس", "addAllocationOfFoundationFunds": "إضافة تخصيص الأموال", "editAllocationOfFoundationFunds": "تعديل تخصيص الأموال", "help": "مساعدة", "allocationOfFoundationFunds": "تخصيص أموال المؤسسة", "totalFundsAmountIs": "إجمالي مبلغ الأموال هو", "Position": "من<PERSON><PERSON>", "founderDecisionDate": "تاريخ قرار التأسيس", "fundbelongsDetails": "بيانات الجهة التي يتبع لها الصندوق", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (بالإنجليزية)", "EntityNameAr": "اسم الجهة (بالعربية)", "EntityType": " تصنيف الجهة ", "NumberOfEmployees": "<PERSON><PERSON><PERSON> الموظفين", "FundServiceObjectiveEn": "أنشطة وأهداف الصندوق (بالانجليزية)", "FundServiceObjectiveAr": "أنشطة وأهداف الصندوق (بالعربية)", "MeansOfAchievingObjective": "وسائل تحقيق الهدف", "fundServiceObjectives": "أنشطة وأهداف الصندوق", "category": "الفئة", "Status": "الحالة", "geographicalLocationInTheAirport": "الموقع الجغرافي داخل المطار  ", "area": "المساحة (متر مربع) ", "capacity": "الطاقة الاستيعابية ", "worshipRoomDescriptionEn": "وصف الغرفة (الإنجليزية)", "worshipRoomDescriptionAr": "وصف الغرفة (العربية)"}, "requestDetails": {"adminJobDetails": "تفاصيل وظيفة المسؤول عن غرفة العبادة", "airportDepartment": "الإدارة التابع لها المسؤول في المطار", "jobTitle": "المسمى الوظيفي للمسؤول", "jobAppointmentDate": "تاريخ تعيين المسؤول في المطار", "title": "مراجعة وتقديم الطلب", "BasicInformation": "المعلومات الأساسية", "worshipPlaceName": "اسم دار العبادة", "worshipPlaceInformation": "معلومات دار العبادة", "parentWorshipPlaceInfo": "معلومات دار العبادة الأم", "religion": "الديانة", "sect": "الطائفة", "belief": "العقيدة", "EmiratesId": "رقم الهوية ", "NameEn": "الاسم (الإنجليزية)", "NameAr": "الا<PERSON><PERSON> (عربي)", "RoomAdministrator": "مسؤول غرفة العبادة", "worshipRoomName": "اسم غرفة العبادة", "worshipRoomInfo": "معلومات غرفة العبادة", "worshipRoomLocationType": "نوع مقر غرفة العبادة", "worshipRoomLocation": "مقر غرفة العبادة", "worshipRoomDetails": "تفاصيل غرفة العبادة", "brief": "نبذة عن الديانة أو الطائفة أو العقيدة", "religiousPractices": "الممارسات والعبادات والشعائر  والطقوس الدينية", "programTimings": "برنامج ومواقيت إقامة العبادات والشعائر  والطقوس الدينية", "controlsAndProcedures": "ضوابط وإجراءات إقامة العبادات والشعائر والطقوس الدينية", "worshipPlaceType": "نوع دار العبادة", "typeName": "اسم النوع", "parentHouseOfWorshipPlaceNameEn": "اسم دار العبادة الأم (انجليزي)", "parentHouseOfWorshipPlaceNameAr": "اسم دار العبادة الأم (عربي)", "country": "الدولة", "proposedNameEn": "الاسم المقترح (انجليزي)", "proposedNameAr": "الاسم المقترح (عربي)", "landlineNumber": "رقم الهاتف الثابت", "poBox": "صندوق البريد", "email": "الب<PERSON>يد الإلكتروني", "website": "الموقع الإلكتروني", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "emirate": "الإمارة", "contactDetails": "تفاصيل الاتصال", "status": "الحالة", "PurposesAndActivitiesEn": "الغرض والنشاط (انجليزي)", "PurposesAndActivitiesAr": "الغرض والنشاط (عربي)", "PurposesAndActivities": "أغراض وأنشطة", "foundingMembers": "الأعضاء المؤسسون", "nationalityType": "نوع الجنسية", "foundingMemberAgeIsLessThan40YearsOld": "عمر العضو المؤسس أقل من 40 سنة", "founderEmiratesID": "رقم الهوية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (انجليزي)", "exceptionReasonEn": "سبب الاستثناء (عربي)", "MeetingPlace": "مكان الاجتماع", "MeetingDate": "التاريخ والوقت", "agendaItems": "محضر الاجتماع", "founderNameEn": "اسم المؤسس (انجليزي)", "founderNameAr": "اسم المؤسس (العربية)", "nationality": "الجنسية", "responseDate": "تاريخ الرد", "foundersMeeting": "اجتماع المؤسسين", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "مناقشة إنشاء دار عبادة لغير المسلمين", "ByLawApproval": "اعتماد النظام الأساسي", "ElectionOfMembersOfTheTemporaryCommittee": "أعضاء اللجنة المؤقتة", "Actions": "الإجراءات", "temporaryCommitteeMembersMeetingPlace": "مكان الاجتماع", "temporaryCommitteeMembersMeetingEmirate": "الإمارة", "temporaryCommitteeMembersMeetingDateTime": "التاريخ والوقت", "temporaryCommitteeMember": "عضو لجنة مؤقتة", "committeeMemberList": "قائمة أعضاء اللجنة", "interimCommittee": "اللجنة المؤقتة", "interimCommitteeMembers": "أعضاء اللجنة المؤقتة", "interimCommitteeInformation": "معلومات اجتماع اللجنة المؤقتة", "temporaryCommitteeMemberEID": "رقم هوية عضو اللجنة المؤقتة", "temporaryCommitteeMemberName": "اسم عضو اللجنة المؤقتة", "temporaryCommitteeMemberPosition": "منصب عضو اللجنة المؤقتة", "Definetheadministrativepositions": "تحديد المناصب الإدارية لأعضاء اللجنة المؤقتة", "AppointCommiteeRep": "تعيين مفوض اللجنة المؤقتة", "membership": "معلومات العضوية", "membershipFees": "رسوم العضوية", "membershipConditionEn": "شرط العضوية (بالإنجليزية)", "membershipConditionAr": "شرط العضوية (بالعربية)", "membershipConditionsList": "قائمة أنواع و شروط العضوية", "membershipInformation": "معلومات العضوية", "membershipConditions": "أنواع و شروط العضوية", "boardInformation": "معلومات المجلس", "numberOfBoardMembers": "ع<PERSON><PERSON> أعضاء المجلس", "frequencyOfBoardMeetings": "دورية انعقاد اجتماعات المجلس", "BoardElectionCycle": "دورية انتخاب المجلس", "boardMemberReNomination": "هل يجوز إعادة ترشيح أعضاء مجلس الإدارة لفترة أخرى؟", "NumberOfPermissibleTerms": "عد<PERSON> الفترات المسموح بها", "electionMethod": "طريقة الانتخاب", "nominationConditionEnglish": "شرط الترشيح (إنجليزي)", "nominationConditionArabic": "شرط الترشيح (عربي)", "nominationConditionsList": "قائمة شروط المجلس", "adminPositionTitleEnglish": "مسمى الوظيفة الإدارية (إنجليزي)", "adminPositionTitleArabic": "مسمى الوظيفة الإدارية (عربي)", "boardAdminPositionsList": "قائمة المناصب الإدارية للمجلس", "exceptionRequests": "طلبات الاستثناء", "boardConditions": "شروط المجلس", "boardPositions": "المناصب الإدارية", "requiredDocuments": "المستندات المطلوبة للموافقة المبدئية", "uploadDocuments": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "initialApprovalExtension": "تمديد الموافقة المبدئية", "ReasonDetailsEn": "تفاصيل سبب التمديد (الإنجليزية)", "ReasonDetailsAr": "تفاصيل سبب التمديد (العربية)", "initialApprovalExtensions": "تمديد الموافقة المبدئية", "extensionRequestReasonDetails": "تفاصيل سبب التمديد", "extensionRequestReasonEn": "سبب طلب التمديد (إنجليزي)", "extensionRequestReasonAr": "سبب طلب التمديد (عربي)", "extensionRequestsList": "قائمة طلبات التمديد", "referenceNumber": "رق<PERSON> الطلب", "requestDate": "تاريخ الطلب", "requestStatus": "حالة الطلب", "downloadBylaws": "تحميل النظام الأساسي", "getFoundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "localBoardMembersPercentage": "نسبة أعضاء المجلس المحلي", "CanBeRenominated": "يمكن إعادة ترشيحه", "FrequencyOfMonthlyBoardMeetings": "عد<PERSON> الاجتماعات الشهرية لمجلس الإدارة", "ElectionMethod": "طريقة الانتخاب", "memberPosition": "منصب عضو مجلس الأمناء", "Date": "التاريخ", "exceptionCases": "حالات الاستثناء", "isFoundingMemberAgeLessThan40": "هل عمر العضو المؤسس أقل من 40 عامًا، الرجاء إدخال سبب الاستثناء ؟", "ministryComments": "ملاحظات الوزارة", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "npoLegalForm": "الشكل القانوني", "localDecreeLawNumber": "رقم المرسوم المحلي", "issuanceDate": "تاريخ الإصدار", "associationClassification": "تصنيف الجمعية", "npoName": "اسم الجمعية", "npoContactDetails": "تفاصيل الاتصال بالجمعية", "Landline": "الهات<PERSON> الثابت", "POBox": "ص.ب", "FaxNumber": "رقم الفاكس", "Email": "الب<PERSON>يد الإلكتروني", "Website": "الموقع الإلكتروني", "Address": "العنوان", "GeographicLocation": "الموقع الجغرافي", "Emirate": "الإمارة", "permanentAdvance": "المبلغ المخصص", "fundsAllocation": "توزيع الأموال", "fundsAllocationAmount": "مبلغ توزيع الأموال", "associationsNationalSocieties": "قائمة الجمعيات أو المؤسسات الأهلية التي تشكل الاتحاد", "ObjectiveEn": "الهدف (بالإنجليزية)", "ObjectiveAr": "الهدف (بالعربية)", "MeansOfAchievingObjectiveEn": "وسائل تحقيق الهدف (بالإنجليزية)", "MeansOfAchievingObjectiveAr": "وسائل تحقيق الهدف (بالعربية)", "Objectives": "الأهداف", "boardOfDirectors": "مجلس الإدارة", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "هل الأعضاء المؤسسين الذين يحملون جنسية الدولة أقل من (70%)؟", "ExceptionReasonFor70En": "سبب الاستثناء (بالإنجليزية)", "ExceptionReasonFor70Ar": "سبب الاستثناء (بالعربية)", "IsNumberOfFoundingMembersIsLessThan7Members": "ه<PERSON> <PERSON><PERSON>د الأعضاء المؤسسين أقل من 7؟", "foundingMemberAgeIsLessThan21YearsOld": "هل عمر العضو المؤسس أقل من 21 عامًا؟", "foundingMemberResidencyIsLessThan3Years": "هل إقامة العضو المؤسس أقل من 3 سنوات؟", "foundingMemberHasDiplomaticStatus": "هل للعضو المؤسس وضع دبلوماسي؟", "boardOfTrustees": "مجلس الأمناء", "Agenda": "جدول الأعمال", "MembershipFees": "رسوم العضوية", "AnnualMembershipDueDate": "تاريخ استحقاق العضوية السنوية", "EnrollmentFees": "رسوم التسجيل", "boardOfDirectorsInformation": "معلومات مجلس الإدارة", "boardOfDirectorsConditions": "الشروط الواجب توافرها في أعضاء المجلس", "boardOfDirectorsPositions": "المناصب الإدارية", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "foundingMember": "عضو مؤسس", "administrativePositionTitleAr": "عنوان المنصب الإداري (بالعربية)", "administrativePositionTitleEn": "عنوان المنصب الإداري (بالإنجليزية)", "conditionForNominationAr": "شروط الترشيح (بالعربية)", "conditionForNominationEn": "شروط الترشيح (بالإنجليزية)", "frequencyOfMeetings": "دورية انعقاد اجتماعات مجلس الأمناء", "numberOfMembers": "ع<PERSON><PERSON> الأعضاء", "frequencyOfAppointments": "دورية تعيين مجلس الأمناء", "boardOfTrusteesInformation": "معلومات مجلس الأمناء", "boardOfTrusteesConditions": "الشروط الواجب توافرها في أعضاء مجلس الأمناء", "boardOfTrusteesPositions": "مناصب مجلس الأمناء", "boardOfTrusteesMembers": "أعضاء مجلس الأمناء", "Means of Achieving Objective": "وسائل تحقيق الهدف", "Id": "رقم", "founderNameEnglish": "اسم المؤسس (انجليزي)", "founderNameArabic": "اسم المؤسس (العربية)", "founderNationality": "الجنسية المؤسس", "AccountId": "معر<PERSON> الح<PERSON>اب", "npoUnifiedNumber": "الرقم الموحد لمؤسسة النفع العام", "npoEstablishmentNameEN": "اسم مؤسسة النفع العام (إنجليزي)", "npoEstablishmentNameAr": "اسم مؤسسة النفع العام (عربي)", "npoEstablishmentDate": "تاريخ تأسيس مؤسسة النفع العام", "npoEstablishmentLegalFormEN": "الشكل القانوني لمؤسسة النفع العام (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني لمؤسسة النفع العام (عربي)", "yes": "نعم", "no": "لا", "targetGroupsAssociation": "الفئات المستهدفة من أنشطة الجمعية", "targetGroupsNationalSociety": "الفئات المستهدفة من أنشطة الجمعية", "addTargetGroup": "إضافة فئة مستهدفة", "targetGroupNameEn": "اسم الفئة المستهدفة (إنجليزي)", "targetGroupNameAr": "اسم الفئة المستهدفة (عربي)", "FundServices": "خدمات الصندوق", "ServiceTitle": "عنوان الخدمة (الإنجليزية)", "ServiceTitleAR": "عنوان الخدمة (عربي)", "Amount": "المبلغ", "PreparingTheDraftStatute": "اعتماد النظام الأساسي", "Appointthecommissioner": "تعيين مفوض اللجنة المؤقتة", "id": "رقم", "AllocationCycle": "دورية التخصيص", "FundsAmount": "قيمة الأموال", "DescriptionOfInKindFundsAr": "وصف الأموال العينية (عربي)", "DescriptionOfInKindFundsEn": "وصف الأموال العينية (انجليزي)", "NatureOfFundsAllocated": "طبيعة الأموال المخصصة", "foundingMemberEID": "العضو المؤسس", "addAllocationOfFoundationFunds": "إضافة تخصيص الأموال", "editAllocationOfFoundationFunds": "تعديل تخصيص الأموال", "help": "مساعدة", "allocationOfFoundationFunds": "تخصيص أموال المؤسسة", "totalFundsAmountIs": "إجمالي مبلغ الأموال هو", "Position": "من<PERSON><PERSON>", "founderDecisionDate": "تاريخ قرار التأسيس", "fundbelongsDetails": "بيانات الجهة التي يتبع لها الصندوق", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (بالإنجليزية)", "EntityNameAr": "اسم الجهة (بالعربية)", "EntityType": " تصنيف الجهة ", "NumberOfEmployees": "<PERSON><PERSON><PERSON> الموظفين", "FundServiceObjectiveEn": "أنشطة وأهداف الصندوق (بالانجليزية)", "FundServiceObjectiveAr": "أنشطة وأهداف الصندوق (بالعربية)", "MeansOfAchievingObjective": "وسائل تحقيق الهدف", "fundServiceObjectives": "أنشطة وأهداف الصندوق", "category": "الفئة", "ObjectivesEn": "الأهداف (الإنجليزية)", "ObjectivesAR": "الأهداف (العربية)", "MemberIsExceeds11": "<PERSON><PERSON><PERSON> الأعضاء يتجاوز 11", "Objective (English)": "الهدف (باللغة الإنجليزية)", "Objective (Arabic)": "الهدف (باللغة العربية)", "Means of Achieving Objective (English)": "وسائل تحقيق الهدف (باللغة الإنجليزية)", "Means of Achieving Objective (Arabic)": "وسائل تحقيق الهدف (باللغة العربية)", "NPO_LICENSE_DECLARATION": "طلب إنشاء مؤسسات النفع العام", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإشهار", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "COMMENT": "تعليق", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>", "geographicalLocationInTheAirport": "الموقع الجغرافي داخل المطار  ", "area": "المساحة (متر مربع) ", "capacity": "الطاقة الاستيعابية ", "worshipRoomDescriptionEn": "وصف الغرفة (الإنجليزية)", "worshipRoomDescriptionAr": "وصف الغرفة (العربية)", "mobileNumber": "رقم الجوال"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "ص<PERSON><PERSON><PERSON> العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لإصدار ترخيص دار عبادة لغير المسلمين", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "عنوان البريد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لإصدار ترخيص دار عبادة لغير المسلمين", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لإصدار ترخيص دار عبادة لغير المسلمين"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "fundraisingService": {"title": "طلب تصريح جمع التبرعات", "title-extend": "طلب تمديد تصريح جمع التبرعات", "desc": "", "forms": {"legalTypePage": {"title": "طلب تصريح جمع التبرعات", "description": "", "selectNpoLegalForm": "اختر نوع الخدمة"}, "entityDetails": {"title": "بيانات الجهة", "sectionTitle": "بيانات الجهة الطالبة للتصريح", "entityDetailsRequestingThePermit": "بيانات الجهة الطالبة للتصريح", "npoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "issuanceDate": "تاريخ الإصدار", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "EntityNameEn": "اسم الجهة (الإنجليزية)", "EntityNameAr": "اسم الجهة (العربية)", "MainCategory": "التصنيف الرئيسي", "MainActivity": "النشاط الرئيسي", "EntityCategory": "تصنيف الجهة", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "edit": "تعديل", "clone": "نسخ", "remove": "<PERSON><PERSON><PERSON>", "emirate": "الإمارة", "add": "إضافة", "id": "رقم", "actions": "الإجراءات", "status": "الحالة", "Sort by": "<PERSON><PERSON><PERSON> حسب", "Name (A-Z)": "الاسم (أ-ي)", "Name (Z-A)": "الاسم (ي-أ)", "Date (Oldest)": "التاريخ (الأقدم)", "Date (Newest)": "التاريخ (الأحدث)", "help": "المساعدة", "helpMessage": "", "search": "ا<PERSON><PERSON><PERSON>", "accountName": "اختر الجهة"}, "fundraisingSpecifications": {"title": "تفاصيل جمع التبرعات", "newFundraisingPermitException": "سبب الاستثناء لتقديم تصريح جديد لجمع التبرعات (أكثر من 4 تصاريح في السنة)", "exceptionReasonAr": "سبب الاستثناء (بالعربية)", "exceptionReasonEn": "سبب الاستثناء (بالإنجليزية)", "purposeOfFundraising": "تفاصيل جمع التبرعات", "purposeOfFundraisingEn": "الغرض من جمع التبرعات  (الإنجليزية)", "purposeOfFundraisingAr": "الغرض من جمع التبرعات (العربية)", "fundraisingDuration": "مدة جمع التبرعات", "fundraisingStartDate": "تاريخ بدء جمع التبرعات", "fundraisingEndDate": "تاريخ انتهاء جمع التبرعات", "fundraisingLocations": "قائمة أماكن جمع التبرعات", "fundraisingLocation": "مكان جمع التبرعات", "addFundraisingLocation": "إضافة موقع لجمع التبرعات", "editCashFundraisingType": "تعديل نوع التبرع النقدي", "editCashCollectionMethod": "تعديل وسيلة التبرع النقدي", "editInkindFundraising": "تعديل وسيلة التبرع العينية", "addLocation": "إضافة الموقع", "editFundraisingLocation": "تعديل مكان جمع التبرعات", "fundraisingEmirate": "إمارة جمع التبرعات", "fundraisingAddress": "عنوان جمع التبرعات", "fundraisingResources": "مصادر جمع التبرعات", "fundraisingType": "نوع التبرعات", "fundraisingTargetAmount": "المبلغ المستهدف", "fundraisingTargetAmount(AED)": "المبلغ المستهدف بالدرهم الإماراتي", "cashFundraisingTypes": "انواع التبرعات النقدية", "methodsOfCollectingCashDonations": "وسائل جمع التبرعات النقدية", "addCashFundraisingType": "إضافة نوع تبرع نقدي", "addInkindFundraisingType": "إضافة نوع تبرع عيني", "addNewCashCollectionMethod": "إضافة وسائل جمع التبرعات النقدية", "typeOfCashDonations": "نوع التبرعات النقدية", "typeOfCashDonationsEn": "نوع التبرعات النقدية (بالانجليزية)", "typeOfCashDonationsAr": "نوع التبرعات النقدية (بالعربية)", "currencyType": "نوع العملة", "approximateValue(AED)": "المبلغ التقريبي بالدرهم الإماراتي", "Actions": "الإجراءات", "select": "Select", "cashCollectionMethod": "اختر وسيلة جمع التبرعات النقدية", "cashCollectionMethodEn": "وسيلة جمع التبرعات النقدية (بالانجليزية)", "cashCollectionMethodAr": "وسيلة جمع التبرعات النقدية (بالعربية)", "descriptionEn": "الوصف (بالانجليزية)", "descriptionAr": "الوصف (بالعربية)", "inKindFundraisingTypes": "انواع التبرعات العينية", "typeOfInkindDonations": "نوع التبرعات العينية", "typeOfInkindDonationsEn": "نوع التبرعات العينية (بالانجليزية)", "typeOfInkindDonationsAr": "نوع التبرعات العينية (بالعربية)", "methodsOfCollectingInkindDonations": "وسائل جمع التبرعات العينية", "countOrQuantity": "الكمية/العدد", "targetAmountError": "يجب أن تكون القيم التقريبية مساوية للمبلغ المستهدف المضاف."}, "npoAssignee": {"title": "بيانات الجمعية الخيرية المسؤولة عن جمع التبرعات", "selectNpoLicensedForFundraising": "اختر اختر الجمعية الخيرية المسؤولة عن جمع التبرعات", "npoLicensedForFundraising": "اختر الجمعية الخيرية المسؤولة عن جمع التبرعات", "percentageOfAdminAndOperatingExpenses": "نسبة النفقات الإدارية والتشغيلية", "enterPercentageOfAdminAndOperatingExpenses": "ادخل نسبة النفقات الإدارية والتشغيلية", "NpoNameEn": "اسم الجمعية (الإنجليزية)", "NpoNameAr": "اسم الجمعية (العربية)", "NpoLocation": "مقر الجمعية", "licensingEntity": "جهة الترخيص", "licenseValidity": "صلاحية الترخيص", "startDate": "من تاريخ", "endDate": "تاريخ الانتهاء", "responsiblePersonName": "اسم المسؤول", "responsiblePersonNationality": "الجنسية", "responsiblePersonPhone": "رقم الجوال", "responsiblePersonEmail": "الب<PERSON>يد الإلكتروني"}, "beneficiaryDetails": {"title": "تفاصيل المستفيدين من جمع التبرعات", "donationStartDate": "تقديم التبرعات من تاريخ", "donationEndDate": "تقديم التبرعات حتى تاريخ", "beneficiariesOfFundRaising": "الجهات المستفيدة من التبرعات", "addFundraisingBeneficiary": "إضافة المستفيد من جمع التبرعات", "editBeneficiary": "تعديل المستفيد من جمع التبرعات", "locaion": "المقر", "selectBeneficiaryLocation": "اختر مقر المستفيد", "beneficiaryLocation": "مقر المستفيد", "country": "الدولة", "selectCountry": "اختر الدولة", "beneficiaryCategoryEn": "فئة المستفيد (إنجليزي)", "beneficiaryCategoryAr": "فئة المستفيد (عربي)", "descriptionEn": "الوصف (إنجليزي)", "descriptionAr": "الوصف (عربي)", "addBeneficiary": "إضافة مستفيد", "Actions": "الإجراءات", "status": "الحالة"}, "previousPermits": {"title": "تصاريح جمع التبرعات السابقة", "actions": "الإجراءات", "permitNumber": "رقم التصريح", "establishmentName": "اسم المؤسسة", "legalForm": "الصيغة القانونية", "fundraisingLocation": "مقر جمع التبراعات", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "status": "الحالة", "conditions": "الشروط", "noDataFound": "لايوجد بيانات", "view": "<PERSON><PERSON><PERSON>", "manage": "إدارة"}, "permitExtensionDetails": {"title": "تفاصيل تمديد التصريح", "permitExtensionReasons": "أسباب تمديد التصريح", "permitExtensionReasonEn": "أسباب تمديد التصريح (الإنجليزية)", "permitExtensionReasonAr": "أسباب تمديد التصريح (العربية)", "permitExtensionDuration": "مدة تمديد التصريح", "permitExtensionStartDate": "تاريخ بدء تمديد التصريح", "permitExtensionEndDate": "تاريخ انتهاء تمديد التصريح"}, "appealToRejectionDetails": {"title": "تفاصيل اسباب التظلم", "appealReasons": "اسباب التظلم", "appealReasonEn": "اسباب التظلم (الإنجليزية)", "appealReasonAr": "اسباب التظلم (العربية)"}, "requestHistory": {"title": "سجل الطلب"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "responsibleNpo": "المنظمة غير الربحية المسؤولة عن جمع موافقة جمع التبرعات", "letterCopy": "نسخة من الخطاب الرسمي الموجه إلى الوزارة", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق المستند الذي يحدد المنظمة غير الربحية المسؤولة عن جمع موافقة جمع التبرعات.", "letterCopyMessage": "يمكنك إرفاق نسخة من الخطاب الرسمي الموجه إلى الوزارة."}, "reviewAndSubmit": {"permitExtensionDetails": "تفاصيل تمديد التصريح", "permitExtensionReasons": "أسباب تمديد التصريح", "permitExtensionReasonEn": "أسباب تمديد التصريح (الإنجليزية)", "permitExtensionReasonAr": "أسباب تمديد التصريح (العربية)", "permitExtensionDuration": "مدة تمديد التصريح", "permitExtensionStartDate": "تاريخ بدء تمديد التصريح", "permitExtensionEndDate": "تاريخ انتهاء تمديد التصريح", "appealToRejectionDetails": "تفاصيل اسباب التظلم", "appealReasons": "اسباب التظلم", "appealReasonEn": "اسباب التظلم (الإنجليزية)", "appealReasonAr": "اسباب التظلم (العربية)", "previousPermits": "تصاريح جمع التبرعات السابقة", "permitNumber": "رقم التصريح", "establishmentName": "اسم المؤسسة", "legalForm": "الصيغة القانونية", "fundraisingLocation": "مقر جمع التبراعات", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "actions": "الإجراءات", "conditions": "الشروط", "beneficiaryDetails": "تفاصيل المستفيدين من جمع التبرعات", "donationStartDate": "تقديم التبرعات من تاريخ", "donationEndDate": "تقديم التبرعات حتى تاريخ", "beneficiariesOfFundRaising": "الجهات المستفيدة من التبرعات", "addFundraisingBeneficiary": "إضافة المستفيد من جمع التبرعات", "editBeneficiary": "تعديل المستفيد من جمع التبرعات", "locaion": "المقر", "beneficiaryLocation": "مقر المستفيد", "country": "الدولة", "selectCountry": "اختر الدولة", "beneficiaryCategoryEn": "فئة المستفيد (إنجليزي)", "beneficiaryCategoryAr": "فئة المستفيد (عربي)", "descriptionEn": "الوصف (إنجليزي)", "descriptionAr": "الوصف (عربي)", "addBeneficiary": "إضافة مستفيد", "npoAssignee": "بيانات الجمعية الخيرية المسؤولة عن جمع التبرعات", "selectNpoLicensedForFundraising": "اختر اختر الجمعية الخيرية المسؤولة عن جمع التبرعات", "percentageOfAdminAndOperatingExpenses": "نسبة النفقات الإدارية والتشغيلية", "NpoNameEn": "اسم الجمعية (الإنجليزية)", "NpoNameAr": "اسم الجمعية (العربية)", "NpoLocation": "مقر الجمعية ", "licensingEntity": "جهة الترخيص", "licenseValidity": "صلاحية الترخيص", "responsiblePersonName": "اسم المسؤول", "responsiblePersonNationality": "الجنسية", "responsiblePersonPhone": "رقم الجوال", "responsiblePersonEmail": "الب<PERSON>يد الإلكتروني", "fundraisingSpecifications": "تفاصيل جمع التبرعات", "newFundraisingPermitException": "سبب الاستثناء لتقديم تصريح جديد لجمع التبرعات (أكثر من 4 تصاريح في السنة)", "purposeOfFundraising": " تفاصيل جمع التبرعات", "purposeOfFundraisingEn": "الغرض من جمع التبرعات  (الإنجليزية)", "purposeOfFundraisingAr": "الغرض من جمع التبرعات (العربية)", "fundraisingDuration": "مدة جمع التبرعات", "fundraisingStartDate": "تاريخ بدء جمع التبرعات", "fundraisingEndDate": "تاريخ انتهاء جمع التبرعات", "fundraisingLocations": "قائمة أماكن جمع التبرعات", "addFundraisingLocation": "إضافة موقع لجمع التبرعات", "editFundraisingLocation": "تعديل مكان جمع التبرعات", "fundraisingEmirate": "إمارة جمع التبرعات", "fundraisingAddress": "عنوان جمع التبرعات", "fundraisingResources": "مصادر جمع التبرعات", "fundraisingType": "نوع التبرعات", "fundraisingTargetAmount": "المبلغ المستهدف", "fundraisingTargetAmount(AED)": "المبلغ المستهدف بالدرهم الإماراتي", "cashFundraisingTypes": "انواع التبرعات النقدية", "methodsOfCollectingCashDonations": "وسائل جمع التبرعات النقدية", "typeOfCashDonationsEn": "نوع التبرعات النقدية (بالانجليزية)", "typeOfCashDonationsAr": "نوع التبرعات النقدية (بالعربية)", "currencyType": "نوع العملة", "approximateValue(AED)": "المبلغ التقريبي بالدرهم الإماراتي", "cashCollectionMethodEn": "نوع التبرعات النقدية (بالانجليزية)", "cashCollectionMethodAr": "نوع التبرعات النقدية (بالعربية)", "inKindFundraisingTypes": "انواع التبرعات العينية", "typeOfInkindDonationsEn": "نوع التبرعات العينية (بالانجليزية)", "typeOfInkindDonationsAr": "نوع التبرعات العينية (بالعربية)", "countOrQuantity": "الكمية/العدد", "methodsOfCollectingInkindDonations": "وسائل جمع التبرعات العينية", "entityDetails": "بيانات الجهة", "entityDetailsRequestingThePermit": "بيانات الجهة الطالبة للتصريح", "MainActivity": "النشاط الرئيسي", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "emirate": "الإمارة", "EntityCategory": "تصنيف الجهة", "npoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "issuanceDate": "تاريخ الإصدار", "NameEn": "الاسم (انجليزي)", "NameAr": "الا<PERSON><PERSON> (عربي)", "Landline": "الهات<PERSON> الثابت", "POBox": "ص.ب", "FaxNumber": "رقم الفاكس", "Email": "الب<PERSON>يد الإلكتروني", "Website": "الموقع الإلكتروني", "Address": "العنوان", "Emirate": "الإمارة", "title": "مراجعة وتقديم الطلب", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "nationalityType": "نوع الجنسية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (بالعربية)", "exceptionReasonEn": "سبب الاستثناء (بالإنجليزية)", "exceptionRequests": "طلبات الاستثناء", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "Id": "رقم", "status": "الحالة", "responseDate": "تاريخ الرد", "yes": "نعم", "no": "لا", "id": "رقم", "help": "المساعدة", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (الإنجليزية)", "EntityNameAr": "اسم الجهة (العربية)", "EntityType": " تصنيف الجهة ", "category": "الفئة"}, "requestDetails": {"previousPermits": "تصاريح جمع التبرعات السابقة", "permitNumber": "رقم التصريح", "establishmentName": "اسم المؤسسة", "legalForm": "الصيغة القانونية", "fundraisingLocation": "مقر جمع التبراعات", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "actions": "الإجراءات", "conditions": "الشروط", "beneficiaryDetails": "تفاصيل المستفيدين من جمع التبرعات", "donationStartDate": "تقديم التبرعات من تاريخ", "donationEndDate": "تقديم التبرعات حتى تاريخ", "beneficiariesOfFundRaising": "الجهات المستفيدة من التبرعات", "addFundraisingBeneficiary": "إضافة المستفيد من جمع التبرعات", "editBeneficiary": "تعديل المستفيد من جمع التبرعات", "locaion": "المقر", "beneficiaryLocation": "مقر المستفيد", "country": "الدولة", "selectCountry": "اختر الدولة", "beneficiaryCategoryEn": "فئة المستفيد (إنجليزي)", "beneficiaryCategoryAr": "فئة المستفيد (عربي)", "descriptionEn": "الوصف (إنجليزي)", "descriptionAr": "الوصف (عربي)", "addBeneficiary": "إضافة مستفيد", "npoAssignee": "بيانات الجمعية الخيرية المسؤولة عن جمع التبرعات", "selectNpoLicensedForFundraising": "اختر الجمعية الخيرية المسؤولة عن جمع التبرعات", "percentageOfAdminAndOperatingExpenses": "نسبة النفقات الإدارية والتشغيلية", "NpoNameEn": "اسم الجمعية (الإنجليزية)", "NpoNameAr": "اسم الجمعية (العربية)", "NpoLocation": "مقر الجمعية", "licensingEntity": "جهة الترخيص", "licenseValidity": "صلاحية الترخيص", "responsiblePersonName": "اسم المسؤول", "responsiblePersonNationality": "الجنسية", "responsiblePersonPhone": "رقم الجوال", "responsiblePersonEmail": "الب<PERSON>يد الإلكتروني", "fundraisingSpecifications": "تفاصيل جمع التبرعات", "newFundraisingPermitException": "سبب الاستثناء لتقديم تصريح جديد لجمع التبرعات (أكثر من 4 تصاريح في السنة)", "purposeOfFundraising": "تفاصيل جمع التبرعات", "purposeOfFundraisingEn": "الغرض من جمع التبرعات  (الإنجليزية)", "purposeOfFundraisingAr": "الغرض من جمع التبرعات (العربية)", "fundraisingDuration": "مدة جمع التبرعات", "fundraisingStartDate": "تاريخ بدء جمع التبرعات", "fundraisingEndDate": "تاريخ انتهاء جمع التبرعات", "fundraisingLocations": "قائمة أماكن جمع التبرعات", "addFundraisingLocation": "إضافة موقع لجمع التبرعات", "editFundraisingLocation": "تعديل مكان جمع التبرعات", "fundraisingEmirate": "إمارة جمع التبرعات", "fundraisingAddress": "عنوان جمع التبرعات", "fundraisingResources": "مصادر جمع التبرعات", "fundraisingType": "نوع التبرعات", "fundraisingTargetAmount": "المبلغ المستهدف", "fundraisingTargetAmount(AED)": "المبلغ المستهدف بالدرهم الإماراتي", "cashFundraisingTypes": "انواع التبرعات النقدية", "methodsOfCollectingCashDonations": "وسائل جمع التبرعات النقدية", "typeOfCashDonationsEn": "نوع التبرعات النقدية (بالانجليزية)", "typeOfCashDonationsAr": "نوع التبرعات النقدية (بالعربية)", "currencyType": "نوع العملة", "approximateValue(AED)": "المبلغ التقريبي بالدرهم الإماراتي", "cashCollectionMethodEn": "نوع التبرعات النقدية (بالانجليزية)", "cashCollectionMethodAr": "نوع التبرعات النقدية (بالعربية)", "inKindFundraisingTypes": "انواع التبرعات العينية", "typeOfInkindDonationsEn": "نوع التبرعات العينية (بالانجليزية)", "typeOfInkindDonationsAr": "نوع التبرعات العينية (بالعربية)", "countOrQuantity": "الكمية/العدد", "methodsOfCollectingInkindDonations": "وسائل جمع التبرعات العينية", "entityDetails": "بيانات الجهة", "entityDetailsRequestingThePermit": "بيانات الجهة الطالبة للتصريح", "MainActivity": "النشاط الرئيسي", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "emirate": "الإمارة", "EntityCategory": "تصنيف الجهة", "npoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "issuanceDate": "تاريخ الإصدار", "NameEn": "الاسم (انجليزي)", "NameAr": "الا<PERSON><PERSON> (عربي)", "Landline": "الهات<PERSON> الثابت", "POBox": "ص.ب", "FaxNumber": "رقم الفاكس", "Email": "الب<PERSON>يد الإلكتروني", "Website": "الموقع الإلكتروني", "Address": "العنوان", "Emirate": "الإمارة", "title": "مراجعة وتقديم الطلب", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "nationalityType": "نوع الجنسية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (بالعربية)", "exceptionReasonEn": "سبب الاستثناء (بالإنجليزية)", "exceptionRequests": "طلبات الاستثناء", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "Id": "رقم", "status": "الحالة", "responseDate": "تاريخ الرد", "yes": "نعم", "no": "لا", "id": "رقم", "help": "المساعدة", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (الإنجليزية)", "EntityNameAr": "اسم الجهة (العربية)", "EntityType": " تصنيف الجهة ", "category": "الفئة", "fundraisingService": "إجراءات تصاريح جمع التبرعات", "GeographicLocation": "الموقع الجغرافي", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإشهار", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "COMMENT": "تعليق", "congratulations": "تم تقديم الطلب بنجاح"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "ص<PERSON><PERSON><PERSON> العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لمؤسسة النفع العام", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "عنوان البريد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لمؤسسة النفع العام", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لمؤسسة النفع العام"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "joiningAndAffiliatingAssociationsService": {"title": "طلب تصريح الانتساب أو الاشتراك أو الانضمام إلى الجمعيات والهيئات الإقليمية أو الدولية", "desc": "", "forms": {"entityDetails": {"title": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص"}, "externalEntityDetails": {"title": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": "التاريخ المحدد للانضمام", "entityName": "اسم الجهة", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "typeName": "اسم النوع"}, "requestHistory": {"title": "سجل الطلب"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة."}, "reviewAndSubmit": {"title": "مراجعة وتقديم الطلب", "entityDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "npoLegalForm": "الشكل القانوني", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "externalEntityDetails": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": " تاريخ البدء", "entityName": "اسم الجهة", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "nationalityType": "نوع الجنسية", "founderEmiratesID": "الهوية الاماراتية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (بالعربية)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "Date": "التاريخ", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "Id": "رقم", "status": "الحالة", "founderNameEnglish": "اسم المؤسس (انجليزي)", "founderNameArabic": "اسم المؤسس (العربية)", "founderNationality": "الجنسية", "responseDate": "تاريخ الرد", "npoEstablishmentNameEN": "اسم مؤسسة النفع العام (إنجليزي)", "npoEstablishmentNameAr": "اسم مؤسسة النفع العام (عربي)", "npoEstablishmentDate": "تاريخ تأسيس مؤسسة النفع العام", "npoEstablishmentLegalFormEN": "الشكل القانوني لمؤسسة النفع العام (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني لمؤسسة النفع العام (عربي)", "AccountId": "معر<PERSON> الح<PERSON>اب", "npoUnifiedNumber": "الرقم الموحد لمؤسسة النفع العام", "yes": "نعم", "no": "لا", "typeName": "اسم النوع"}, "requestDetails": {"title": "مراجعة وتقديم الطلب", "entityDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "npoLegalForm": "الشكل القانوني", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "externalEntityDetails": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": " تاريخ البدء", "entityName": "اسم الجهة", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "nationalityType": "نوع الجنسية", "founderEmiratesID": "الهوية الاماراتية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (بالعربية)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "Date": "التاريخ", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "Id": "رقم", "status": "الحالة", "founderNameEnglish": "اسم المؤسس (انجليزي)", "founderNameArabic": "اسم المؤسس (العربية)", "founderNationality": "الجنسية", "responseDate": "تاريخ الرد", "npoEstablishmentNameEN": "اسم مؤسسة النفع العام (إنجليزي)", "npoEstablishmentNameAr": "اسم مؤسسة النفع العام (عربي)", "npoEstablishmentDate": "تاريخ تأسيس مؤسسة النفع العام", "npoEstablishmentLegalFormEN": "الشكل القانوني لمؤسسة النفع العام (إنجليزي)", "npoEstablishmentLegalFormAr": "الشكل القانوني لمؤسسة النفع العام (عربي)", "AccountId": "معر<PERSON> الح<PERSON>اب", "npoUnifiedNumber": "الرقم الموحد لمؤسسة النفع العام", "yes": "نعم", "no": "لا", "joiningAndAffiliatingAssociationsService": "طلب تصريح الانتساب أو الاشتراك أو الانضمام إلى الجمعيات والهيئات الإقليمية أو الدولية", "NPO_LICENSE_DECLARATION": "طلب إنشاء مؤسسات النفع العام", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإشهار", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "COMMENT": "تعليق", "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>", "typeName": "اسم النوع"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "ص<PERSON><PERSON><PERSON> العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لمؤسسة النفع العام", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "عنوان البريد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لمؤسسة النفع العام", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لمؤسسة النفع العام"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "organizingEventsAndActivities": {"title": "تنظيم أنشطة وفعاليات (داخل دولة الإمارات العربية المتحدة)", "desc": "", "forms": {"entityDetails": {"title": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "worshipPlaceNameEn": "الاسم المقترح (انجليزي)", "worshipPlaceNameAr": "الاسم المقترح (عربي)", "religion": "الديانة", "sect": "الطائفة", "worshipPlaceType": "نوع دار العبادة"}, "activityAndEventDetails": {"title": "تفاصيل النشاط أو الفعالية", "requestType": "نوع الطلب", "npoActivityOrEventAssociation": "ارتباط النشاط أو الفعالية ", "selectBranch": "تحديد الفرع", "targetGroupOfTheActivityOrEvent": "الفئة المستهدفة من النشاط أو الفعالية", "natureOfTheActivityOrEvent": "وصف النشاط أو الفعالية", "thePurposeOfTheActivityOrEvent": "الهدف من النشاط أو الفعالية", "workingPapersSummary": "نبذة عن أوراق العمل التي ستقدم ضمن النشاط أو الفعالية"}, "participantDetails": {"title": "تفاصيل المشاركين", "willHostParticipants": "هل سيتم استضافة أشخاص للمشاركة؟", "Participants": "المشاركين", "insideUae": "من داخل الدولة", "outsideUae": "من خارج الدولة", "addParticipantsInsideUae": "إضافة مشاركين من داخل الدولة", "addParticipantsOutsideUae": "إضافة مشاركين من خارج الدولة", "addParticipant": "إضافة مشارك", "idNumber": "رقم الهوية", "dateOfBirth": "تاريخ الميلاد", "name": "الاسم", "purposeOfHosting": "الهدف من الاستضافة", "actions": "الإجراءات", "fullName": "الاسم الكامل ", "nationality": "الجنسية", "passportNumber": "رقم جواز السفر", "eidNumber": "رقم الهوية", "getInformation": "الحصول على المعلومات", "editParticipantInsideUae": "تعديل مشاركين من داخل الدولة", "editParticipantOutsideUae": "تعديل مشاركين من خارج الدولة", "passportCopy": "صورة عن جواز السفر"}, "executionDateAndPlace": {"title": "تاريخ ومكان التنفيذ", "executionDate": "تاريخ التنفيذ", "executionPlace": "مكان التنفيذ", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "placeName": "اسم المكان", "address": "العنوان", "geographicLocation": "الموقع الجغرافي"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف"}, "reviewAndSubmit": {"worshipPlaceNameEn": "الاسم المقترح (انجليزي)", "worshipPlaceNameAr": "الاسم المقترح (عربي)", "religion": "الديانة", "sect": "الطائفة", "worshipPlaceType": "نوع دار العبادة", "executionDateAndPlace": "تاريخ ومكان التنفيذ", "executionDate": "تاريخ التنفيذ", "executionPlace": "مكان التنفيذ", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "placeName": "اسم المكان", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "participantDetails": "تفاصيل المشاركين", "willHostParticipants": "هل سيتم استضافة أشخاص للمشاركة؟", "insideUae": "من داخل الدولة", "outsideUae": "من خارج الدولة", "participantsInsideUae": "مشاركين من داخل الدولة", "participantsOutsideUae": "المشاركين من خارج الدولة", "idNumber": "رقم الهوية", "name": "الاسم", "purposeOfHosting": "الهدف من الاستضافة", "actions": "الإجراءات", "fullName": "الاسم الكامل ", "nationality": "الجنسية", "passportNumber": "رقم جواز السفر", "eidNumber": "رقم الهوية", "activityAndEventDetails": "تفاصيل النشاط أو الفعالية", "npoActivityOrEventAssociation": "ارتباط النشاط أو الفعالية ", "branch": "الفرع", "targetGroupOfTheActivityOrEvent": "الفئة المستهدفة من النشاط أو الفعالية", "natureOfTheActivityOrEvent": "وصف النشاط أو الفعالية", "thePurposeOfTheActivityOrEvent": "الهدف من النشاط أو الفعالية", "workingPapersSummary": "نبذة عن أوراق العمل التي ستقدم ضمن النشاط أو الفعالية", "entityDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "requestType": "نوع الطلب", "dateOfBirth": "تاريخ الميلاد", "title": "مراجعة وتقديم الطلب", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "uploadDocuments": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "passportCopy": "صورة عن جواز السفر"}, "requestDetails": {"page-title": "تنظيم أنشطة وفعاليات (داخل دولة الإمارات العربية المتحدة)", "worshipPlaceNameEn": "الاسم المقترح (انجليزي)", "worshipPlaceNameAr": "الاسم المقترح (عربي)", "religion": "الديانة", "sect": "الطائفة", "worshipPlaceType": "نوع دار العبادة", "executionDateAndPlace": "تاريخ ومكان التنفيذ", "executionDate": "تاريخ التنفيذ", "executionPlace": "مكان التنفيذ", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "placeName": "اسم المكان", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "participantDetails": "تفاصيل المشاركين", "willHostParticipants": "هل سيتم استضافة أشخاص للمشاركة؟", "insideUae": "من داخل الدولة", "outsideUae": "من خارج الدولة", "participantsInsideUae": "مشاركين من داخل الدولة", "participantsOutsideUae": "المشاركين من خارج الدولة", "idNumber": "رقم الهوية", "name": "الاسم", "purposeOfHosting": "الهدف من الاستضافة", "actions": "الإجراءات", "fullName": "الاسم الكامل ", "nationality": "الجنسية", "passportNumber": "رقم جواز السفر", "eidNumber": "رقم الهوية", "activityAndEventDetails": "تفاصيل النشاط أو الفعالية", "npoActivityOrEventAssociation": "ارتباط النشاط أو الفعالية ", "branch": "الفرع", "targetGroupOfTheActivityOrEvent": "الفئة المستهدفة من النشاط أو الفعالية", "natureOfTheActivityOrEvent": "وصف النشاط أو الفعالية", "thePurposeOfTheActivityOrEvent": "الهدف من النشاط أو الفعالية", "workingPapersSummary": "نبذة عن أوراق العمل التي ستقدم ضمن النشاط أو الفعالية", "entityDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "requestType": "نوع الطلب", "dateOfBirth": "تاريخ الميلاد", "title": "مراجعة وتقديم الطلب", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "uploadDocuments": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "passportCopy": "صورة عن جواز السفر"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "activitiesAndEventsParticipation": {"title": "المشاركة في الأنشطة والفعاليات (داخل/خارج الإمارات العربية المتحدة)", "desc": "", "forms": {"entityDetails": {"title": "بيانات الجهة الطالبة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص"}, "organizerEntityDetails": {"title": "بيانات الجهة المنظمة", "EntityName": "اسم الجهة", "EntitySector": "تصنيف الجهة"}, "activityAndEventDetails": {"title": "تفاصيل النشاط/الفعالية", "activityAndEventTitle": " اسم النشاط/الفعالية", "activityAndEventType": "نوع النشاط/الفعالية", "activityAndEventDesc": "وصف النشاط/الفعالية", "dateAndLocation": "التاريخ والموقع", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "location": "الموقع", "emirate": "الإمارة", "country": "الدولة", "address": "مكان إقامة النشاط/الفعالية", "geographicLocation": "الموقع الجغرافي"}, "participationDetails": {"title": "تفاصيل المشاركة", "natureOfParticipationDetails": "طبيعة تفاصيل المشاركة", "npoMembersOrEmployeesParticipations": "المشاركين من داخل المؤسسة", "addParticipantMember": "إضافة عضو مشارك", "searchForMember": "البحث عن عضو", "searchForEmployee": "البحث عن موظف", "addParticipant": " إضافة مشارك داخلي ", "addExternalParticipant": "إضافة مشارك خارجي", "membershipNumber": "رقم العضوية", "name": "الاسم", "participantName": "اسم المشارك", "participantType": "نوع المشارك", "participantEmiratesId": "رقم الهوية للمشارك", "externalParticipantEmiratesId": "رقم هوية المشارك الخارجي", "externalParticipantDateOfBirth": "تاريخ ميلاد المشارك الخارجي", "participantDateOfBirth": "تاريخ ميلاد المشارك", "participantNationality": "جنسية المشارك", "status": "الحالة", "actions": "الإجراءات", "nationality": "الجنسية", "passportNumber": "رقم جواز السفر", "eidNumber": "رقم الهوية", "dateOfBirth": "تاريخ الميلاد", "externalParticipants": "المشاركين من خارج المؤسسة", "edit": "تعديل", "getInformation": "الحصول على المعلومات", "searchByMemebrParticipant": "البحث عن عضو", "searchByEmplyeeParticipant": "البحث عن موظف"}, "requestHistory": {"title": "سجل الطلب"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة."}, "reviewAndSubmit": {"participationDetails": "تفاصيل المشاركة", "natureOfParticipationDetails": "طبيعة تفاصيل المشاركة", "npoMembersOrEmployeesParticipations": "المشاركين من داخل المؤسسة", "addParticipantMember": "إضافة عضو مشارك", "searchForMember": "البحث عن عضو", "searchForEmployee": "البحث عن موظف", "addParticipant": "إضافة مشارك", "addExternalParticipant": "إضافة مشارك خارجي", "membershipNumber": "رقم العضوية", "participantName": "اسم المشارك", "participantType": "نوع المشارك", "participantEmiratesId": "رقم الهوية للمشارك", "participantDateOfBirth": "تاريخ ميلاد المشارك", "participantNationality": "جنسية المشارك", "status": "الحالة", "externalParticipants": "المشاركين من خارج المؤسسة", "activityAndEventTitle": " اسم النشاط/الفعالية", "activityAndEventType": "نوع النشاط/الفعالية", "activityAndEventDesc": "وصف النشاط/الفعالية", "dateAndLocation": "التاريخ والموقع", "location": "الموقع", "emirate": "الإمارة", "country": "الدولة", "organizerEntityDetails": "بيانات الجهة المنظمة", "EntityName": "اسم الجهة", "EntitySector": "تصنيف الجهة", "executionDateAndPlace": "تاريخ ومكان التنفيذ", "executionDate": "تاريخ التنفيذ", "executionPlace": "مكان التنفيذ", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "placeName": "اسم المكان", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "participantDetails": "تفاصيل المشاركين", "willHostParticipants": "هل سيتم استضافة أشخاص للمشاركة؟", "insideUae": "من داخل الدولة", "outsideUae": "من خارج الدولة", "participantsInsideUae": "مشاركين من داخل الدولة", "participantsOutsideUae": "المشاركين من خارج الدولة", "idNumber": "رقم الهوية", "name": "الاسم", "purposeOfHosting": "الهدف من الاستضافة", "actions": "الإجراءات", "fullName": "الاسم", "nationality": "الجنسية", "passportNumber": "رقم جواز السفر", "eidNumber": "رقم الهوية", "activityAndEventDetails": "تفاصيل النشاط أو الفعالية", "npoActivityOrEventAssociation": "ارتباط النشاط أو الفعالية ", "branch": "الفرع", "targetGroupOfTheActivityOrEvent": "الفئة المستهدفة من النشاط أو الفعالية", "natureOfTheActivityOrEvent": "وصف النشاط أو الفعالية", "thePurposeOfTheActivityOrEvent": "الهدف من النشاط أو الفعالية", "workingPapersSummary": "نبذة عن أوراق العمل التي ستقدم ضمن النشاط أو الفعالية", "entityDetails": "بيانات الجهة الطالبة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "requestType": "نوع الطلب", "dateOfBirth": "تاريخ الميلاد", "title": "مراجعة وتقديم الطلب", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "uploadDocuments": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "npoLegalForm": "الشكل القانوني"}, "requestDetails": {"APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإشهار", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "COMMENT": "تعليق", "participate-title": "المشاركة في الأنشطة والفعاليات (داخل/خارج الإمارات العربية المتحدة)", "participationDetails": "تفاصيل المشاركة", "natureOfParticipationDetails": "طبيعة تفاصيل المشاركة", "npoMembersOrEmployeesParticipations": "المشاركين من داخل المؤسسة", "addParticipantMember": "إضافة عضو مشارك", "searchForMember": "البحث عن عضو", "searchForEmployee": "البحث عن موظف", "addParticipant": "إضافة مشارك", "addExternalParticipant": "إضافة مشارك خارجي", "membershipNumber": "رقم العضوية", "participantName": "اسم المشارك", "participantType": "نوع المشارك", "participantEmiratesId": "رقم الهوية للمشارك", "participantDateOfBirth": "تاريخ ميلاد المشارك", "participantNationality": "جنسية المشارك", "status": "الحالة", "externalParticipants": "المشاركين من خارج المؤسسة", "activityAndEventTitle": " اسم النشاط/الفعالية", "activityAndEventType": "نوع النشاط/الفعالية", "activityAndEventDesc": "وصف النشاط/الفعالية", "dateAndLocation": "التاريخ والموقع", "location": "الموقع", "emirate": "الإمارة", "country": "الدولة", "organizerEntityDetails": "بيانات الجهة المنظمة", "EntityName": "اسم الجهة", "EntitySector": "تصنيف الجهة", "executionDateAndPlace": "تاريخ ومكان التنفيذ", "executionDate": "تاريخ التنفيذ", "executionPlace": "مكان التنفيذ", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "placeName": "اسم المكان", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "participantDetails": "تفاصيل المشاركين", "willHostParticipants": "هل سيتم استضافة أشخاص للمشاركة؟", "insideUae": "من داخل الدولة", "outsideUae": "من خارج الدولة", "participantsInsideUae": "مشاركين من داخل الدولة", "participantsOutsideUae": "المشاركين من خارج الدولة", "idNumber": "رقم الهوية", "name": "الاسم", "purposeOfHosting": "الهدف من الاستضافة", "actions": "الإجراءات", "fullName": "الاسم", "nationality": "الجنسية", "passportNumber": "رقم جواز السفر", "eidNumber": "رقم الهوية", "activityAndEventDetails": "تفاصيل النشاط أو الفعالية", "npoActivityOrEventAssociation": "ارتباط النشاط أو الفعالية ", "branch": "الفرع", "targetGroupOfTheActivityOrEvent": "الفئة المستهدفة من النشاط أو الفعالية", "natureOfTheActivityOrEvent": "وصف النشاط أو الفعالية", "thePurposeOfTheActivityOrEvent": "الهدف من النشاط أو الفعالية", "workingPapersSummary": "نبذة عن أوراق العمل التي ستقدم ضمن النشاط أو الفعالية", "entityDetails": "بيانات الجهة الطالبة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "requestType": "نوع الطلب", "dateOfBirth": "تاريخ الميلاد", "title": "مراجعة وتقديم الطلب", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "uploadDocuments": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "npoLegalForm": "الشكل القانوني"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "جهة العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لمؤسسة النفع العام", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "الب<PERSON>يد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لمؤسسة النفع العام", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لمؤسسة النفع العام"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "openingBranchRequest": {"title": "طلب الموافقة على إنشاء فرع مؤسسة نفع عام", "desc": "", "forms": {"entityDetails": {"title": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص"}, "newBranchDetails": {"title": "بيانات الفرع الجديد", "newBranchDetails": "بيانات الفرع الجديد", "branchNameAr": "إسم الفرع (العربية)", "branchNameEn": "إسم الفرع (الإنجليزية)", "branchEmirate": "إمارة الفرع", "branchManager": "مدير الفرع", "selectBranchManager": "اختر مدير الفرع", "help": "مساعدة"}, "branchMembersList": {"title": "قائمة أعضاء الفرع", "branchMembersList": "قائمة أعضاء الفرع", "membershipNumber": "رقم العضوية", "emiratesId": "رقم الهوية", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "gender": "الجنس", "nationality": "الجنسية", "emirate": "الإمارة", "membershipStatus": "حالة العضوية"}, "requestHistory": {"title": "سجل الطلب"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة."}, "reviewAndSubmit": {"branchMembersList": "قائمة أعضاء الفرع", "membershipNumber": "رقم العضوية", "emiratesId": "رقم الهوية", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "gender": "الجنس", "nationality": "الجنسية", "emirate": "الإمارة", "membershipStatus": "حالة العضوية", "newBranchDetails": "بيانات الفرع الجديد", "branchNameAr": "إسم الفرع (العربية)", "branchNameEn": "إسم الفرع (الإنجليزية)", "branchEmirate": "إمارة الفرع", "branchManager": "مدير الفرع", "selectBranchManager": "اختر مدير الفرع", "title": "مراجعة وتقديم الطلب", "entityDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "npoLegalForm": "الشكل القانوني", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "externalEntityDetails": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": " تاريخ البدء", "entityName": "اسم الجهة", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "nationalityType": "نوع الجنسية", "founderEmiratesID": "الهوية الاماراتية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (عربي)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "Date": "التاريخ", "Agenda": "جدول الأعمال"}, "requestDetails": {"title": "بيانات الجهة", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "newBranchDetails": "بيانات الفرع الجديد", "branchNameAr": "إسم الفرع (العربية)", "branchNameEn": "إسم الفرع (الإنجليزية)", "branchEmirate": "إمارة الفرع", "branchManager": "مدير الفرع", "selectBranchManager": "اختر مدير الفرع", "branchMembersList": "قائمة أعضاء الفرع", "membershipNumber": "رقم العضوية", "emiratesId": "رقم الهوية", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "gender": "الجنس", "nationality": "الجنسية", "emirate": "الإمارة", "membershipStatus": "حالة العضوية", "details-title": "طلب الموافقة على إنشاء فرع مؤسسة نفع عام", "entityDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "npoLegalForm": "الشكل القانوني", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "externalEntityDetails": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": " تاريخ البدء", "entityName": "اسم الجهة", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "status": "الحالة", "responseDate": "تاريخ الرد", "yes": "نعم", "no": "لا", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإشهار", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "COMMENT": "تعليق", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (الإنجليزية)", "EntityNameAr": "اسم الجهة (العربية)", "EntityType": "Entity Type", "NumberOfEmployees": "<PERSON><PERSON><PERSON> الموظفين", "category": "الفئة", "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "ص<PERSON><PERSON><PERSON> العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لمؤسسة النفع العام", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "عنوان البريد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لمؤسسة النفع العام", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لمؤسسة النفع العام"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "nocRequestService": {"title": "طلب الحصول على شهادة عدم ممانعة لتلقي التبرعات", "desc": "", "forms": {"legalTypePage": {"title": "طلب الحصول على شهادة عدم ممانعة لتلقي التبرعات", "selectNpoLegalForm": "اختر نوع الخدمة"}, "npoDetails": {"title": "بيانات الجهة", "npoDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم الجهة (الإنجليزية)", "NpoNameAr": "اسم الجهة (العربية)", "NpoLegalForm": "الشكل القانوني للجهة", "NpoDeclarationDecision": "رابط قرار إشهار الجهة", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص"}, "donorDetails": {"title": "تفاصيل المتبرع", "donorDetails": "تفاصيل المتبرع", "donorType": "نوع المتبرع", "fullName": "الاسم الكامل", "nationality": "الجنسية", "placeOfBirth": "مكان الميلاد", "currentResidence": "مكان الإقامة الحالي", "mobile": "رقم الموبايل", "email": "الب<PERSON>يد الإلكتروني", "passportNumber": "رقم جواز السفر", "passportType": "نوع جواز السفر", "passportExpiryDate": "تاريخ انتهاء جواز السفر", "jobTitle": "المسمى الوظيفي", "employer": "جهة العمل", "companyName": "اسم الشركة/الجهة", "country": "الدولة", "city": "المدينة", "licensingAuthority": "الجهة المرخصة", "commercialLicenseNumber": "رقم الرخصة التجارية", "licenseIssuanceDate": "تاريخ إصدار الترخيص", "licenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "phoneNumber": "رقم الهاتف", "entityName": "اسم الجهة", "businessDomain": "نوع النشاط", "licenseNumber": "رقم الرخصة", "npoCategory": "النشاط الرئيسي", "otherPassportType": "من فضلك ادخل نوع جواز السفر", "emiratesId": "رقم الهوية الإماراتية", "dateOfBirth": "تاريخ الميلاد", "getInformation": "الحصول على المعلومات", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "unifiedNumber": "الرقم الموحد", "emirate": "الإمارة", "entityType": "نوع الجهة", "entityOrCompanyName": "اسم الكيان / الشركة", "unifiedNumberOrNpoName": "الرقم الموحد / اسم المؤسسة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "npoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "mainCategory": "الفئة الرئيسية", "headquartersEmirate": "مقر الامارة", "licensingEntity": "جهة الترخيص", "actions": "الإجراءات"}, "typeOfDonations": {"title": "نوع التبرعات", "typeOfDonations": "نوع التبرعات", "typeOfCashDonations": "التبرعات النقدية", "totalCashDonationsAmount": "إجمالي التبرعات النقدية (بالدرهم الإماراتي)", "typeOfInkindDonations": "التبرعات العينية", "totalInkindDonationsAmount": "إجمالي التبرعات العينية (بالدرهم الإماراتي)", "grandTotalDonationsAmount": "إجمالي مبلغ التبرع بالدرهم الإماراتي", "grandTotalDonations": "إجمالي مبلغ التبرع", "addCashDonationType": "أض<PERSON> التبرعات النقدية", "addInkindDonationType": "أض<PERSON> التبرعات العينية", "editCashDonationType": "تعديل التبرعات النقدية", "editInkindDonationType": "تعديل التبرعات العينية", "selectCashDonationType": "اختر التبرع النقدي", "selectInkindDonationType": "اختر التبرع العيني", "cashDonationType": "التبرع النقدي", "inkindDonationType": "التبرع العيني", "cashDonationTypeEn": "التبرع النقدي (بالانجليزية)", "cashDonationTypeAr": "التبرع النقدي (بالعربية)", "inkindDonationTypeEn": "التبرع العيني (بالانجليزية)", "inkindDonationTypeAr": "التبرع العيني (بالعربية)", "currencyType": "نوع العملة", "enterCurrencyType": "ادخل نوع العملة", "enterCountOrQuantuty": "ادخل الكمية او العدد", "approximateValueAed": "القيمة التقريبية بالدرهم الاماراتي", "actions": "الإجراءات", "countOrQuantity": "الكمية / العدد"}, "npoBankAccountDetails": {"title": "تفاصيل الحساب البنكي لمؤسسة النفع العام", "npoBankAccountDetails": "تفاصيل الحساب البنكي لمؤسسة النفع العام", "selectBank": "اختر البنك", "selectBankAccount": "اختر الحساب المصرفي", "bank": "البنك", "bankAccount": "الحساب البنكي", "accountOwnerNameEn": "اسم صاحب الحسا<PERSON> (إنجليزي)", "accountOwnerNameAr": "اسم صاحب الح<PERSON><PERSON><PERSON> (عر<PERSON><PERSON>)", "branchName": "اسم الفرع", "bankAddress": "عنوان البنك", "ibanNumber": "رقم IBAN", "accountType": "نوع الحساب", "accountOpenDate": "تاريخ فتح الحساب", "currencyType": "نوع العملة"}, "donorBankAccountDetails": {"title": "تفاصيل الحساب البنكي للمتبرع", "donorBankAccountDetails": "تفاصيل الحساب البنكي للمتبرع", "country": "الدولة", "bankName": "اسم البنك", "accountNumber": "رقم الحساب", "ibanNumber": "رقم IBAN"}, "donationPurpose": {"title": "الغرض من التبرع", "donationPurpose": "الغرض من التبرع"}, "donationDateAndDuration": {"title": "تاريخ/مدة تلقي التبرعات المتوقعة", "donationDateAndDuration": "تاريخ/مدة تلقي التبرعات المتوقعة", "dateOfReceivingDonations": "تاريخ استلام التبرعات", "fromDate": "من تاريخ", "toDate": "إلى تاريخ"}, "requestHistory": {"title": "سجل الطلب"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة."}, "reviewAndSubmit": {"unifiedNumberOrNpoName": "الرقم الموحد / اسم المؤسسة", "mainCategory": "الفئة الرئيسية", "headquartersEmirate": "مقر الامارة", "licensingEntity": "جهة الترخيص", "inkindDonationTypeEn": "نوع التبرع العيني (بالانجليزية)", "inkindDonationTypeAr": "نوع التبرع العيني (بالعربية)", "cashDonationTypeEn": "نوع التبرع النقدي (بالانجليزية)", "cashDonationTypeAr": "نوع التبرع النقدي (بالعربية)", "countOrQuantity": "الكمية / العدد", "approximateValueAed": "القيمة التقريبية بالدرهم الاماراتي", "npoDetails": "بيانات الجهة", "bank": "البنك", "bankAccount": "الحساب البنكي", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم الجهة (الإنجليزية)", "NpoNameAr": "اسم الجهة (العربية)", "NpoLegalForm": "الشكل القانوني للجهة", "NpoDeclarationDecision": "رابط قرار إشهار الجهة", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "donorDetails": "تفاصيل المتبرع", "donorType": "نوع المتبرع", "fullName": "الاسم الكامل", "nationality": "الجنسية", "placeOfBirth": "مكان الميلاد", "entityType": "نوع الجهة", "currentResidence": "مكان الإقامة الحالي", "mobile": "رقم الموبايل", "email": "الب<PERSON>يد الإلكتروني", "passportNumber": "رقم جواز السفر", "passportType": "نوع جواز السفر", "passportExpiryDate": "تاريخ انتهاء جواز السفر", "jobTitle": "المسمى الوظيفي", "employer": "جهة العمل", "companyName": "اسم الشركة/الجهة", "country": "الدولة", "city": "المدينة", "licensingAuthority": "الجهة المرخصة", "commercialLicenseNumber": "رقم الرخصة التجارية", "licenseIssuanceDate": "تاريخ إصدار الترخيص", "licenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "phoneNumber": "رقم الهاتف", "entityName": "اسم الجهة", "businessDomain": "نوع النشاط", "licenseNumber": "رقم الرخصة", "npoCategory": "النشاط الرئيسي", "typeOfDonations": "نوع التبرعات", "typeOfCashDonations": "التبرعات النقدية", "totalCashDonationsAmount": "إجمالي التبرعات النقدية (بالدرهم الإماراتي)", "typeOfInkindDonations": "التبرعات العينية", "totalInkindDonationsAmount": "إجمالي التبرعات العينية (بالدرهم الإماراتي)", "grandTotalDonationsAmount": "إجمالي مبلغ التبرع بالدرهم الإماراتي", "grandTotalDonations": "إجمالي مبلغ التبرع", "npoBankAccountDetails": "تفاصيل الحساب البنكي لالجهة", "selectBank": "اختر البنك", "selectBankAccount": "اختر الحساب المصرفي", "accountOwnerNameEn": "اسم صاحب الحسا<PERSON> (إنجليزي)", "accountOwnerNameAr": "اسم صاحب الح<PERSON><PERSON><PERSON> (عر<PERSON><PERSON>)", "branchName": "اسم الفرع", "bankAddress": "عنوان البنك", "ibanNumber": "رقم IBAN", "accountType": "نوع الحساب", "accountOpenDate": "تاريخ فتح الحساب", "currencyType": "نوع العملة", "donorBankAccountDetails": "تفاصيل الحساب البنكي للمتبرع", "bankName": "اسم البنك", "accountNumber": "رقم الحساب", "donationPurpose": "الغرض من التبرع", "donationDateAndDuration": "تاريخ/مدة تلقي التبرعات المتوقعة", "dateOfReceivingDonations": "تاريخ استلام التبرعات", "fromDate": "من تاريخ", "toDate": "إلى تاريخ", "branchMembersList": "قائمة أعضاء الفرع", "membershipNumber": "رقم العضوية", "emiratesId": "رقم الهوية", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "gender": "الجنس", "emirate": "الإمارة", "entityOrCompanyName": "اسم الكيان / الشركة", "membershipStatus": "حالة العضوية", "newBranchDetails": "بيانات الفرع الجديد", "branchNameAr": "إسم الفرع (العربية)", "branchNameEn": "إسم الفرع (الإنجليزية)", "branchEmirate": "إمارة الفرع", "branchManager": "مدير الفرع", "selectBranchManager": "اختر مدير الفرع", "title": "مراجعة وتقديم الطلب", "entityDetails": "بيانات الجهة", "npoLegalForm": "الشكل القانوني", "externalEntityDetails": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": " تاريخ البدء", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "nationalityType": "نوع الجنسية", "founderEmiratesID": "الهوية الاماراتية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (عربي)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "Date": "التاريخ", "Agenda": "جدول الأعمال"}, "requestDetails": {"nocRequestServiceTitle": "طلب الحصول على شهادة عدم ممانعة لتلقي التبرعات", "unifiedNumberOrNpoName": "الرقم الموحد / اسم المؤسسة", "mainCategory": "الفئة الرئيسية", "headquartersEmirate": "مقر الامارة", "licensingEntity": "جهة الترخيص", "inkindDonationTypeEn": "نوع التبرع العيني (بالانجليزية)", "inkindDonationTypeAr": "نوع التبرع العيني (بالعربية)", "cashDonationTypeEn": "نوع التبرع النقدي (بالانجليزية)", "cashDonationTypeAr": "نوع التبرع النقدي (بالعربية)", "countOrQuantity": "الكمية / العدد", "approximateValueAed": "القيمة التقريبية بالدرهم الاماراتي", "npoDetails": "بيانات الجهة", "bank": "البنك", "bankAccount": "الحساب البنكي", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم الجهة (الإنجليزية)", "NpoNameAr": "اسم الجهة (العربية)", "NpoLegalForm": "الشكل القانوني لالجهة", "NpoDeclarationDecision": "رابط قرار إشهار الجهة", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "donorDetails": "تفاصيل المتبرع", "entityType": "نوع الجهة", "donorType": "نوع المتبرع", "fullName": "الاسم الكامل", "nationality": "الجنسية", "placeOfBirth": "مكان الميلاد", "currentResidence": "مكان الإقامة الحالي", "mobile": "رقم الموبايل", "email": "الب<PERSON>يد الإلكتروني", "passportNumber": "رقم جواز السفر", "passportType": "نوع جواز السفر", "passportExpiryDate": "تاريخ انتهاء جواز السفر", "jobTitle": "المسمى الوظيفي", "employer": "جهة العمل", "companyName": "اسم الشركة/الجهة", "country": "الدولة", "city": "المدينة", "licensingAuthority": "الجهة المرخصة", "commercialLicenseNumber": "رقم الرخصة التجارية", "licenseIssuanceDate": "تاريخ إصدار الترخيص", "licenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "phoneNumber": "رقم الهاتف", "entityName": "اسم الجهة", "businessDomain": "نوع النشاط", "licenseNumber": "رقم الرخصة", "npoCategory": "النشاط الرئيسي", "typeOfDonations": "نوع التبرعات", "typeOfCashDonations": "نوع التبرعات النقدية", "totalCashDonationsAmount": "إجمالي التبرعات النقدية (بالدرهم الإماراتي)", "typeOfInkindDonations": "نوع التبرعات العينية", "totalInkindDonationsAmount": "إجمالي التبرعات العينية (بالدرهم الإماراتي)", "grandTotalDonationsAmount": "إجمالي مبلغ التبرع بالدرهم الإماراتي", "grandTotalDonations": "إجمالي مبلغ التبرع", "npoBankAccountDetails": "تفاصيل الحساب البنكي للجهة", "selectBank": "اختر البنك", "selectBankAccount": "اختر الحساب المصرفي", "accountOwnerNameEn": "اسم صاحب الحسا<PERSON> (إنجليزي)", "accountOwnerNameAr": "اسم صاحب الح<PERSON><PERSON><PERSON> (عر<PERSON><PERSON>)", "branchName": "اسم الفرع", "bankAddress": "عنوان البنك", "ibanNumber": "رقم IBAN", "accountType": "نوع الحساب", "accountOpenDate": "تاريخ فتح الحساب", "currencyType": "نوع العملة", "donorBankAccountDetails": "تفاصيل الحساب البنكي للمتبرع", "bankName": "اسم البنك", "accountNumber": "رقم الحساب", "donationPurpose": "الغرض من التبرع", "donationDateAndDuration": "تاريخ/مدة تلقي التبرعات المتوقعة", "dateOfReceivingDonations": "تاريخ استلام التبرعات", "fromDate": "من تاريخ", "toDate": "إلى تاريخ", "branchMembersList": "قائمة أعضاء الفرع", "membershipNumber": "رقم العضوية", "emiratesId": "رقم الهوية", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "gender": "الجنس", "emirate": "الإمارة", "membershipStatus": "حالة العضوية", "newBranchDetails": "بيانات الفرع الجديد", "branchNameAr": "إسم الفرع (العربية)", "branchNameEn": "إسم الفرع (الإنجليزية)", "branchEmirate": "إمارة الفرع", "branchManager": "مدير الفرع", "selectBranchManager": "اختر مدير الفرع", "title": "مراجعة وتقديم الطلب", "entityDetails": "بيانات الجهة", "npoLegalForm": "الشكل القانوني", "externalEntityDetails": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": " تاريخ البدء", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "status": "الحالة", "responseDate": "تاريخ الرد", "yes": "نعم", "no": "لا", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإشهار", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "COMMENT": "تعليق", "EntityName": "اسم الجهة", "EntityNameEn": "اسم الجهة (الإنجليزية)", "EntityNameAr": "اسم الجهة (العربية)", "EntityType": "Entity Type", "NumberOfEmployees": "<PERSON><PERSON><PERSON> الموظفين", "category": "الفئة", "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "ص<PERSON><PERSON><PERSON> العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لمؤسسة النفع العام", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "عنوان البريد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لمؤسسة النفع العام", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لمؤسسة النفع العام"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "openingNewBankAccount": {"title": "طلب فتح حساب بنكي جديد", "desc": "", "forms": {"entityDetails": {"title": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "NpoLegalForm": "الشكل القانوني لمؤسسة النفع العام", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "worshipPlaceNameAr": "اسم دار العبادة (العربية)", "worshipPlaceNameEn": "اسم دار العبادة (الإنجليزية)", "religion": "الديانة", "sect": "الطائفة", "worshipPlaceType": "نوع دار العبادة"}, "bankInformation": {"title": "معلومات البنك", "bankInformation": "معلومات البنك", "selectBank": "اختر البنك", "bank": "البنك", "accountCurrency": "عملة الحساب"}, "authorizationMatrix": {"title": "مصفوفة الصلاحيات", "authorizationMatrix": "مصفوفة الصلاحيات", "authorizationMatrixType": "نوع مصفوفة الصلاحيات", "authorizedSignatureGroup": "مجموعات المخولين بالتوقيع", "authorizedSignatories": "المخولين بالتوقيع", "boardTermExpiryDate": "تاريخ انتهاء مدة مجلس الإدارة", "setAuthorizationMatrixType": "حدد نوع مصفوفة الصلاحيات", "addAuthorizedSignatureGroup": "إضافة مجموعات المخولين بالتوقيع", "authorizedSignatureGroupList": "قائمة مجموعات المخولين بالتوقيع", "addAuthorizedSignatory": "إضافة مخول بالتوقيع", "authorizedSignatoriesList": "قائمة المخولين بالتوقيع", "amountThreshold": "الحدود المالية", "amount": "المبلغ", "position": "المنصب", "name": "الاسم", "isBoardMember": "هل عضو مجلس ادارة", "actions": "الإجراءات", "assignedGroup": "المجموعة المخصصة", "editAuthorizedSignatory": "تعديل المخول بالتوقيع", "groupName": "اسم المجموعة", "soloSignatories": "الموقعون المعتمدون (الموقع المنفرد)", "dualSignatories": "الموقعون المعتمدون (الموقع المزدوج)", "soloAndDualSignatories": "الموقعون المعتمدون (موقع فردي وثنائي)", "groupsSignatories": "الموقعون المعتمدون (مجموعات الموقعين)"}, "requestHistory": {"title": "سجل الطلب"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف", "membershipConditions": "أنواع و شروط العضوية", "condition1": "لا يجوز أن يقل عدد الأعضاء المؤسسين عن سبعة أعضاء", "condition2": "لا يجوز أن تقل نسبة الأعضاء المؤسسين الذين يحملون الجنسية الدولة عن 70% من إجمالي عدد الأعضاء المؤسسين، ويجوز للأشخاص الذين لا يحملون الجنسية الدولة الاشتراك في تأسيس الجمعيات وفقاً للضوابط التالية:", "condition2.1": "ألا يتجاوز عددهم 30% من إجمالي عدد الأعضاء المؤسسين.", "condition2.2": "لا يتمتع العضو بوضع دبلوماسي.", "condition2.3": "أن يكون لديه إقامة صالحة في الدولة لمدة لا تقل عن ثلاث سنوات.", "condition3": "يجب أن يكون العضو المؤسس قد بلغ سن الرشد وفقاً للتشريعات النافذة في الدولة", "condition4": "- يشترط في العضو المؤسس أن يكون حسن السيرة والسلوك والسمعة، ولم يسبق الحكم عليه بعقوبة مقيدة للحرية في جناية أو جنحة مخلة بالشرف أو الأمانة ما لم يكن قد رد إليه اعتباره.", "help": "المساعدة", "helpMessage": "يمكنك إرفاق شعار مؤسسة النفع العام في أي وقت بعد تقديم المعلومات، إلا أن ذلك إلزامي قبل موافقة الوزارة."}, "reviewAndSubmit": {"groupName": "اسم المجموعة", "soloSignatories": "الموقعون المعتمدون (الموقع المنفرد)", "dualSignatories": "الموقعون المعتمدون (الموقع المزدوج)", "soloAndDualSignatories": "الموقعون المعتمدون (موقع فردي وثنائي)", "groupsSignatories": "الموقعون المعتمدون (مجموعات الموقعين)", "amountThreshold": "الحدود المالية", "amount": "المبلغ", "position": "المنصب", "name": "الاسم", "isBoardMember": "هل عضو مجلس ادارة", "assignedGroup": "المجموعة المخصصة", "bank": "البنك", "authorizationMatrix": "مصفوفة الصلاحيات", "authorizationMatrixType": "نوع مصفوفة الصلاحيات", "authorizedSignatureGroup": "مجموعات المخولين بالتوقيع", "authorizedSignatories": "المخولين بالتوقيع", "boardTermExpiryDate": "تاريخ انتهاء مدة مجلس الإدارة", "setAuthorizationMatrixType": "حدد نوع مصفوفة الصلاحيات", "addAuthorizedSignatureGroup": "إضافة مجموعات المخولين بالتوقيع", "authorizedSignatureGroupList": "قائمة مجموعات المخولين بالتوقيع", "addAuthorizedSignatory": "إضافة مخول بالتوقيع", "authorizedSignatoriesList": "قائمة المخولين بالتوقيع", "bankInformation": "معلومات البنك", "selectBank": "اختر البنك", "accountCurrency": "عملة الحساب", "worshipPlaceNameAr": "اسم دار العبادة (العربية)", "worshipPlaceNameEn": "اسم دار العبادة (الإنجليزية)", "religion": "الديانة", "sect": "الطائفة", "worshipPlaceType": "نوع دار العبادة", "branchMembersList": "قائمة أعضاء الفرع", "membershipNumber": "رقم العضوية", "emiratesId": "رقم الهوية", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "gender": "الجنس", "nationality": "الجنسية", "emirate": "الإمارة", "membershipStatus": "حالة العضوية", "newBranchDetails": "بيانات الفرع الجديد", "branchNameAr": "إسم الفرع (العربية)", "branchNameEn": "إسم الفرع (الإنجليزية)", "branchEmirate": "إمارة الفرع", "branchManager": "مدير الفرع", "selectBranchManager": "اختر مدير الفرع", "title": "مراجعة وتقديم الطلب", "entityDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "npoLegalForm": "الشكل القانوني", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "externalEntityDetails": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": " تاريخ البدء", "entityName": "اسم الجهة", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "nationalityType": "نوع الجنسية", "founderEmiratesID": "الهوية الاماراتية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (عربي)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "Date": "التاريخ", "Agenda": "جدول الأعمال"}, "requestDetails": {"groupName": "اسم المجموعة", "soloSignatories": "الموقعون المعتمدون (الموقع المنفرد)", "dualSignatories": "الموقعون المعتمدون (الموقع المزدوج)", "soloAndDualSignatories": "الموقعون المعتمدون (موقع فردي وثنائي)", "groupsSignatories": "الموقعون المعتمدون (مجموعات الموقعين)", "amountThreshold": "الحدود المالية", "amount": "المبلغ", "position": "المنصب", "name": "الاسم", "isBoardMember": "هل عضو مجلس ادارة", "assignedGroup": "المجموعة المخصصة", "bank": "البنك", "authorizationMatrix": "مصفوفة الصلاحيات", "authorizationMatrixType": "نوع مصفوفة الصلاحيات", "authorizedSignatureGroup": "مجموعات المخولين بالتوقيع", "authorizedSignatories": "المخولين بالتوقيع", "boardTermExpiryDate": "تاريخ انتهاء مدة مجلس الإدارة", "setAuthorizationMatrixType": "حدد نوع مصفوفة الصلاحيات", "addAuthorizedSignatureGroup": "إضافة مجموعات المخولين بالتوقيع", "authorizedSignatureGroupList": "قائمة مجموعات المخولين بالتوقيع", "addAuthorizedSignatory": "إضافة مخول بالتوقيع", "authorizedSignatoriesList": "قائمة المخولين بالتوقيع", "bankInformation": "معلومات البنك", "selectBank": "اختر البنك", "accountCurrency": "عملة الحساب", "worshipPlaceNameAr": "اسم دار العبادة (العربية)", "worshipPlaceNameEn": "اسم دار العبادة (الإنجليزية)", "religion": "الديانة", "sect": "الطائفة", "worshipPlaceType": "نوع دار العبادة", "branchMembersList": "قائمة أعضاء الفرع", "membershipNumber": "رقم العضوية", "emiratesId": "رقم الهوية", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "gender": "الجنس", "nationality": "الجنسية", "emirate": "الإمارة", "membershipStatus": "حالة العضوية", "newBranchDetails": "بيانات الفرع الجديد", "branchNameAr": "إسم الفرع (العربية)", "branchNameEn": "إسم الفرع (الإنجليزية)", "branchEmirate": "إمارة الفرع", "branchManager": "مدير الفرع", "selectBranchManager": "اختر مدير الفرع", "title": "مراجعة وتقديم الطلب", "entityDetails": "بيانات الجهة", "EntityCategory": "تصنيف الجهة", "NpoNameEn": "اسم مؤسسة النفع العام (الإنجليزية)", "NpoNameAr": "اسم مؤسسة النفع العام (العربية)", "npoLegalForm": "الشكل القانوني", "NpoDeclarationDecision": "رابط قرار إشهار مؤسسة النفع العام", "MainCategory": "التصنيف الرئيسي", "HeadquarterEmirate": "إمارة المقر الرئيسي", "LicensingEntity": "جهة الترخيص", "LicenseNumber": "رقم الترخيص", "LicenseIssuanceDate": "تاريخ إصدار الترخيص", "LicenseExpiryDate": "تاريخ انتهاء صلاحية الترخيص", "externalEntityDetails": "تفاصيل الجهه الخارجية", "requestType": "نوع الطلب", "specifiedDate": " تاريخ البدء", "entityName": "اسم الجهة", "entityLegalForm": "الشكل القانوني للجهة", "headquartersCountry": "دولة المقر", "theNatureOfTheEntityMainActivities": "طبيعة الأنشطة الرئيسية للجهة", "objectivesAndGoalsOfTheEntity": "أهداف وأغراض الجهة", "goalsAndObjectivesCompatibility": "مدى توافق أهداف وأغراض الجهة الخارجية مع أهداف وأغراض مؤسسة النفع العام", "ministryComments": "ملاحظات الوزارة", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "landlineNumber": "رقم الهاتف الثابت", "uploadDocuments": "المستندات", "exceptionCases": "حالات الاستثناء", "nationalityType": "نوع الجنسية", "founderEmiratesID": "الهوية الاماراتية", "dateOfBirth": "تاريخ الميلاد", "exceptionReasonAr": "سبب الاستثناء (عربي)", "exceptionReasonEn": "سبب الاستثناء (انجليزي)", "Date": "التاريخ", "Agenda": "جدول الأعمال", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإشهار", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "COMMENT": "تعليق"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "ص<PERSON><PERSON><PERSON> العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لمؤسسة النفع العام", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "عنوان البريد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لمؤسسة النفع العام", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لمؤسسة النفع العام"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "requestToJoinNpo": {"title": "طلب الالتحاق بمؤسسة نفع عام", "desc": "", "forms": {"memberInformation": {"title": "بيانات العضو", "memberInformation": "بيانات العضو", "memberPersonalDetails": "البيانات الشخصية للأعضاء", "emiratesId": "رقم الهوية الإماراتية للعضو", "emirate": "الإمارة", "memberNameEn": "اسم العضو (إنجليزي)", "memberNameAr": "اسم العضو (عربي)", "gender": "جنس العضو", "dateOfBirth": "تاريخ ميلاد العضو", "nationality": "جنسية العضو", "passportNumber": "رقم جواز السفر", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "unifiedNumber": "الرقم الموحد", "mobile": "رقم الموبايل", "address": "العنوان", "email": "الب<PERSON>يد الإلكتروني", "qualificationAndProfession": "المؤهلات والمهنة", "qualification": "مؤهل", "employer": "جهة العمل", "jobTitle": "المسمى الوظيفي", "branchName": "اسم الفرع"}, "membershipInformation": {"title": "بيانات العضوية", "membershipInformation": "بيانات العضوية", "membershipDetails": "تفاصيل العضوية", "membershipNumber": "رقم العضوية", "membershipValidityAndStatus": "صلاحية العضوية وحالتها", "membershipIssueDate": "تاريخ إصدار العضوية", "membershipExpiryDate": "تاريخ إنتهاء العضوية", "membershipStatus": "حالة العضوية"}, "requestHistory": {"title": "سجل الطلب"}, "uploadDocuments": {"title": "المستندات", "logoEn": "شعار مؤسسة النفع العام (بالإنجليزية)", "logoAr": "شعار مؤسسة النفع العام (بالعربية)", "npoLogo": "شعار مؤسسة نفع عام", "acceptedFiles": "أنواع الملفات المقبولة: JPG، PNG", "maxFileSize": "الح<PERSON> الأقصى لحجم الملف"}, "reviewAndSubmit": {"membershipInformation": "بيانات العضوية", "membershipDetails": "تفاصيل العضوية", "membershipNumber": "رقم العضوية", "membershipValidityAndStatus": "صلاحية العضوية وحالتها", "membershipIssueDate": "تاريخ إصدار العضوية", "membershipExpiryDate": "تاريخ إنتهاء العضوية", "membershipStatus": "حالة العضوية", "memberInformation": "بيانات العضو", "memberPersonalDetails": "البيانات الشخصية للأعضاء", "emiratesId": "رقم الهوية الإماراتية للعضو", "emirate": "الإمارة", "memberNameEn": "اسم العضو (إنجليزي)", "memberNameAr": "اسم العضو (عربي)", "gender": "جنس العضو", "dateOfBirth": "تاريخ ميلاد العضو", "nationality": "جنسية العضو", "passportNumber": "رقم جواز السفر", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "unifiedNumber": "الرقم الموحد", "mobile": "رقم الموبايل", "address": "العنوان", "email": "الب<PERSON>يد الإلكتروني", "qualificationAndProfession": "المؤهلات والمهنة", "qualification": "مؤهل", "employer": "جهة العمل", "jobTitle": "المسمى الوظيفي", "branchName": "اسم الفرع", "title": "مراجعة وتقديم الطلب", "reqeustToJoinNpo": "طلب الالتحاق بمؤسسة نفع عام", "downloadBylaws": "تحميل النظام الأساسي", "getFundingMembersConfirmation": "الحصول على تأكيد الأعضاء المؤسسين", "submitRequest": "تقديم الطلب", "submitRequestedUpdates": "تقديم التعديلات المطلوبة", "saveAsDraft": "ح<PERSON>ظ كمسودة", "cancelRequest": "إلغاء الطلب", "uploadDocuments": "المستندات", "LogoEn": "الشعار (بالإنجليزية)", "LogoAr": "الشعار (بالعربية)", "status": "الحالة", "responseDate": "تاريخ الرد", "yes": "نعم", "no": "لا", "ServiceTitle": "عنوان الخدمة (الإنجليزية)", "ServiceTitleAR": "عنوان الخدمة (عربي)"}, "requestDetails": {"membershipInformation": "بيانات العضوية", "membershipDetails": "تفاصيل العضوية", "membershipNumber": "رقم العضوية", "membershipValidityAndStatus": "صلاحية العضوية وحالتها", "membershipIssueDate": "تاريخ إصدار العضوية", "membershipExpiryDate": "تاريخ إنتهاء العضوية", "membershipStatus": "حالة العضوية", "memberInformation": "بيانات العضو", "memberPersonalDetails": "البيانات الشخصية للأعضاء", "emiratesId": "رقم الهوية الإماراتية للعضو", "emirate": "الإمارة", "memberNameEn": "اسم العضو (إنجليزي)", "memberNameAr": "اسم العضو (عربي)", "gender": "جنس العضو", "dateOfBirth": "تاريخ ميلاد العضو", "nationality": "جنسية العضو", "passportNumber": "رقم جواز السفر", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "unifiedNumber": "الرقم الموحد", "mobile": "رقم الموبايل", "address": "العنوان", "email": "الب<PERSON>يد الإلكتروني", "qualificationAndProfession": "المؤهلات والمهنة", "qualification": "مؤهل", "employer": "جهة العمل", "jobTitle": "المسمى الوظيفي", "branchName": "اسم الفرع", "title": "مراجعة وتقديم الطلب", "reqeustToJoinNpo": "طلب الالتحاق بمؤسسة نفع عام", "NPO_LICENSE_DECLARATION": "طلب إنشاء مؤسسات النفع العام", "NPO_ByDecree": "طلب تسجيل مؤسسات النفع العام", "APPLICATION_NUMBER": "رق<PERSON> الطلب", "APPLICATION_STATUS": "حالة الطلب", "SERVICE_NAME": "اسم الخدمة", "SUBMIT_DATE": "تاريخ التقديم", "APPLICATION_SUMMARY": "ملخص الطلب", "EDIT_INFORMATION": "تعديل المعلومات", "DOWNLOAD_NPO_BY_LAW": "تحميل النظام الأساسي للجمعية", "DOWNLOAD_NPO_DECLARATION_DECISION": "تحميل الإشهار", "DOWNLOAD_NPO_LICENSE": "تحميل الرخصة", "ERROR_IN": "خطأ في", "MODIFICATION_REQUIRED_IN": "مطلوب التعديل في", "INCLUDE_SECTION": "<PERSON><PERSON><PERSON> القسم الخاص ب", "COMMENT": "تعليق", "ServiceTitle": "عنوان الخدمة (الإنجليزية)", "ServiceTitleAR": "عنوان الخدمة (عربي)", "id": "رقم", "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك، نتوقع الرد خلال 5 أيام عمل.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "feedBack": {"Title": "الملاحظات", "index": "#", "fieldName": "اسم الحقل", "sectionName": "اسم القسم", "reviewerComments": "تعليقات المراجع", "action": "الإجراء", "goToSection": "انتقل إلى القسم"}}, "approval": {"npoLegalFormTitle": "الشكل القانوني", "academicQualification": "المؤهل العلمي", "selectAcademicQualification": "حدد المؤهل العلمي", "jobTitle": "المسمى الوظيفي", "selectJobTitle": "المسمى الوظيفي", "employer": "ص<PERSON><PERSON><PERSON> العمل", "selectEmployer": "ص<PERSON><PERSON><PERSON> العمل", "qualificationInformation": "معلومات المؤهل", "personalInformation": "معلومات شخصية", "passportPhoto": "صورة جواز السفر", "personalPhoto": "صورة شخصية", "confirm": "تأكيد", "reject": "<PERSON><PERSON><PERSON>", "download": "تنزيل النظام الأساسي لمؤسسة النفع العام", "residencyExpiryDate": "تاريخ انتهاء الإقامة", "residencyIssuanceDate": "تاريخ إصدار الإقامة", "emirate": "الإمارة", "passportNumber": "رقم جواز السفر", "nationality": "الجنسية", "mobileNumber": "رقم الهاتف الموبايل", "email": "عنوان البريد الإلكتروني", "nameEnglish": "الاسم (باللغة الإنجليزية)", "nameArabic": "الاسم (باللغة العربية)", "dob": "تاريخ الميلاد", "emiratedIdNumber": "رقم الهوية الإماراتية", "tableProposedNameAr": "الاسم المقترح (بالعربية)", "tableProposedNameEn": "الاسم المقترح (بالإنجليزية)", "tableId": "بطاقة تعريف", "npoLegalFormAndProposedNames": "الأسماء المقترحة لمؤسسة النفع العام", "foundingMemberConfirmation": "تأكيد عضوية مؤسس لمؤسسة النفع العام"}, "congratulations": "تم تقديم الطلب بنجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "تم الانتهاء من استكمال بيانات الطلب، بانتظار تأكيد الأعضاء المؤسسين لتقديم الطلب بنجاح", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>"}, "generalForm": {"Service Form": "نموذج الخدمة", "description": "الوصف", "issue971Title": "طلب إصدار 971 من بطاقة عضوية المجتمع", "reportingAbuseTitle": "الإبلاغ عن إساءة", "licensingNonGovPodCenterTitle": "ترخيص مركز رعاية وتأهيل لأصحاب الهمم غير الحكومي", "RequestAdTitle": "طلب نشر إعلان لمراكز الاستشارات الأسرية", "renewfamilyCounselingLicenseTitle": "طلب تجديد ترخيص مراكز الاستشارات الأسرية", "renewNonGovPodCenterTitle": "طلب تجديد ترخيص المؤسسات غير الحكومية لتأهيل أصحاب الهمم", "licensingPrivateFamilyCounselingCenterTitle": "طلب ترخيص لمراكز الاستشارات الأسرية الخاصة"}, "toWhomApply": {"title": "إصدار شهادة لمن يهمه الأمر بشأن الدعم الاجتماعي", "desc": "يتم اصدار رسالة لمن يهمه الأمر للأشخاص والجهات وذلك لمعرفة هل الاشخاص المستعلم عنهم يتقاضون المساعدة الاجتماعية من الوزارة أم لا. يتوجب عليك انشاء ملف شخصي للاستفادة من الخدمة.", "preferredEmail": " البريد الإلكتروني (سيتم إرسال الشهادة إلى البريد الالكتروني أدناه)", "preferredMobile": " رقم الهاتف المتحرك (سيتم إرسال الشهادة إلى رقم الهاتف أدناه)", "entityAddressed": "الجهة المعنية", "otherEntity": "الجهات الأُخرى", "typeOfCertificate": "نوع الشهادة", "otherEntityAddressed": "جهات موجهة أُخرى"}}, "submitResult": {"congratulations": "نجاح", "submitType1": "تم حفظ المسودة بنجاح", "submitType2": "لقد قمت بنجاح بتقديم طلبك للحصول على تأكيدات الأعضاء المؤسسين", "submitType3": "لقد قمت بتقديم طلبك بنجاح", "reviewMessage": "نحن نقوم بمراجعة طلبك، نتوقع الرد خلال 5 أيام عمل.", "appReferenceNumber": "الرقم المرجعي للطلب", "emailNotification": "سيتم إعلامك بأي تغيير في حالة طلبك عبر البريد الإلكتروني", "smsNotification": "والرسائل القصيرة", "applicationStatus": "حالة طلبك متاحة في", "myApplications": "طلباتي", "myCases": "طلباتي", "backToServices": "العودة إلى الخدمات", "Save": "<PERSON><PERSON><PERSON>", "SubmissionFailed": "فشل تقديم الطلب", "SubmissionFailedDesc": "كانت هناك مشكلة في تقديم طلبك"}, "alert": {"deleteTitle": "<PERSON>ذ<PERSON> الصف", "saveTheRequestAsDraftTitle": "تأكيد", "saveTheRequestAsSubmittTitle": "تأكد إرسال", "title": "هل أنت متأكد؟", "text": "أنت على وشك تقديم النموذج.", "confirmDeleteFile": "هل انت متأكد من قيامك بحذف الملف؟", "icon": "warning", "showCancelButton": true, "confirmButtonColor": "#3085d6", "cancelButtonColor": "#d33", "cancelButtonText": "لا", "confirmButtonText": "نعم", "cancelDeleteButtonText": "الغاء", "confirmDeleteButtonText": "<PERSON><PERSON><PERSON>"}, "notify": {"deleteMessage": "هل أنت متأكد أنك تريد حذف هذا الصف؟ لا يمكن التراجع عن هذا الإجراء.", "saveTheRequestAsDraftMessage": "هل أنت متأكد أنك تريد حفظ الطلب كمسودة؟", "saveTheRequestAsSubmitMessage": "هل أنت متأكد أنك تريد تقديم الطلب؟", "saveTheRequestAsConfirmMessage": "هل أنت متأكد من أنك تريد تقديم الطلب للحصول على تأكيد الأعضاء المؤسسين؟", "success": "نجاح", "error": "خطأ", "warning": "تحذير ", "info": "معلومات", "comingSoon": "قريبا سوف يتم تفعيل الخدمة...", "validForm": "النموذج صالح", "invalidForm": "ير<PERSON>ى ملء الحقول المطلوبة", "invalidFileSize": "حجم الملف اكبر من المسموح", "invalidFileFormat": "نوع الملف غير مسموح", "noInternet": "لا يوجد اتصال بالانترنت", "consentText": "يرجى الموافقة على الشروط والأحكام", "draftSaved": "تم حفظ مسودة بنجاح", "submitted": "تم تقديم الطلب بنجاح", "sameApplicantApplyAnother": "معلومات المستخدم مطابقة لمعلومات المقدم عنه، يرجى اختيار تقديم لنفسي", "invalidObjectives": "الرجاء إضافة على الأقل 3 أهداف", "invalidMembers": "يجب ألا يقل عدد المؤسسين عن 7 أعضاء.", "invalidEmiratesIdDetails": "مدخلات غير صحيحة للبطاقة", "anotherSameAsApplicant": "المستخدم الذي تحاول التقديم إليه هو نفس المستخدم الذي قام بتسجيل الدخول، يرجى تحديدي بدلاً من آخر", "emirateIdExists": "بطاقة الهوية الإماراتية موجودة بالفعل", "invalidAge": "يج<PERSON> ألا يقل عمر الأعضاء عن 21 عامًا.", "invalidAge18": "يج<PERSON> ألا يقل عمر ولي الأمر عن 18 عامًا.", "memberAdded": "تم اضافة العضو بنجاح", "sameAsGuardian": "رقم الهوية الإماراتية للطفل وولي الأمر هو نفسه. يرجى تحديث التفاصيل.", "invalidAgeToApply": "عمر الطفل أكثر من خمس سنوات.لم يتم بعد بدء التسجيل في مراكز الرعاية والتأهيل - أصحاب الهمم.سيتم إعلامكم في وقت لاحق", "isNotNational": "وفقًا لمتطلبات النظام، يحق فقط لمواطني دولة الإمارات العربية المتحدة أو أبناء المواطنات الإماراتيات التقدم لهذه الخدمة. حدد 'نعم' للمتابعة أو 'لا' لإنهاء عملية التقديم.", "invalidEmirate": "هذه الخدمة غير متوفرة لسكان أبوظبي", "sameAsApplicant": "رقم هوية الطفل المدخلة تماثل رقم هوية المستخدم في الدخول عبر الهوية الرقمية، فقط وصي الطفل يستطيع التقديم إلى هذه الخدمة - يجب تسجيل الدخول بهوية ولي الأمر قبل البدء بالخدمة", "familyMembersNotUpdated": "يرجى تحديث بيانات العضويات العائلية", "fileDeletedSuccess": "تم حذف الم<PERSON><PERSON> بنجاح", "proposedNameExistsError": "الاسم المقترح موجود بالفعل", "objectiveIsAlearyExist": "الهد<PERSON> المدخل موجود بالفعل.", "conditionExistsError": "هذا الشرط موجود بالفعل.", "beneficiaryExistsError": "المسفتيد موجود بالفعل", "positionExistsError": "الوظيفة موجود بالفعل", "complaintReopened": "تم اعادة فتح الشكوى بنجاح", "saveTheRequestAsDraft": "هل أنت متأكد أنك تريد حفظ الطلب كمسودة؟", "submitTheRequest": "هل أنت متأكد أنك تريد تقديم الطلب؟", "DataNotCorrect": "البيانات غير صحيحة.", "invalidDateFormat": "التاريخ غير صالح", "ThisMemberIsAlreadyAdded": "هذا العضو تمت إضافته بالفعل.", "minFundIs5Million": "ال<PERSON><PERSON> الأدنى لقيمة الاموال هو 5 ملايين", "unionCategoryMismatch": "يرجى التأكد من أن يكون نفس التصنيف الرئيسي معين لجميع الجمعيات أو المؤسسات الأهلية المشاركة في تأسيس الاتحاد", "npoListWillBeAssociationsOrNationalSocieties": "يرجى التأكد من أن يكون الشكل القانوني موحد لجميع الجمعيات أو المؤسسات الأهلية المشاركة في تأسيس الاتحاد.", "invalidNPONumber": "خطا في البيانات", "NPONumberIsAlreadyExists": "رقم/اسم مؤسسة النفع العام موجود بالفعل", "FundServicesIsAlearyExist": "خدمة الصندوق موجودة بالفعل.", "targetGroupExistsError": "اسم الفئة المستهدفة موجود بالفعل", "purposesAndActivitieIsAlreadyExist": "الغرض و النشاط موجود بالفعل", "participantExistsError": "المشارك موجود بالفعل.", "cashDonationTypeExistsError": "نوع التبرع النقدي موجود بالفعل.", "inkindDonationTypeExistsError": "نوع التبرع العيني موجود بالفعل.", "authorizedSignatoryExistsError": "العضو موجود بالفعل.", "authorizedGroupExistsError": "المجموعة موجودة بالفعل", "exampleOfActivitiesAlreadyExists": "المثال على النشاط الموجود بالفعل"}, "userPages": {"applications": {"title": "طلباتي"}, "drafts": {"title": "مسوداتي"}, "socialCases": {"title": "الحالات الاجتماعية"}, "readyToPay": {"title": "طلباتي الجاهزة للدفع"}, "financial": {"title": "معاملاتي المالية"}, "cases": {"title": "الاستفسارات والملاحظات"}, "documents": {"title": "مستنداتي", "DocumentNumber": "رقم الوثيقة", "Status": "الحالة", "IssueDate": "تاريخ الاصدار", "ExpiryDate": "تاريخ الانتهاء", "confirmText": "متعاملنا العزيز.. سيقوم النظام بإنشاء طلب جديد للدراسة يتيح تحميل المستند الذي يخص نوع الإعاقة الجديد. في حال تمت الموافقة على تغيير نوع الإعاقة، سيتم إلغاء الطلب السابق. في حال تم رفض طلبكم في تغيير نوع الإعاقة، يبقى الطلب السابق في حالة: موافقة", "ChangeDisability": "تغيير نوع الاعاقة"}, "myEstablishments": {"title": "مؤسساتي", "unifiedNumber": "الرقم الموحد", "establishmentName": "اسم المؤسسة", "legalForm": "الصيغة القانونية", "emirate": "الإمارة", "licenseExpiryDate": "تاريخ انتهاء الرخصة", "status": "الحالة", "actions": "الإجراءات", "viewEstablishment": "عرض المؤسسة", "editEstablishment": "تعديل بيانات المؤسسة", "noDataFound": "لايوجد بيانات"}}, "validationMessage": {"required": "هذا الحقل مطلوب", "invalidDate": "صيغة غير صحيحة", "minlength": "الحد الادنى غير صحيح", "maxlength": "تم تجاوز الحد الاقصى", "min": "القيمة الأدنى غير مستوفاة. القيمة الأدنى هي MinimumValue", "max": "تم تجاوز القيمة القصوى. القيمة القصوى هي MaximumValue", "email": "عنوان البريد الإلكتروني غير صالح", "pattern": "النمط غير متطابق", "arabicOnly": "هذا الحقل يقبل حروف عربية فقط", "englishOnly": "هذا الحقل يقبل حروف انجليزية فقط", "customEmail": "تنسيق البريد الإلكتروني غير صالح", "customMobile": "صيغة رقم الموبايل غير صحيحة، يجب أن يبدأ الموبايل بـ 5 971", "invalidFaxNumber": "يجب أن يبدأ رقم الفاكس بـ 9716 او 9719", "invalidLandlineNumberStartWith6": "يجب أن يبدأ رقم الهاتف الثابت بـ 9716", "invalidLandlineNumberStartWith9": "يجب أن يبدأ رقم الهاتف الثابت بـ 9719", "invalidLandlineNumberStartWith5": "يجب أن يبدأ رقم الهاتف الثابت بـ 9715", "invalidLandlineNumberLength": "رقم الهاتف الثابت غير صحيح", "invalidEmiratesId": "تنسيق رقم الهوية الإماراتية غير صالح", "matDatetimePickerMax": "القيمة المحددة للتاريخ والوقت غير صالحة، يرجى تحديد تاريخ ووقت صالحين.", "matDatetimePickerMin": "يجب أن يكون تاريخ ووقت اجتماع اللجنة المؤقتة بعد أو يساوي اجتماع المؤسسين، وألا يتجاوز اليوم.", "matDatepickerMax": "يجب أن يكون التاريخ المحدد أصغر من التاريخ أو يساويه.", "minLength": "يقبل هذا الحقل 3 أحر<PERSON> ك<PERSON>د أدنى", "minLengthIs2": "يقبل هذا الحقل 2 أحر<PERSON> ك<PERSON>د أدنى", "maxLength": "يقبل هذا الحقل 500 أحرف كحد أقصي", "containsNumbers": "هذا الحقل يقبل الحروف فقط", "invalidPatternEn": "هذا الحقل يقبل الأحرف الإنجليزية فقط", "invalidPatternAr": "هذا الحقل يقبل الأحرف العربية فقط", "invalidCharacters": "يسمح فقط بالأحرف العربية، الإنجليزية، والمسافات", "invalidNumber": "يجب أن تكون القيمة أكبر من 0", "invalidIban": "رقم IBAN غير صالح", "invalidBankAccountFormat": "رقم الحساب البنكي غير صالح", "invalidText": "الرجاء إدخال نص صالح.", "invalidPattern2En": "هذا الحقل يقبل الأحرف الإنجليزية", "invalidPattern2Ar": "هذا الحقل يقبل الأحرف العربية", "dateRangeInvalid": "يجب أن يكون تاريخ الانتهاء بعد تاريخ البدء", "startDateInvalidRange": "يجب أن يكون تاريخ البدء قبل تاريخ الانتهاء.", "endDateInvalidRange": "يجب أن يكون تاريخ الانتهاء بعد تاريخ البدء .", "matDatepickerMin": "يجب أن يكون التاريخ أكبر من أو يساوي اليوم", "invalidPatternNotNumOnly": "يُسمح بالحروف العربية أو الإنجليزية ولا يُسمح بالأرقام فقط، والمسافات مسموحة.", "lengthMemberError": "يجب عليك اختيار فرع الإمارة الذي يحتوي على 7 أعضاء أو أكثر", "internationalPhone": "رقم الهات<PERSON> الدولي غير صحيح", "accountMismatch": "رقم الحساب لا يتطابق مع رقم IBAN."}, "npoNationalRegistry": {"dashboard": {"title": "NPO National Registry", "npoLegalFormTitle": "UAE NPO / Legal Form", "npoInUae": "NPO In UAE", "npoEmiratesCategory": "NPO Emirates Category", "npoCategoryPerEmirate": "NPO Category Per Emirate", "npoCountPerCategory": "NPO Count Per Category Across UAE"}, "npoList": {"title": "قائمة مؤسسات النفع العام", "name": "الاسم", "legalForm": "الصيغة القانونية", "unifiedNumber": "الرقم الموحد", "declarationDate": "تاريخ الاشهار", "emirate": "الإمارة", "licensingAuthority": "الجهة المرخصة", "mainCategory": "الفئة الرئيسية", "licenseStatus": "حالة الترخيص", "joinUs": "انضم الينا", "viewDetails": "عرض التفاصيل"}, "npoDetails": {"basicInformation": {"title": "المعلومات الاساسية", "npoName": "اسم مؤسسة النفع العام", "npoLegalForm": "الشكل القانوني", "npoContactDetails": "تفاصيل الاتصال بمؤسسة النفع العام", "name": "الاسم", "localDecreeLawNumber": "رقم المرسوم/القانون المحلي", "issuanceDate": "تاريخ الإصدار", "associationClassification": "تصنيف الجمعية", "landlineNumber": "رقم الهاتف الثابت", "email": "الب<PERSON>يد الإلكتروني", "website": "الموقع الإلكتروني", "address": "العنوان", "geographicLocation": "الموقع الجغرافي", "emirate": "الإمارة"}, "objectives": {"title": "قائمة الاهداف", "objectiveEn": "الهدف (بالإنجليزية)", "objectiveAr": "الهدف (بالعربية)", "meansOfAchievingObjectiveEn": "وسائل تحقيق الهدف (بالإنجليزية)", "meansOfAchievingObjectiveAr": "وسائل تحقيق الهدف (بالعربية)"}, "membership": {"title": "العضوية والتسجيل", "membershipInformation": "معلومات العضوية", "membershipConditions": "أنواع و شروط العضوية", "membershipFees": "رسوم العضوية", "enrollmentFees": "رسوم الالتحاق", "category": "الفئة", "membershipConditionEn": "أنواع و شروط العضوية (بالإنجليزية)", "membershipConditionAr": "أنواع و شروط العضوية (بالعربية)"}, "boardOfDirectors": {"title": "مجلس الادارة", "boardOfDirectorsInformation": "معلومات مجلس الإدارة", "numberOfBoardMembers": " عدد أعضاء مجلس الإدارة ", "boardOfDirectorsPositions": "المناصب الإدارية", "adminPositionTitleArabic": "المنصب الإداري (بالعربية)", "adminPositionTitleEnglish": "المنصب الإداري (بالانجليزية)"}}}, "AED": "درهم إماراتي", "Save": "<PERSON><PERSON><PERSON>", "Show per page": "عرض لكل صفحة", "ViewAndEditApplication": "عرض/تعديل الطلب", "View Application": "عر<PERSON> الطلب", "Delete Application": "<PERSON><PERSON><PERSON> الطلب", "New": "جديد", "Application Overview": "نظرة عامة على الطلب", "Keep track of your applications and tasks": "تتبع طلباتك ومهامك", "Here you can easily access your applications, payments, and receive smart suggestions based on your data from UAE Pass": "هنا يمكنك الوصول بسهولة إلى طلباتك ومدفوعاتك والحصول على اقتراحات ذكية بناءً على بياناتك من UAE Pass", "Search for anything": "ابحث عن أي شيء", "Go to Main Website": "الذها<PERSON> إلى الموقع الرئيسي", "All Services": "جميع الخدمات", "Once you complete an application it will be available here.": "بم<PERSON>رد إكمال الطلب، سيكون متاحًا هنا.", "You have no applications": "ليس لديك طلبات", "missingRequiredFields": "الحقول المطلوبة مفقودة", "missingRequiredDocs": "المستندات المطلوبة المفقودة"}