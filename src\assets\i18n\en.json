{"hello": "Hello", "home": "My MOCE", "languages": "Languages", "No documents required": "No documents required", "service_uae_national_only": "Only Emirati citizen can apply for this service", "More": "More", "about": "About", "servicesHeader": "Services & Information", "Welcome": "Welcome, ", "Sign out": "Sign out", "First Name": "First Name", "Last Name": "Last Name", "Summary": "Summary", "Next": "Next", "gender-error": "Gender should be female", "dob-error": "Date of birth validation failed", "invalid-emirates-id": "Invalid Emirates ID", "Full Name": "Full Name", "Your UAE Pass": "Your UAE Pass", "Phone number": "Phone number", "Date of Birth": "Date of Birth", "Previous": "Previous", "Attachments": "Attachments", "Submit": "Submit", "Pay": "Pay", "Close": "Close", "Application Number": "Application Number", "Help": "Help", "FAQs": "FAQs", "Contact us": "Contact us", "Need Help?": "Need Help?", "Your preferred mobile": "Your preferred mobile", "8000000": "8000000", "Customer Charter": "Customer Charter", "loading": "Loading...", "Search": "Search", "itemsPerPage": "items", "Total count": "Total count", "Email": "Email", "Mobile": "Mobile", "MobileV2": "Mobile ((971) 5000000000)", "Preferred Email": "Preferred Email", "Preferred Mobile": "Preferred Mobile", "Preferred MobileV2": "Preferred Mobile  ((971) 5000000000)", "City": "City", "Address": "Address", "Country": "Country", "Emirate": "Emirate", "Emirate with search": "Emirate with search", "Gender": "Gender", "Choose a file": "Upload file", "format": "Format: PDF, JPG, PNG Max file size: 5MB", "formatPhoto": "Format: JPG, PNG Max file size: 5MB", "formatOnly": "Format: ", "MaxFileSizeOnly": "Max file size: ", "Emirates ID": "Emirates ID", "Please enter": "Enter ", "Passport": "Passport", "Please select...": "Select ", "PleaseSelect": "Please Select", "Search...": "Search...", "The title of the card": "The title of the card", "langToggle": "EN", "Apply for": "Apply for", "Applicant Information": "Applicant Information", "Step One": "Step One", "Step Two": "Step Two", "Step Three": "Step Three", "Fill the form": "Fill the form", "Summary & Submit": "Summary & Submit", "noMatchingDataFound": "No matching data found", "Name": "Name", "Type": "Type", "Size": "Size", "Action": "Action", "bytes": "bytes", "Service": "Service", "Start Service": "Start Service", "Service Card": "Service Card", "Some quick example text": "Some quick example text", "In progress": "In progress", "dashboard-description": "Here you can easily access your applications, payments, and receive smart suggestions based on your data from UAE Pass.", "Completed": "Completed", "Returned": "Returned", "Ready To Pay": "Ready To Pay", "Draft": "Draft", "View All": "View All", "Progress": "Progress", "Service Form": "Service Form", "Additional Information": "Additional Information", "Add Member": "Add Member", "Add Objective": "Add Objective", "Add Example Of Activities": "Add Activity/Program", "Add Purpose / Activity": "Add Purpose / Activity", "Add Room Administrator": "Add Room Administrator", "Add Extension Request": "Add Extension Request", "Add FundServices": "Add New Service", "Conditions": "Conditions", "Edit Member": "Edit Member", "Cancel": "Cancel", "Reopened": "Reopened", "problemSolved": "Resolved", "Closed": "Closed", "inProgress": "In Progress", "saveAsDraft": "Save as Draft", "submitRequest": "Submit Request", "draftSaved": "Draft saved successfully", "Continue": "Continue", "consentText": "I agree to the terms and conditions", "readTermsAndConditions": "I have read and agree to the", "confirmAccurateInformation": "I confirm that the above information is accurate to my best of knowledge", "termsAndConditions": "Terms & Conditions", "acknowledgementAndUndertaking": "Acknowledgment and Declaration", "InformationProvided": "Information Provided", "Proposed": "Proposed", "Name (Google)": "Name (Google)", "DoYouAcceptThisTranslation": "Do you accept this translation", "yes": "Yes", "English": "English", "Arabic": "Arabic", "Other Languages": "Other Languages", "French": "French", "Spanish": "Spanish", "German": "German", "Portuguese": "Portuguese", "Russian": "Russian", "otherLanguageText": "The list of “Other languages” listed above use Google Translate to create an automated translation of content for the purpose of display. Accuracy of automated content translation is not guaranteed.", "dashboard": {"welcome": "Dear customer, we welcome you to the new unified services portal through which you can apply for services", "link": "Through the link"}, "sort": {"sortBy": "Sort by", "mostRelevant": "Most relevant", "oldestFirst": "Oldest first", "newestFirst": "Newest first"}, "footer": {"About MoCD": "About MoCD", "Cool stuff": "Cool stuff", "Random feature": "Random feature", "Team feature": "Team feature", "Support": "Support", "Resource": "Resource", "Resource name": "Resource name", "About Website": "About Website", "Team": "Team", "Locations": "Locations", "Privacy": "Privacy", "Terms": "Terms", "Guidelines": "Guidelines", "Contact Us": "Contact Us", "The site is best viewed in screen resolution 1920 x 1080. Support, Microsoft Edge, Firefox 10+, Google Chrome 12+. Safari 3+": "The site is best viewed in screen resolution 1920 x 1080. Support, Microsoft Edge, Firefox 10+, Google Chrome 12+. Safari 3+", "© Copyright 2025. Ministry of Community Empowerment - United Arab Emirates": "© Copyright 2025. Ministry of Community Empowerment - United Arab Emirates", "Last updated on": "Last updated on", "Desclaimer": "Desclaimer", "Privacy Policy": "Privacy Policy", "Terms & Conditions": "Terms & Conditions", "Copyrights": "Copyrights", "Mobile App": "Mobile App", "Channel Programs": "Channel Programs", "Sitemap": "Sitemap", "Archive": "Archive", "Careers": "Careers", "Frequently Asked Questions": "Frequently Asked Questions", "Accessibility Statement": "Accessibility Statement", "Whistleblower Form (IAO)": "Whistleblower Form (IAO)", "Remote Access": "Remote Access", "Employee Mail": "Employee Mail", "Communicate with leadership": "Communicate with leadership", "Customer Happiness Centers": "Customer Happiness Centers", "Customer Happiness Charter": "Customer Happiness Charter", "UAE Promise Guidelines": "UAE Promise Guidelines", "copyrights": "All Rights Reserved. Ministry of Community Empowerment - United Arab Emirates", "Follow us on": "Follow us on", "Laws, Legislation and Policies": "Laws, Legislation and Policies", "Disclaimer": "Disclaimer", "About the website": "About the website", "Information and support": "Information and support", "About MOCD": "About MOCD", "About the minister": "About The Minister", "About the ministry": "About The Ministry", "Toll free": "Toll Free", "The UAE charter for Future Services": "The UAE charter for Future Services", "Awards": "Awards", "Procurement": "Procurement", "The Ministry": "The Ministry", "Using the website": "Using the website", "Terms and conditions": "Terms and conditions", "Accessibility": "Accessibility", "Digital participation policy": "Digital participation policy", "Services catalogue": "Services catalogue", "Media centre": "Media centre", "FAQ’s": "FAQ", "Feedback and complaints": "Feedback and complaints", "References": "References", "Regulations": "Regulations", "Media kit": "Media kit", "Abbreviations and glossary": "Abbreviations and glossary", "Number of visitors": "Number of visitors", "Load Time": "Load Time", "AboutUs": "About Us", "CopyRights": "Copy Rights"}, "attachment": {"PersonalPhoto": "Upload Personal Photo", "MedicalReport": "Upload Medical Report", "PsychologicalEducationalReport": "Psychological/Educational report", "Other": "Other supportive document", "incomeStatement": "Income Statement", "realstateEvidence": "Property Documents", "bankLetter": "Bank Letter", "bankStatement": "Bank Statement"}, "applications": {"ApplicationNumber": "Application Number", "ServiceName": "Service Name", "Status": "Status", "SubmissionDate": "Submission Date", "FinalComment": "Final Comment", "Actions": "Actions"}, "fundraisingPermitList": {"title": "Fundraising Permit List", "permitNumber": "Permit Number", "establishmentName": "Establishment Name", "legalForm": "Legal Form", "location": "Location", "startDate": "Start Date", "endDate": "End Date", "applicationStatus": "Application Status", "permitStatus": "Permit Status", "actions": "Actions", "extendFundraisingPermit": "Extend Fundraising Permit", "appealToPermitRejection": "Appeal To Permit Rejection"}, "compliants": {"complaintNumber": "Inquiry / Suggestion ID", "complaintRequestType": "Inquiry / Suggestion Type", "raisedAt": "Raised At", "status": "Status", "reopenRequest": "Reopen Request", "reopenReason": "Reopen Reason", "ticketNumber": "Ticket Number", "title": "Title"}, "status": {"all": "All", "Completed": "Completed", "InProgress": "In Progress", "Submitted": "Submitted", "Draft": "Draft", "OnHold": "On Hold", "Rejected": "Rejected", "Approved": "Approved", "Returned": "Returned", "PendingPayment": "Pending Payment"}, "payment": {"Status": "Status", "Success": "Success", "Failed": "Failed", "Payment ID": "Payment ID", "Payment Provider Message": "Payment Provider Message", "Payment Date": "Payment Date", "Payment Amount": "Payment Amount"}, "services": {"title": "Services", "service1": {"title": "Service 1", "Service Form": "Service Form"}, "service2": {"title": "Service 2", "Service Form": "Service Form"}, "inflationService": {"title": "Inflation Service", "Form": {"caseInformation": {"title": "Case Information", "fields": {"reasonForApply": "Reason for applying"}}, "personalInformation": {"title": "Personal Information", "fields": {"emiratesId": "Emirates ID", "caseNumber": "Case Number", "maritalStatus": "Marital Status", "mobileNumber": "Mobile Number", "preferredMobileNumber": "Preferred Number", "email": "Email Address", "preferredEmail": "Prefered Email", "employmentStatus": "Emirate Residency", "emirateResidency": "Employment Status", "areaEmirate": "Area", "centerEmirate": "Center"}}, "familyBookInformation": {"title": "Family Information", "fields": {"usernameLabel": "Family Houshold Name", "familyBookNumber": "Family Book Number", "fullName": "Full Name", "NoOfHouseHold": "Number of Household", "relationship": "Relationship", "status": "Status", "action": "Action", "noFamilyBookData": "No Family Book data"}}, "inflationInformation": {"title": "Inflation Information", "fields": {"applyUtilityAllowance": "Would you like to apply for utility allowance as well?", "utilityProvider": "Utility Provider", "utilityAccountNumber": "Utility Account Number"}}, "attachments": {"title": "Attachments", "fields": {"salaryCertificate": "Salary Certificate"}}}}, "marriageGrant": {"title": "Marriage Grant", "desc": "Apply for Marriage Grant", "gotoMyApplications": "Go to My Applications", "NotEligible": "Not eligible", "tabs": {"title": {"serviceFormTabTitle": "Service Form", "husbandPersonalInfoTab": "Husband Personal Information", "wifePersonalInfoTab": "Wife Personal Information", "abstractEnrollmentInfoTab": "Family Book Information", "statementOfWorkTab": "Statement Of Work", "incomeStatementTab": "Income and Bank Statement", "attachmentTab": "Attachments", "summaryTab": "Summary"}}, "forms": {"husbandPersonalInfo": {"dateOfMarriageContract": "Date of Marraige Contract", "shariaCourtName": "Sharia Court Name", "NameAr": "Name (Arabic)", "NameEn": "Name (English)", "dob": "Date of Birth", "emiratesId": "Emirates Id", "educationLevel": "Education Level", "email": "Email", "mobile1": "Mobile", "mobile2": "Mobile 2"}, "wifePersonalInfo": {"NameAr": "Name (Arabic)", "NameEn": "Name (English)", "dob": "Date of Birth", "emiratesId": "Emirates Id", "educationLevel": "Education Level", "email": "Email", "mobile": "Mobile"}, "abstractEnrollmentInfo": {"familyBookNumber": "Family Book Number", "townNumber": "Town", "familyNumber": "Family Number", "dateOfIssuanceFamilyBook": "Family Book Issue Date", "placeOfIssuanceFamilyBook": "Family Book Issue Place"}, "statementOfWork": {"employerCategory": "Employer Category", "employer": "Employer Name", "placeOfWork": "Place of Work"}, "incomeStatement": {"totalMonthlyIncome": "Total Monthly Income", "bankName": "Bank Name", "iban": "International Banking Number (IBAN)"}, "attachment": {"incomeStatement": "Income Statement", "realstateEvidence": "Property Documents", "bankLetter": "IBAN Letter", "bankStatement": "Bank Statement of the last 6 months", "salaryCertificate": "Salary Certificate", "salaryCertificateDesc": "Detailed Salary Certificate including:Basic Salary, Allowances, Pension Deduction, Housing Allowance (if applicable)", "propertyDocuments": "Property Documents", "propertyDocumentsDesc": "For applicants from Sharjah, please provide certificates issued by the Sharjah Real Estate Registration Department and the Department of Town Planning and Survey", "Other": "Other Supporting Document"}, "summaryForm": {"consentText": "Please agree to the terms and conditions", "termConditions": "You can read the terms and conditions here", "terms": "I hereby undertake to notify the Ministry of any changes in these statements or documents within a period not exceeding two weeks from the date of the change or amendment and until I actually receive the full marriage grant. The Ministry of Community Empowerment has the right to communicate by telephone, e-mail, address the employer or any other entity before and after receiving the marriage grant to follow up on my social status. I also certify that I have read the terms and conditions of the marriage grant entitlement and The Ministry has the right to take all legal measures to ensure that the grant received in the event of non-compliance with the terms and conditions of the grant in full, or in case of non-submission of the legitimate entry or the continuation of the marriage and the entry of the wife within the specified period of conditions and controls (One calendar year from the date of receipt of the grant."}}}, "podNewCard": {"title": "Issue a New Card for People of Determination", "Service Form": "Service Form", "Labor Sector": "Labor Sector", "Employer": "Employer", "Job Title": "Job Title", "Employment Status": "Employment Status", "Are you employed?": "Are you employed?", "Are you student?": "Are you student?", "Educational Status": "Educational Status", "Educational Level": "Educational Level", "Registration Status": "Registration Status", "School": "Name of School / Center / University / Institute", "Grade": "Stage/ Grade", "Support?": "Does he receive Educational Private Support?", "Is there a shadow teacher?": "Is there a shadow teacher?", "Disability Type": "Disability Type", "Disability Information": "Disability Information", "Card Delivery": "Card Delivery", "deliveryAddress": "Delivery Address ", "Want card delivery?": "Want card delivery?", "Terms and Conditions": "Terms and Conditions", "1": "The Card of People of Determination is a personal card issued by the Ministry of Community Empowerment to people with disabilities. It is considered to be an official document indicating that the holder is disabled to ensure the rights and services established in the Federal Persons with Disabilities Law No. 29 of 2006 Amended by Federal Law No. (14) of 2009.", "2": "Please read the following conditions carefully before completing the application process", "3": "These Terms and Conditions may be subject to change and the Ministry of Community Empowerment reserve the right to modify the Terms and Conditions for any reason, without notice at any time.", "4": "By pressing the completion of the application button, the applicant is deemed to have fully accepted to", "5": "Let specialist in the Ministry of Community Empowerment have full access to the information contained in the application", "6": "Request for your data (e.g., medical reports, psychological, etc.) from other entities", "7": "Refer your application to the evaluation team at the National Centre for Diagnosis and Evaluation of the Ministry of Community Empowerment.", "8": "Cancel the application if the applicant does not follow up or respond by providing additional information or documents required for the application. Application will be cancelled six months after the request for such information or documents.", "9": "If your application is rejected, you may appeal against the rejection decision through website by submitting reports and evidence justifying your appeal within no more than 60 days of the rejection of the application and only once.", "10": "Share the applicant's information with other government agencies for the necessity and for the benefit of the cardholder.", "11": "Share your disability data with ICA to view your disability statement on the back side of you UAE ID card.", "12": "Do not use the card for other ilegally authorized purposes", "13": "Disclosure of the death or migration of the cardholder", "14": "For further information please call 623 800"}, "podLostCard": {"title": "Replace Lost and Damage Card for People of Determination", "Service Form": "Service Form", "Mobile": "Mobile", "deliveryAddress": "Delivery Address ", "Search Type": "Search Type", "Search By": "Search By", "Card Number": "Card Number", "Emirates Id": "Emirates Id", "Expiry Date": "Expiry Date", "Person Name": "Person Name", "Note": "Note", "No Data Found": "No Data Found", "NoCardNumberSelected": "No card information selected", "Payment Amount": "Payment Amount", "PaymentNote": "Additional payment charges will be added by payment provider."}, "npo": {"basicData": {"title": "Declaring Non-Profit Organization", "Basic Data": "Basic Data", "Service Form": "Service Form", "First Proposed Name (English)": "First Proposed Name (English)", "First Proposed Name (Arabic)": "First Proposed Name (Arabic)", "Second Proposed Name (English)": "Second Proposed Name (English)", "Second Proposed Name (Arabic)": "Second Proposed Name (Arabic)", "Third Proposed Name (English)": "Third Proposed Name (English)", "Third Proposed Name (Arabic)": "Third Proposed Name (Arabic)", "Authority Feedback For Name (English)": "Authority Feedback For Name (English)", "Authority Feedback For Name (Arabic)": "Authority Feedback For Name (Arabic)", "Landline Number": "Landline Number", "PO Box": "PO Box", "Email": "Email", "Website": "Website", "Address": "Address", "Geographic Location": "Geographic Location", "Emirate": "Emirate", "Licensing Authority": "Licensing Authority", "Coordinates (X, Y)": "Coordinates (X, Y)"}, "objectives": {"Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective (English)": "Means of Achieving Objective (English)", "Means of Achieving Objective (Arabic)": "Means of Achieving Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "Objectives": "Objectives", "Action": "Action", "Preferred email": "Preferred email"}, "foundingMembers": {"Founding Meeting Data": "Founding Meeting Data", "Founding Meeting Place": "Founding Meeting Place", "Founding Meeting Emirate": "Founding Meeting Emirate", "Founding Meeting Date and Time": "Founding Meeting Date and Time", "Founding Meeting Agenda": "Founding Meeting Agenda", "EID Number": "EID Number", "Founding Members Number": "Founding Members Number", "Full Name in English": "Full Name in English", "Full Name in Arabic": "Full Name in Arabic", "Preferred Email": "Preferred Email", "Preferred Phone Number": "Preferred Phone Number", "Preferred Mobile Number": "Preferred Mobile Number", "Passport Number": "Passport Number", "Upload Personal Photo": "Upload Personal Photo", "Upload Passport Copy": "Upload Passport Copy", "Approval Status": "Approval Status", "Nationality": "Nationality", "Name": "Name", "EID": "EID", "Action": "Action"}, "interimCommittee": {"Interim Committee Meeting Data": "Interim Committee Meeting Data", "Interim Committee Members Meeting Place": "Interim Committee Members Meeting Place", "Interim Committee Meeting Emirate": "Interim Committee Meeting Emirate", "Interim Committee Meeting Date and Time": "Interim Committee Meeting Date and Time", "Recommendations of the meeting of the Interim Committee": "Recommendations of the meeting of the Interim Committee"}, "membership": {"Membership Data": "Membership Data", "More Membership Conditions": "More Membership Conditions", "Membership Fees": "Membership Fees", "Membership Fees Due Date Type": "Membership Fees Due Date Type", "Joining Fees (if any)": "Joining Fees (if any)"}, "boardofdirector": {"Board of Directors": "Board of Directors", "Frequency of Board Meetings": "Frequency of Board Meetings", "Number of Board Members": "Number of Board Members", "Can Board Members Be Renominated for Another Term?": "Can Board Members Be Renominated for Another Term?", "Administrative Positions of the Board": "Administrative Positions of the Board", "Number of Permissible Terms": "Number of Permissible Terms", "Board Numbers (between 5 to 11)": "Board Numbers (between 5 to 11)", "Justification if Board number exceeds 11": "Justification if Board number exceeds 11", "Board Election Cycle (Year)": "Board Election Cycle (Year)", "Allow Member Re-Election?": "Allow Member Re-Election?", "Allowed Periods": "Allowed Periods", "Permanent Advance (AED)": "Permanent Advance (AED)", "More Nomination Conditions": "More Nomination Conditions", "Administrative Positions": "Administrative Positions", "More Administrative Positions": "More Administrative Positions", "More Administrative Positions of the Board of Directors": "More Administrative Positions of the Board of directors"}, "Conditions that must be met by the founding members": "Conditions that must be met by the founding members", "The number of founders must not be less than 7 member": "The number of farmers must not be less than 7 member", "The percentage of members residing in the country should not exceed 30% of the total number of founders": "The percentage of members residing in the country should not exceed 30% of the total number of farmers", "The resident member's period of residence in the country must not be less than three years": "The resident member's period of residence in the country must not be less than three years", "That the resident member does not hold diplomatic status": "That the resident member does not hold diplomatic status", "Founding meeting agenda": "Founding meeting agenda", "Discussing the establishment of a public benefit institution": "Discussing the establishment of a public benefit institution", "Preparing the draft statute": "Preparing the draft statute", "Election of members of the temporary committee": "Election of members of the temporary committee", "Interim Committee meeting agenda": "Interim Committee meeting agenda", "Define the administrative positions for the members of the interim committee": "Define the administrative positions for the members of the interim committee", "Appoint the commissioner of the interim committee": "Appoint the commissioner of the interim committee", "Membership Conditions": "Membership Conditions", "The number of founding members shall not be less than (7) seven members": "The number of founding members shall not be less than (7) seven members", "The percentage of the founding members holding the nationality of the state shall not be less than (70%) of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls": "The percentage of the founding members holding the nationality of the state shall not be less than (70%) of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls", "Their number should not exceed 30% of the total number of founding members": "Their number should not exceed 30% of the total number of founding members", "The member does not hold diplomatic status": "The member does not hold diplomatic status", "He must have a valid residence permit in the country for a period of no less than (3) three years": "He must have a valid residence permit in the country for a period of no less than (3) three years", "The founding member must be of the age of majority in accordance with the legislation in force in the country": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated", "Conditions of the members of the Board of Directors": "Conditions of the members of the Board of Directors", "The number of members shall not be less than (5) members and not more than (11) members": "The number of members shall not be less than (5) members and not more than (11) members", "The number of members holding state nationality should not be less than (70%) of the total number of council members": "The number of members holding state nationality should not be less than (70%) of the total number of council members", "Nomination Conditions for the Board of Directors": "Nomination Conditions for the Board of Directors", "The member's age must not be less than (21) Gregorian years when elected": "The member's age must not be less than (21) Gregorian years when elected", "Administrative positions of the board members": "Administrative positions of the board members", "Chairman of the Board of Directors": "Chairman of the Board of Directors", "Vice President": "Vice President", "General Secretary": "General Secretary", "Treasurer": "Treasurer"}, "massWedding": {"title": "Mass Weddings", "desc": "Organize collective weddings through which a number of young men are married to a similar number of young women with the aim of reducing marriage costs", "gotoMyApplications": "Go to My Applications", "NotEligible": "Not eligible", "Husband Personal Information": "Husband Personal Information", "Wife Personal Information": "Wife Personal Information", "Abstract Enrollment Information": "Family Book Information", "Statement Of Work Information": "Statement Of Work Information", "Income Statement Information": "Income Statement Information", "tabs": {"title": {"husbandPersonalInfoTab": "Husband Personal Information", "wifePersonalInfoTab": "Wife Personal Information", "abstractEnrollmentInfoTab": "Family Book Information", "statementOfWorkTab": "Statement Of Work", "incomeStatementTab": "Income and Bank Statement", "attachmentTab": "Attachments", "summaryTab": "Summary"}}, "forms": {"husbandPersonalInfo": {"dateOfMarriageContract": "Date of Marraige Contract", "shariaCourtName": "Sharia Court Name", "NameAr": "Name (Arabic)", "NameEn": "Name (English)", "dob": "Date of Birth", "emiratesId": "Emirates Id", "educationLevel": "Education Level", "email": "Email", "mobile1": "Mobile 1", "mobile2": "Mobile 2"}, "wifePersonalInfo": {"NameAr": "Name (Arabic)", "NameEn": "Name (English)", "dob": "Date of Birth", "emiratesId": "Emirates Id", "educationLevel": "Education Level", "mobile": "Mobile", "email": "Email"}, "abstractEnrollmentInfo": {"familyBookNumber": "Family Book Number", "townNumber": "Town Number", "familyNumber": "Family Number", "dateOfIssuanceFamilyBook": "Family Book Issue Date", "placeOfIssuanceFamilyBook": "Family Book Issue Place"}, "statementOfWork": {"employerCategory": "Employer Category", "employer": "Employer Name", "placeOfWork": "Place of Work"}, "incomeStatement": {"totalMonthlyIncome": "Total Monthly Income", "bankName": "Bank Name", "iban": "International Banking Number (IBAN)"}, "attachment": {"commercialLicense": "Commercial License Statement", "monthlyIncomeCertificate": "Proof of Monthly Income (Salary Certificate)", "proofOfResidence": "Proof of Residence", "ibanLetter": "IBAN Letter", "bankStatement": "Bank Statement"}, "summaryForm": {"consentText": "Please agree to the terms and conditions", "termConditions": "You can read the terms and conditions here"}}}, "swp": {"title": "Social Welfare Program", "desc": "The Social Welfare Program offers financial support to low-income UAE citizens.", "Service Form": "Service Form", "NotEligible": "Not eligible", "applyForHousingAllowance": "Applying for a Housing Allowance", "applyForAcademicExcellenceAllowance": "Applying for an Education Excellence Allowance for Higher Education", "applyForHousingEducationTopup": "Applying for both Housing Allowances and Education Excellence Allowances for Higher Education", "forms": {"accomadations": "Beneficiary's Accommodation Type", "actions": "Actions", "Action": "Actions", "additionalAttachments": "Additional Attachments", "additionalDocuments": "Additional Documents", "additionalDocumentsSubtext": "If you wish to upload additional supporting documents with your application, you may do so below.", "addMoreIncome": "Add more Income", "addPension": "Add Pension", "addRentalIncome": "Add Rental Income", "address": "Address (Area, Street Number, House Number)", "addTradeLicense": "Add Trade License", "AlternativeEmail": "Prefered Email", "alternativeNumber": "Preferred Number", "area": "Area", "attachedDocuments": "Attached Documents", "attachedDocumentsSubtext": "Upload the necessary documents below.", "beneficiaryEducationTooltip": "Please specify the highest level of education certificate you have achieved.", "caseID": "Case Number", "caseReason": "Case Reason", "center": "Center", "comment": "Comment", "companyName": "Company Name", "complaintDetails": "{{inquiryType}} Details", "complaintType": "Complaint Type", "complete": "Complete", "ContractEndDate": "Contract End Date", "ContractNo": "Contract Number", "ContractStartDate": "Contract Start Date", "countryOfBirth": "Country of Birth", "dateOfBirth": "Date Of Birth", "deleteCantBeUndone": "This action can't be undone", "deleteFamilyMember": "Delete Family Member", "deleteFamilyMemberText": "Are you sure you want to delete the Family Member?", "deleteIncome": "Delete Income", "deleteIncomeText": "Are you sure you want to delete the additonal Income?", "deletePension": "Delete Pension", "deletePensionText": "Are you sure you want to delete the additonal Pension?", "deleteRentalIncome": "Delete Rental Income", "ThisFieldShouldbeNumber": "This Field Should be Number", "deleteRentalIncomeText": "Are you sure you want to delete the Rental Income?", "deleteTradeLicense": "Delete Trade License", "deleteTradeLicenseText": "Are you sure you want to delete the additonal Trade License?", "deletingFile": "Deleting File", "docd-ad": "Department of Community development - Abu Dhabi", "docd-dubait": "Community Development Authority – Dubai ", "documents": "Documents", "DescriptionLimitationMsg": "Description must be at most 700 characters", "doesFamilyMemberContribute": "Does this family member contribute to the family's income?", "doesFamilyMemberContributeTooltip": "Employment income, including irregular payments such as commissions, bonuses, etc. Please add additional income sources including income from self-employment/ freelancing/ contract agreements, including irregular payments such as income from freelance projects, etc.", "doesFamilyMemberPension": "Does this family member receive a retirement or pension income?", "doesFamilyMemberPensionTooltip": "Income from (public) pension benefit schemes for all household members including the head and contributors (e.g. wives)", "doesFamilyMemberRental": "Does this family member have a Rental Income?", "doesFamilyTradeLicense": "Does this family member have a trade license?", "dropFile": "or drop file here", "editFamilyMembersInformation": "Edit Family Members Information", "education": "Education", "Educations": "Highest Education", "Emirate": "Emirate", "Emirates": "Emirate", "EmiratesID": "Emirates ID", "emiratesID": "Emirates ID", "endDateCantStartBeforeStartDate": "The end date should be after the start date.", "enterRequestDetails": "Enter Case Details", "EWEBill": "Bill number from EWE", "eweErrorMessage": "Please enter valid 12 digit number", "familyMemberInformation": "Family Member Information", "familyMembersInformation": "Family Members Information", "housingInformation": "Housing allowance", "systemValidation": "System Validation", "documentGeneration": "Document Generation", "educationInformation": "Education allowance", "familyMembersInformationSubtext": "Please enter all required information below. All fields marked with an asterisk (*) are mandatory.", "filesType": "File types {{extensions}}", "fileUploadErrorFileSize": "Please upload a file with size less than {{size}}", "fileUploadErrorFileTypes": "Please upload a file with the following extensions: {{extensions}}", "FirstName": "First Name", "Fullname": "Full Name", "FullName": "Full Name", "firstName": "First Name", "last": "Full Name", "gender": "Gender", "healthCardInsurance": "Valid Health Card Insurance", "householdHeadContributes": "Does the household head contribute to the family's income?", "householdHeadContributesTooltip": "Employment income, including irregular payments such as commissions, bonuses, etc. Please add additional income sources including income from self-employment / freelancing / contract agreements, including irregular payments such as income from freelance projects, etc.", "householdHeadPension": "Does the household head receive a retirement or pension income?", "householdHeadPensionTooltip": "Income from (public) pension benefit schemes for all household members including the head and contributors (e.g. wives)", "householdHeadTradeLicense": "Does the household head have a trade license?", "householdRentalIncomes": "Does the household head have a Rental Income?", "IDNBackNumber": "EID Card Number (located on the back of the card)", "income": "Income", "incomeAmount": "Income Amount Monthly (in AED)", "Income": "Income Amount Monthly (in AED)", "incomeAmountToolTip": "Please specify the income amount for all household members including the head and contributors (e.g. wives)", "incomeInformation": "Income Information", "incomeInformationSubtext": "Please enter all required information below. All fields marked with an asterisk (*) are mandatory.", "incomeSource": "Income Source", "IncomeSource": "Income Source", "IncomeSourceText": "Income Source", "incomeSourceToolTip": "Please specify the income source type for all household members including the head and contributors (e.g. wives)", "IncomeTypes": "Income Source", "InvalidNumberOfChildren": "The value must be greater than zero, greater than, or equal to the number of criteria selected", "PleaseEnterNumbergraterThanZero": "Please Enter Number greater Than Zero", "PleaseEntera1or2-digit": "Please insert one or two-digit number", "informationRequired": "Information Required", "PleaseEnteraPositivenumber": "This field should contain a whole number greater than or equal to 1", "PleaseEnteranIntegerNumber": "Please enter an integer number", "isIntegerAndhasOneOrTwoDigits": "This field should contain whole numbers only", "NumberofPoDSiblings": "Number of PoD Siblings must be less than or equal to Number of Siblings", "NumberofPoDChildren": "Number of PoD Children must be less than or equal to Number of Emirati children", "PleaseEnterNumberBiggerThanZero": "Please enter number bigger than zero", "isRequired": "{{fieldName}} is a required field.", "lessThanOrEqual4": "This number should be less than or equal 4", "numberOfPODSpouses": "Number Of POD Spouses", "numberOfChildren": "Number Of Children", "numberOfSpouses": "Number Of Spouses", "totalIncome": "Total Income", "jobTitle": "Job Title", "LastName": "Last Name", "lastName": "Last Name", "localNumberFormatSubtext": "UAE mobile number", "MaritalStatus": "Marital Status", "maxSize": "Max {{size}}", "memberName": "Member Name", "mocd": "Ministry of Community Empowerment", "mustBeNumber": "This field must be a number.", "mustBePositive": "This field must have a positive value.", "next": "Next", "noDocumentsUpload": "No documents needed, please proceed to the next step.", "noEWENoFarmerDesc": "This service is for ewe registered only", "noEWENoFarmerTitle": "You cant apply for this service", "noFamilyMembersData": "No Family Members data available, please proceed to the next step.", "noEducationMembersData": "No Childrens above 16 years old available, please proceed to the next step.", "numberOfHousehold": "Number of Household", "Occupations": "Employment Status", "ownerEWEBill": "Do you own this account from EWE?", "passportCopy": "Passport Copy", "passportNo": "Passport No.", "PassportNumber": "Passport Number", "pension": "Pension", "pensionAmount": "Pension Amount Monthly (in AED)", "PensiontAuthority": "Pension Authority", "PensiontAuthorityText": "Pension Authority", "PensionType": "Pension Type", "PensionAuthority": "Pension Authority", "personalDocuments": "Personal Documents", "personalInformation": "Personal Information", "personalInformationSubtext": "Please enter all required information below. All fields marked with an asterisk (*) are mandatory. Before proceeding with your application, please make sure you have all the required documents, and their validity meets the requirements.", "phoneNumber": "Phone Number", "PreferredEmail": "Email Address", "PreferredPhoneNumber": "Mobile Number", "RelatedEmiratesID": "Emirates ID registered with EWE", "relationship": "Relationship", "RentalIncome": "Rental Income", "RentalIncomes": "Rental Incomes", "rentalSource": "Rental Source", "RentalSource": "Rental Source", "RentalSourceText": "Rental Source", "RentAmount": "Rent Amount Monthly (in AED)", "requestAddedBody1": "Thank you for submitting an application for the Social Assistance Service.", "requestAddedBody1Farmer": "Thank you for applying to the Farmer Welfare program", "requestAddedBody2": "Your application for the service has been submitted and is under review.The Ministry of Community Empowerment will contact you soon.", "requestAddedTitle": "Your application has been successfully submitted and is under review.", "requestEditedBody1": "Thank you for applying to the Social Welfare program.", "requestEditedBody1Farmer": "Thank you for applying to the Farmer Welfare program.", "requestEditedBody2": "Your application has been submitted successfully and will be processed for review by the relevant department. The ministry will contact you soon.", "requestEditedTitle": "Your application has been submitted successfully and will be processed for review by the relevant department.", "requestNumber": "Request Number", "requestSubmitted": "Case Submitted", "reviewDetails": "Review Details", "reviewDetailsSubtext": "Please review the information and proceed as required.", "selectFiles": "Select File", "socialAidInformation": "Social Aid Information", "inflationInformation": "Inflation Information", "socialAidInformationSubtext": "Please enter all required information below. All fields marked with an asterisk (*) are mandatory.", "socialServices-sharjah": "Social Services Department - Sharjah", "status": "Status", "StatusCode": "Status", "submitRequest": "Submit Case", "submittedOn": "Submitted On", "thankYouForFeedback": "Thank you for your feedback.", "thisField": "This field", "tradeLicense": "Trade License", "tradeLicenseAmount": "Trade License Amount Monthly (in AED)", "TradeSourceText": "Trade Source", "uaeMobileNumberError": "Please provide a UAE number with the following format 05XXXXXXXX", "uaeIDNumberError": "Please enter a valid Emirates ID", "uaeResidenceVisa": "UAE Residence Visa", "universityDegree": "Certified Degree From University", "uploadingFile": "Uploading File", "useLengthError": "Please enter a number of 12 digits", "validWorkContractFamily": "Valid Work Contract", "wrongEmailAddress": "Please enter a valid email address.", "EnterEmirateIdForEWE": "Please enter the Emirates ID number registered with the farm account number from the Union Water and Electricity Company", "EntityReceivedFrom": "From what side?", "HowToKnowEWE": "How to find out the account number of the Union Water and Electricity Company.", "PleaseEnterEWENumber": "Please enter EWE account number for the farm", "ReceiveInflationAllowance": "Do you receive an inflation bonus?", "ReceiveSocialAid": "Do you receive social assistance?", "RegisteredWithEWE": "Are you registered with EWE?", "attachedDocumentsFarmer": "Attached documents", "complaintSuccessBody": "Thank you for submitting the {{inquiryType}}. You can check the status of this {{inquiryType}} at any time through the Inquiries / Suggestions page. We will contact you later if we need any additional information.", "complaintSuccessBodyForAnonymous": "Thank you for submitting the {{inquiryType}}. We will contact you later if we need any information.", "complaintSuccessCaseNumberTitle": "{{inquiryType}} number", "complaintSuccessDateTitle": "Submitted", "complaintSuccessTitle": "The {{inquiryType}} has been submitted successfully and is under review", "complaintTitle": "{{inquiryType}} Title", "familyMembersInformationFarmer": "Family member information", "farmerAidInformation": "Information related to farm owners aid", "farmerAidRquestAddedBody2": "Your request is under review and we will be contacted shortly.", "farmerRequestAdded": "Your request has been submitted successfully", "incomeInformationFarmer": "income information", "reviewDetailsFarmer": "Review details", "socialAidInformationFarmer": "Social assistance information", "Area": "Area", "Category": "Reason", "SubCategory": "Details", "EmiratesResd": "Emirate Residency", "Center": "Center", "youHaveToReadTerms": "You have to read the terms", "IsActiveStudent": "Is active student ?", "MilitaryServiceStatus": "Military", "Terminated": "Terminated", "jobseekerErrorMsg": "This assistance is temporary for six months - and you can only apply for it twice in the next five years.", "notApplicableErrorMsg": "you can not select another value with not applicable", "feedbackRecived": "Your feedback has been submitted, thank you!", "HaveChildrenCustody": "Do you have custody of your children", "levelOfEducation": "Enrolled in (level of education) ?", "ReceivedLocalSupport": "Are you or your spouse receiving any other local social support ?", "guardianIncome": "Guardian income (AED)", "PursuingHigherEducation": "Have you been pursuing higher education since turning 21 years old?", "PursuingMilitaryService": "Have you been pursuing military service since turning 21 years old?", "GuardianEmiratesID": "Guardian Emirates ID", "PensiontAmount": "Monthly Pension Amount (AED)", "noDataFound": "No Data Found", "swfProgram": "Social Subsidy Program", "InflationProgram": "Inflation Program", "theService": "Service", "completeInfo": "Complete", "IsPursuingHigherEducation": "Is Pursuing Higher Education since turning 21 years old?", "IsDraftedinMilitaryService": "Is Draftedin Military Service since turning 21 years old?", "addMoreDocs": "Add additional documents", "selectDocType": "Select the type of documennt you want to add", "PreferredEmailUsed": "Email (the certificate will be sent to this email)", "aboutWebsite": "About Website", "siteMap": "Sitemap", "previous": "Previous", "allowanceCalculator": "Allowance Calculator", "pages": "Pages", "more": "More", "Beneficiaries": "Beneficiaries", "Topic": "Topic", "FamilyHousholdName": "Family Houshold Name", "khulasitQaidNumber": "Family book number", "ChildEligibilityforWomeninDifficulty": "Do you have custody of at least one child that meets the following criteria ?", "select": "Please select if applicable", "isRequiredField": "this is a required field", "NumberOfChildren": "How many children meet the above criteria?", "NumberOfChildrenLessThan25": "How many children less than 25?", "complaintServices": "Service", "complaintSubServices": "Sub Services", "FamilyBookInformation": "Family Book Information", "generatingFamilyBook": "Please wait... Family book is being generated", "thisFieldshouldbeLess": "must be greater than 0 and less than or equal to children meet the above criteria", "greaterThanZero": "must be greater than 0", "localSupText": "(From any of these entities: Abu Dhabi Social Support Authority, Community Development Authority, Sharjah Social Services Department)", "localSupText2": "(Sheikh <PERSON> Housing Program, Abu Dhabi Housing authority, Mohammed Bin Rashid Housing Establishment, Directorate of Housing of Sharjah)", "Verified": "Verified", "PendingVerification": "Pending Verification", "complaintHeader": "Complaint Title", "inquiryHeader": "Inquiry Title", "suggestionHeader": "Suggestion Title", "thankYouHeader": "Thank You Title", "thankYouTitle": "Thank You", "thankYouTitleHeader": "Thank You Title", "numberOfSiblingsError": "Please enter a 2-digit number for number Of Siblings", "ApplyHousingAllowance": "Would you like to apply for housing allowance?", "IsHouseholOwnerResidentialProperty": "Household members sole owner of a constructed residential property", "ReceivingFederalLocalhousingsupport": "Do you or any of your household members receive any Federal or Local housing support from any of the below programs?", "ReceivingHousingAllowanceFromEmployer": "Do you or any of your household members receive housing scheme from employer? (disclaimer: not including housing allowance part of the monthly salary)", "IsUtilityBillIssuedForFullyOwnedProperty": "Is their a utility bill issued for the fully owned residential property?", "FullOwnershipResidentialProperty": "Do you or any of your household members have full ownership of a residential property?", "LivingSituation": "What is your living situation?", "ApplyEducationAllowance": "Do you want to apply for educational excellence allowance for this child/sibling?", "childCompletedSemesterInUniversity": "Has this child completed more than 1 semester in an accredited university?", "highSchoolCurriculuim": "Please select high school curriculum", "enrolledEducationStream": "Which public education stream was the child/sibling enrolled in?", "EmSATorAdvancedPlacementScores": "Do you want to upload your EmSAT or Advanced Placement scores?", "Age": "Age", "ApplyEducation": "Applying for education allowance", "applyedtrue": "Yes", "applyedfalse": "No", "ReceivesHousingSupportFromHusband": "Do you receive housing support from your divorced husband as per court ruling?", "ApplyInflationAllowance": "Would you like to apply for inflation allowance?", "ApplyUtilityAllowance": "Would you like to apply for utility allowance as well?", "UtilityProvider": "Please select your utility provider", "UtilityAccountNumber": "Please insert the utility account number you wish to receive utility allowance for", "IsEnrolledInNationalService": "Is the child currently enrolled in national service?", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "inquiry": "Inquiry", "suggestion": "Suggestion", "informationForm": "Reason for Applying", "InflationCategory": "Reason for applying", "discliamer": "Child can apply for Education Excellence upon completing national service", "disabledFieldMessage": "You may update the preferred phone or preferred email under My Profile", "womanOver45": "Please upload the EID and custody document if available", "personalDocumentTitle": "Personal Documents", "additionalDocumentTitle": "Additional Documents", "SelectFileType": "Additional File Type", "AddAditionalDocument": "Add Additional Document", "notEligibleForInflation": "You are not eligible for inflation subsidy", "InactiveError": "This utility account is not eligible to receive inflation allowance because it is inactive. Please insert another utility account number, or proceed to the next section without applying to utility allowance", "Non-ResidentialError": "This utility account is not eligible to receive inflation allowance because it is non-residential. Please insert another utility account number, or proceed to the next section without applying to utility allowance.", "Non-EmiratiError": "This utility account is not eligible to receive inflation allowance because the account holder is non-Emirati. Please insert another utility account number, or proceed to the next section without applying to utility allowance.", "ReceivingUtilityAidError": "This utility account is not eligible to receive inflation allowance because it is receiving another utility aid. Please insert another utility account number, or proceed to the next section without applying to utility allowance.", "inflationEdit": "Please update all inflation related information on the inflation allowance case that appears under 'My Cases' section"}}, "complaint": {"title": "<PERSON><PERSON><PERSON><PERSON>", "desc": "Submit a complaint regarding a recent service used.", "Service Form": "Service Form", "Topic": "Topic", "Complaint Title": "Complaint Title", "Complaint Service": "Complaint Service", "Complaint Sub Service": "Complaint Sub Service", "Complaint Details": "<PERSON><PERSON><PERSON><PERSON>"}, "inquiry": {"title": "Inquiry", "desc": "Request information pertaining to your service of interest.", "Service Form": "Service Form", "Topic": "Topic", "Inquiry Title": "Inquiry Title", "Inquiry Service": "Inquiry Service", "Inquiry Sub Service": "Inquiry Sub Service", "Inquiry Details": "Inquiry Details"}, "suggestion": {"title": "Suggestion", "desc": "Provide a suggestion on how we could improve your experience.", "Service Form": "Service Form", "Topic": "Topic", "Suggestion Title": "Suggestion Title", "Suggestion Service": "Suggestion Service", "Suggestion Sub Service": "Suggestion Sub Service", "Suggestion Details": "Suggestion Details"}, "thankyou": {"title": "Thank You", "desc": "Express appreciation towards an employee or service used.", "Service Form": "Service Form", "Topic": "Topic", "Thankyou Title": "Thank you Title", "Thankyou Service": " Thank you Service", "Thankyou Sub Service": "Thank you Sub Service", "Thankyou Details": "Thank you Details"}, "earlyIntervention": {"title": "Care and rehabilitation of people with disabilities 'people of determination'", "desc": "This service is to register children in early intervention centers and units to obtain the necessary training and rehabilitation to enable them to integrate into society", "Child Details": "Child Details", "Guardian Details": "Guardian Details", "Application ID": "Application ID", "Child Emirate ID": "Child Emirate ID", "Child Name in Arabic": "Child Name in Arabic", "Child Name in English": "Child Name in English", "Date of Birth": "Date of Birth", "Gender": "Gender", "Center Name": "Center Name", "Emirate": "Emirate", "Residency Emirate": "Residency Emirate", "Guardian Emirate ID": "Guardian Emirate ID", "Guardian Name in Arabic": "Guardian Name in Arabic", "Guardian Name in English": "Guardian Name in English", "Relationship": "Relationship", "Mobile": "Mobile", "Email": "Email", "Preferred Mobile": "Preferred Mobile", "Preferred Email": "Preferred Email", "Area": "Area", "Address": "Address", "Personal Photo": "Personal Photo", "Passport Copy": "Passport Copy", "Emirate ID": "Emirate ID", "Nationality": "Nationality", "Nationality Status": "Nationality Status", "Number of Siblings": "Number of Siblings", "Home Languages": "Home Languages", "Siblings Health Status": "Siblings Health Status", "Disability Type": "Disability Type", "Mobile 1": "Mobile 1", "Mobile 2": "Mobile 2", "Hearing Test Report": "Hearing Test Report", "Vision Test Report": "Vision Test Report", "Person Type": "Nationality Category", "Birth Certificate": "Birth Certificate", "consentText": "I agree to the Terms and Conditions", "Medical Report": "Medical Report", "Child Name (Arabic)": "Child Name (Arabic)", "Child Name (English)": "Child Name (English)", "Guardian Name (English)": "Guardian Name (English)", "Applicant is not eligible to apply": "The child is more than five years old. Registration has not yet started in the care and rehabilitation centers - People of Determination.You will be notified later.", "Guardian Information": "Guardian Information", "Guardian Name (Arabic)": "Guardian Name (Arabic)", "Select Guardian": "Select Guardian", "Guardian": "Guardian", "Guardian Date of Birth": "Guardian Date of Birth", "Guardian Dob": "Guardian Dob", "Terms and Conditions": "Terms and Conditions", "terms": {"1": "To be a developmentally delayed child, with a disability, at risk of developmental delay, age: from 0 to 5 years, for citizens and children of female citizens", "2": "All the child's identification documents must be valid (passport, ID, nationality, recent personal photo)", "3": "A copy of the medical report (if any)", "4": "A copy of the disability card (people of determination) issued by the ministry (if any)", "5": "If one of the previous conditions is not met, the case will not be registered and the guardian will be directed to review us after fulfilling all conditions and requirements"}}, "podCenter": {"title": "Registration in the center of care and rehabilitation of People of Determination", "desc": "This service is to register children in people of determination care and rehabilitation centers to obtain the necessary training and rehabilitation to enable them to integrate into society.", "Personal Details": "Personal Details", "Application ID": "Application ID", "Child Emirate ID": "Child Emirate ID", "PoD Card Number": "PoD Card Number", "Child Name in Arabic": "Child Name in Arabic", "Child Name in English": "Child Name in English", "Date of Birth": "Date of Birth", "Gender": "Gender", "Disability Type": "Disability Type", "Center Name": "Center Name", "Emirate": "Emirate", "Guardian Details": "Guardian Details", "Guardian Emirate ID": "Guardian Emirate EID", "Guardian Date of Birth": "Guardian Date of Birth", "Guardian Name in Arabic": "Guardian Name in Arabic", "Guardian Name in English": "Guardian Name in English", "Relationship": "Relationship", "Mobile": "Mobile", "Email": "Email", "Preferred Mobile": "Preferred Mobile", "Preferred Email": "Preferred Email", "Area": "Area", "Address": "Address", "Attachments": "Attachments", "Personal Photo": "Personal Photo", "Passport Copy": "Passport Copy", "Emirate ID": "Emirate ID", "Medical Report": "Medical Report", "Submit": "Submit", "Terms and Conditions": "Terms and Conditions", "consentText": "I agree to the Terms and Conditions", "Location Information": "Location Information", "Siblings Number": "Siblings Number"}, "npoLicenseDeclaration": {"title": "Non-Profit Organization Declaration Request", "desc": "Declaring NPO Establishment", "titleByDecree": "NPO By Decree Registration (established by decree or local law)", "descByDecree": "Declaring NPO By Decree Registration", "forms": {"legalTypePage": {"title": "Non-Profit Organization Declaration Request", "titleByDecree": "Non-Profit Organization Registration Request", "selectNpoLegalForm": "Select NPO Legal Form", "description": "As an NPO Representative, you can now to apply for an NPO Establishment request in order to obtain NPO Founding Members Confirmation so I will be able to submit the NPO Licensing & Declaration request to MoCD for approval."}, "npoLegalForm": {"title": "Legal Form"}, "basicInformation": {"title": "Basic Information", "npoLegalForm": "NPO Legal Form", "localDecreeLawNumber": "Local Decree/Law Number", "issuanceDate": "Issuance Date", "associationClassification": "Association Classification", "localDecreeLawCopy": "Local Decree/Law Copy", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "addProposedName": "Add Proposed Name", "edit": "Edit ", "clone": "<PERSON><PERSON> ", "remove": "Remove ", "proposedNamesList": "Proposed Names List", "landlineNumber": "Landline Number", "poBox": "PO Box", "faxNumber": "Fax Number", "email": "Email", "website": "Website", "address": "Address", "geographicLocation": "Geographic Location", "emirate": "Emirate", "logo": "Logo", "permanentAdvance": "Permanent Advance (Emergency Fund)", "fundsAllocationAmount": "Funds Allocation Amount", "add": "Add", "id": "Id", "actions": "Actions", "npoName": "NPO Name", "npoContactDetails": "NPO Contact Details", "fundsAllocation": "Funds Allocation", "status": "Status", "threeProposedNames": "3 Proposed Names", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "membershipConditions": "Membership Conditions", "help": "Help", "helpMessage": "In order to submit the request, two proposed names and a maximum of three names must be added", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "confirm": "Confirm Proposed Name", "ExamplesOfActivitiesAndProgramsOfferedByTheNPO": "Examples of activities and programs offered by the NPO", "exampleOfActivitiesAr": "Activity/Program Title (AR)", "exampleOfActivitiesEn": "Activity/Program Title (EN)", "activitiesAndPrograms": "Examples of activities and programs offered by the NPO"}, "objectives": {"ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "Id": "Id", "Title": "Objectives", "FundServiceTitle": "Fund Activities and Objectives", "Actions": "Actions", "npoObjectives": "NPO an Organization Objective", "editObjective": "Edit ", "cloneObjective": "<PERSON><PERSON> ", "removeObjective": "Remove ", "objectives": "Objectives", "fundServiceObjectives": "Fund Activities and Objectives", "fundServiceAddObjective": "Add Activity / Objective", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "helpMessage": "In order to submit the request, you must add at least five objectives", "helpMessageByDecree": "In order to submit the request, you must add at least one objective", "status": "Status"}, "foundingMembers": {"title": "Founding Members", "foundingMembersLessThan70Percent": "Founding Members holding the nationality of the State is less than (70%)", "exceptionReasonEn": "Exception Reason (EN)", "exceptionReasonAr": "Exception Reason (AR)", "foundingMembersLessThanSeven": "Number of Founding Members is less than (7) seven Members", "foundingMembersLessThanSevenHint": "You should add at least (3) founding members to proceed", "numberOfFounders": "Number of Founding", "founderEmiratesID": "Emirates ID", "founderDateOfBirth": "Founder Date of Birth", "addFoundingMember": "Add Founding Member", "FoundingMember": "Founding Member", "founderNameEnglish": "Founder Name (English)", "founderNameArabic": "Founder Name (Arabic)", "founderNationality": "Nationality", "founderEmail": "Founder <PERSON><PERSON>", "founderMobileNumber": "Founder Mobile Number", "founderEmirate": "Founder Emira<PERSON>", "agendaItems": "Agenda Items", "founderPassportNumber": "Founder Passport Number", "founderResidencyIssuanceDate": "Founder Residency Issuance Date", "founderResidencyExpiryDate": "Founder Residency Expiry Date", "founderAcademicQualification": "Founder Academic Qualification", "founderJobTitle": "Founder Job Title", "founderEmployer": "Founder Employer", "founderPassportPhoto": "Founder Passport Photo", "founderPersonalPhoto": "Founder Personal Photo", "foundingMembersList": "Founding Members List", "removeFoundingMember": "Remove Founding Member", "foundersMeetingPlace": "Place", "foundersMeetingEmirate": "Emirate", "foundersMeetingDate": "Date & Time", "founderDecisionDate": "Founder Decision Date", "foundersMeetingAgenda": "Agenda", "exceptionCases": "Exception Requests", "foundersMeeting": "Founders Meeting", "emiratesId": "Emirates ID", "editFoundingMember": "Edit ", "cloneFoundingMember": "<PERSON><PERSON> ", "dateOfBirth": "Founder Date of Birth", "actions": "Actions", "id": "Id", "founderEmiratesId": "Founder Emirates ID", "founderBOD": "BOD", "founderNameEn": "Name", "responseDate": "Response Date", "status": "Status", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Founding member age is less than 21 years old", "foundingMemberResidencyIsLessThan3Years": "Founding member residency is less than 3 years", "foundingMemberResidency": "This Founding member residency is less than 3 years, Please enter Exception Reason", "foundingMemberHasDiplomaticStatus": "Founding member has a diplomatic status", "UAELocal": "Get Information", "percentageText": "The  percentage of local members is", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "addFoundingMembersHelpMessage": "Once the founding member responds to the confirmation request, you will be able to view his name and nationality, as well as his response and the response date.", "foundersMeetingAgendaHelpMessage": "Founder meeting agenda:", "foundersMeetingAgendaHelpMessage1": "Discussing the establishment of a public benefit institution.", "foundersMeetingAgendaHelpMessage2": "Preparing the draft statute.", "foundersMeetingAgendaHelpMessage3": "Election of members of the temporary committee.", "foundingMemberAgeLessThan21": "This Founding Member Age Is Less Than 21 Years Old, Please enter Exception Reason", "socialSolidarityFundsHelpMessage": "Founding members of the fund upon establishment shall not be less than (25) members", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "editNPO": "Edit NPO Unified Number", "cloneNPO": "Clone NPO Unified Number", "removeNPO": "Remove NPO Unified Number", "npoUnifiedNumber": "NPO Unified Number", "npoUnifiedNumberOrNPOName": "NPO Unified Number or NPO name", "addNPOUnifiedNumber": "Add NPO Unified Number", "associationsOrNationalSocietiesFromTheUnionList": "Associations or National Societies from the Union List", "npoName": "NPO Name", "npoContactDetails": "NPO Contact Details", "associationsNationalSocieties": "Associations or National Societies from the Union List", "addFoundingMemberUnifiedNumber": "Add Founding Member Unified Number", "foundingMemberUnifiedNumber": "Founding Member Unified Number", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "unionFoundersMeetingHelpMessage": "To be able to submit a request for licensing and declaring a union, you must add a minimum number of (5) associations or national societies only. It is not allowed to combine associations and national societies when forming a union."}, "interimCommittee": {"Id": "Id", "temporaryCommitteeMembersMeetingPlace": "Meeting Place", "temporaryCommitteeMembersMeetingEmirate": "Meeting Emirate", "temporaryCommitteeMembersMeetingDateTime": "Meeting Date & Time", "temporaryCommitteeMembersMeetingAgenda": "Meeting Agenda", "temporaryCommitteeMember": "Temporary Committee Member", "temporaryCommitteeMemberPosition": "Temporary Committee Member Position", "addCommitteeMember": "Add Committee Member", "committeeMemberList": "Committee Member List", "removeTemporaryCommitteeMember": "Remove Temporary Committee Member", "Title": "Interim Committee", "Actions": "Actions", "editMember": "Edit ", "cloneMember": "<PERSON><PERSON> ", "agendaItems": "Agenda Items", "removeMember": "Remove ", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "temporaryCommitteeMemberEID": "Temporary Committee Member EID", "temporaryCommitteeMemberName": "Temporary Committee Member Name", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "selectNpoLegalForm": "Select NPO Legal Form", "interimCommitteeMembersHelpMessage": "The number of members of the interim committee shall not be less than three members.", "interimCommitteeInformationHelpMessage": "Interim Committee meeting agenda", "interimCommitteeInformationHelpMessage1": "Define the administrative positions for the members of the interim committee.", "interimCommitteeInformationHelpMessage2": "Appoint the commissioner of the interim committee.", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "status": "Status"}, "membership": {"title": "Membership", "id": "Id", "membershipFees": "Basic Membership Fees", "normalMembershipFees": "Membership Fees", "annualMembershipDueDate": "Annual Membership Due Date", "enrollmentFees": "Enrollment Fees", "membershipConditionEn": "Membership Condition (EN)", "membershipConditionAr": "Membership Condition (AR)", "category": "Category", "addCondition": "Add Membership Conditions", "addConditionButton": "Add Condition", "membershipConditionsList": "Membership Conditions List", "Actions": "Actions", "cancel": "cancel", "editCondition": "Edit ", "cloneCondition": "<PERSON><PERSON> ", "removeCondition": "Remove ", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "status": "Status", "beneficiaryMembershipFees": "Beneficiary or Membership Fees"}, "allocationOfFoundationFunds": {"title": "Allocation Of Foundation Funds", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "totalFundsAmountIs": "Total funds amount is", "totalFundsAmountError": "Minimum Funds Amount Should be 5000000 AED", "status": "Status"}, "boardOfDirectors": {"Id": "Id", "Title": "Board Of Directors", "Actions": "Actions", "boardMembersExceed11": "The number of Board Members exceeds 11 members", "exceptionReasonEn": "Exception Reason (EN)", "exceptionReasonAr": "Exception Reason (AR)", "numberOfBoardMembers": "Number of Board Members", "frequencyOfBoardMeetings": "Frequency of Board Meetings", "BoardElectionCycle": "Board Election Frequency", "boardMemberReNomination": "Can Board Members Be Renominated for Another Term?", "numberOfPermissibleTerms": "Number of Permissible Terms", "localBoardMembersPercentage": "Percentage of UAE Local Board Members", "electionMethod": "Election Method", "nominationConditionEnglish": "Condition for Nomination (English)", "nominationConditionArabic": "Condition for Nomination (Arabic)", "addNominationCondition": "Add Condition", "nominationConditionsList": "Conditions for Nomination for membership in the Board of Directors List", "removeNominationCondition": "Remove Condition", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "addPosition": "Add Position", "boardAdminPositionsList": "Board Administrative Positions List", "editCondition": "Edit ", "cloneCondition": "<PERSON><PERSON> ", "removeCondition": "Remove ", "editPosition": "Edit ", "clonePosition": "<PERSON><PERSON> ", "removePosition": "Remove ", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "individualElection": "Individual Election", "individualElectionCondition": "The NPO General Secretary adds nominees one by one to the BOD Nominations list. The system validates that there are at least 5 nominees, and the percentage of UAE locals matches the NPO By-law.", "slateElection": "Slate Election", "slateElectionCondition": "The NPO General Secretary creates slates, giving each an English and Arabic title, and adds nominees under each slate. The system validates that each slate has at least 5 nominees and that the percentage of UAE locals matches the NPO By-law.", "specialElectionGCC": "Special Election / GCC (9 Seats)", "specialElectionGCCCondition": "The system shows 6 lists representing GCC countries and 1 list of the other additional 3seats. The NPO General Secretary adds nominees to the list. The system validates the nationality of nominees and ensures a minimum number of nominees per list.", "twoStageElection1": "Two-Stage Election (Board Chairman and Board Member).", "twoStageElection1Condition": "The system shows 2 lists: Board Chairman and Board Member. The NPO General Secretary adds nominees to each list. The system ensures at least 5 nominees in total, and that the percentage of UAE locals matches the NPO By-law. Nominees cannot be in both lists simultaneously.", "twoStageElection2": "Two-Stage Election (Board Chairman or Board Member)", "twoStageElection2Condition": "Similar to the previous method, nominees can only be on one list. After voting for the Board Chairman, unsuccessful nominees cannot be added to the Board Member list.", "electionsWithFemaleQuota": "Elections with <PERSON> Quota", "electionsWithFemaleQuotaCondition": "The NPO General Secretary adds nominees one by one. The system ensures at least 5 nominees, and that the percentage of UAE locals matches the NPO By-law. The system prioritizes female nominees with the highest votes, up to 3, and completes the list with male nominees.", "help": "Help", "status": "Status"}, "FundServices": {"title": "Fund Services", "Header": "Services provided by the Fund", "help": "Help", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Actions": "Actions", "helpMessage": "Services provided by the Fund", "Amount": "Amount", "editFundServices": "Edit Fund Service", "status": "Status"}, "boardOfTrustees": {"title": "Board Of Trustees", "frequencyOfMeetings": "Frequency of Board of Trustees meetings", "numberOfMembers": "Number of Board of Trustees members", "frequencyOfAppointments": "Frequency of Board of Trustees appointment", "conditionForNominationEn": "Condition for Nomination (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "addCondition": "Add Condition", "administrativePositionTitleEn": "Administrative Position Title (English)", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "addPosition": "Add Position", "addMember": "Add Member", "memberPosition": "Board of Trustees Member Position", "foundingMember": "Founding Member", "id": "Id", "actions": "Actions", "editCondition": "Edit ", "cloneCondition": "<PERSON><PERSON> ", "removeCondition": "Remove ", "editPosition": "Edit ", "clonePosition": "<PERSON><PERSON> ", "removePosition": "Remove ", "editMember": "Edit ", "cloneMember": "<PERSON><PERSON> ", "removeMember": "Remove ", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesMembers": "Board of Trustees Members", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesConditions": "Board of Trustees Conditions", "conditions": "Conditions", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "Position": "Position", "foundingMembersLessThan70Percent": " Members holding the nationality of the State is less than (70%)", "exceptionReasonEn": "Exception Reason (EN)", "exceptionReasonAr": "Exception Reason (AR)", "foundingMembersLessThanSeven": "Number of  Members is less than (7) seven Members", "numberOfFounders": "Number of ", "founderEmiratesID": "Emirates ID", "founderDateOfBirth": " Date of Birth", "addFoundingMember": "Add  Member", "founderNameEnglish": " Name (English)", "founderNameArabic": " Name (Arabic)", "founderNationality": "Nationality", "founderEmail": " Email Address", "founderMobileNumber": " Mobile Number", "founderEmirate": " Emirate", "agendaItems": "Agenda Items", "founderPassportNumber": " Passport Number", "founderResidencyIssuanceDate": " Residency Issuance Date", "founderResidencyExpiryDate": " Residency Expiry Date", "founderAcademicQualification": " Academic Qualification", "founderJobTitle": " Job Title", "founderEmployer": " Employer", "founderPassportPhoto": " Passport Photo", "founderPersonalPhoto": " Personal Photo", "foundingMembersList": " Members List", "removeFoundingMember": "Remove  Member", "foundersMeetingPlace": "Place", "foundersMeetingEmirate": "Emirate", "foundersMeetingDate": "Date & Time", "founderDecisionDate": " Decision Date", "foundersMeetingAgenda": "Agenda", "exceptionCases": "Exception Requests", "foundersMeeting": " Meeting", "emiratesId": "Emirates ID", "editFoundingMember": "Edit ", "cloneFoundingMember": "<PERSON><PERSON> ", "dateOfBirth": " Date of Birth", "founderEmiratesId": " Emirates ID", "founderBOD": "BOD", "founderNameEn": "Name", "responseDate": "Response Date", "status": "Status", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": " member age is less than 21 years old", "foundingMemberResidencyIsLessThan3Years": " member residency is less than 3 years", "foundingMemberResidency": "This  member residency is less than 3 years, Please enter Exception Reason", "foundingMemberHasDiplomaticStatus": " member has a diplomatic status", "UAELocal": "Get Information", "percentageText": "The  percentage of local members is", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "addFoundingMembersHelpMessage": "Once the  member responds to the confirmation request, you will be able to view his name and nationality, as well as his response and the response date.", "foundersMeetingAgendaHelpMessage": "Founder meeting agenda:", "foundersMeetingAgendaHelpMessage1": "Discussing the establishment of a public benefit institution.", "foundersMeetingAgendaHelpMessage2": "Preparing the draft statute.", "foundersMeetingAgendaHelpMessage3": "Election of members of the temporary committee.", "foundingMemberAgeLessThan21": "This  Member Age Is Less Than 21 Years Old, Please enter Exception Reason", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "npoMember": "Board of Trustees Members", "helpMessageMember1": "Each 'Board of Trustees' position, should have only one member assigned to it.", "helpMessageMember2": "The 'Board Member' position only could have multiple members assigned to it. "}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the NPO logo at any time after submitting the information, however it is mandatory before the ministry's approval."}, "reviewAndSubmit": {"ExamplesOfActivitiesAndProgramsOfferedByTheNPO": "Examples of activities and programs offered by the NPO", "exampleOfActivitiesAr": "Activity/Program Title (AR)", "exampleOfActivitiesEn": "Activity/Program Title (EN)", "activitiesAndPrograms": "Examples of activities and programs offered by the NPO", "npoLegalForm": "NPO Legal Form", "localDecreeLawNumber": "Local Decree Law Number", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "associationClassification": "Association Classification", "npoName": "NPO Name", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "npoContactDetails": "NPO Contact Details", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "GeographicLocation": "Geographic Location", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Basic Membership Fees", "normalMembershipFees": "Membership Fees", "beneficiaryMembershipFees": "Beneficiary or Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category"}, "requestDetails": {"ExamplesOfActivitiesAndProgramsOfferedByTheNPO": "Examples of activities and programs offered by the NPO", "exampleOfActivitiesAr": "Activity/Program Title (AR)", "exampleOfActivitiesEn": "Activity/Program Title (EN)", "activitiesAndPrograms": "Examples of activities and programs offered by the NPO", "npoLegalForm": "NPO Legal Form", "localDecreeLawNumber": "Local Decree Law Number", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "associationClassification": "Association Classification", "npoName": "NPO Name", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "npoContactDetails": "NPO Contact Details", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "GeographicLocation": "Geographic Location", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectivesEn": "Objectives (English)", "ObjectivesAR": "Objectives (Arabic)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Meeting Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Basic Membership Fees", "normalMembershipFees": "Membership Fees", "beneficiaryMembershipFees": "Beneficiary or Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective (English)": "Means of Achieving Objective (English)", "Means of Achieving Objective (Arabic)": "Means of Achieving Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founding Meeting", "NPO_LICENSE_DECLARATION": "Non-Profit Organization Declaration Request", "NPO_ByDecree": "NPO By Decree Registration", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section for ", "COMMENT": "Comment", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "fundServiceObjectives": "Fund Activities and Objectives", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "category": "Category", "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "nonMuslimsWorshipPlaceLicense": {"title": "Issue License For Non-Muslims Worship Place", "desc": "Declaring Non-Muslims Worship Place / Room", "roomTitle": "Request for Allocation of Worship Room", "roomDesc": "Request for Allocation of Worship Room", "forms": {"legalTypePage": {"title": "Non-Muslims Worship Place / Room", "selectNpoLegalForm": "Select Service Type", "description": ""}, "basicInformation": {"title": "Basic Information", "worshipPlaceName": "Worship Place Name", "worshipPlaceInformation": "Worship Place Information", "parentWorshipPlaceInfo": "Parent Worship Place Information", "religion": "Religion", "proposedNamesList": "Proposed Names List", "sect": "Sect", "belief": "Belief", "brief": "Brief about the Religion, Sect, or Belief", "religiousPractices": "Religious practices, worship, rituals and ceremonies", "programTimings": "Program and timings for performing worship, rituals and ceremonies", "controlsAndProcedures": "Controls and procedures for performing worship, rituals and ceremonies", "worshipPlaceType": "Type of Worship Place", "typeName": "Worship Place Type Name", "parentHouseOfWorshipPlaceNameEn": "Parent House of Worship Place Name (English)", "parentHouseOfWorshipPlaceNameAr": "Parent House of Worship Place Name (Arabic)", "country": "Country", "proposedNameEn": "Proposed Name (English)", "proposedNameAr": "Proposed Name (Arabic)", "addProposedName": "Add Proposed Name", "edit": "Edit ", "clone": "<PERSON><PERSON> ", "remove": "Remove ", "landlineNumber": "Landline Number", "poBox": "PO Box", "email": "Email", "website": "Website", "address": "Address", "geographicLocation": "Geographic Location", "emirate": "Emirate", "logo": "Logo", "add": "Add", "id": "Id", "actions": "Actions", "contactDetails": "Contact Details", "status": "Status", "threeProposedNames": "3 Proposed Names", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "membershipConditions": "Membership Conditions", "help": "Help", "helpMessage": "In order to submit the request, two proposed names and a maximum of three names must be added", "worshipRoomName": "Worship Room Name", "worshipRoomInfo": "Worship Room Information", "worshipRoomLocationType": "Type of Worship Room Location", "worshipRoomLocation": "Worship Room Location", "worshipRoomDetails": "Worship Room Details", "worshipPlaceProposedNames": "Proposed Names", "geographicalLocationInTheAirport": "Geographical Location in the Airport", "area": "Area (Sq. m)", "capacity": "Capacity", "worshipRoomDescriptionEn": "Room Description (English)", "worshipRoomDescriptionAr": "Room Description (Arabic)"}, "purposesAndActivities": {"PurposesAndActivitiesEn": "Purpose and Activity (English)", "PurposesAndActivitiesAr": "Purpose and Activity (Arabic)", "Id": "Id", "Title": "Purposes and Activities", "Actions": "Actions", "status": "Status", "purposesAndActivitiesToolTip": "Purposes And Activities", "editPurposesAndActivities": "Edit ", "clonePurposesAndActivities": "<PERSON><PERSON> ", "removePurposeAndActivity": "Remove ", "purposesAndActivites": "Purposes / Activites", "help": "Help", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "helpMessage": "In order to submit the request, you must add at least five (5) Purposes and/or Activites"}, "roomAdministrator": {"Title": "Room Administrator", "foundingMembersLessThan70Percent": "Room Administrators holding the nationality of the State is less than (70%)", "exceptionReasonEn": "Exception Reason (EN)", "exceptionReasonAr": "Exception Reason (AR)", "foundingMembersLessThanSeven": "Number of Room Administrator is less than (7) seven Members", "foundingMembersLessThanSevenHint": "You should add at least (3) Room Administrator to proceed", "numberOfFounders": "Number of Administrators", "founderEmiratesID": "Emirates ID", "founderDateOfBirth": "Date of Birth", "addFoundingMember": "Add Room Administrator", "FoundingMember": "Room Administrator", "founderNameEnglish": "Administrator Name (English)", "founderNameArabic": "Administrator Name (Arabic)", "founderNationality": "Nationality", "founderEmail": "Email Address", "founderMobileNumber": "Mobile Number", "founderEmirate": "Administrator <PERSON><PERSON><PERSON>", "agendaItems": "Agenda Items", "founderPassportNumber": "Administrator Passport Number", "founderResidencyIssuanceDate": "Administrator Residency Issuance Date", "founderResidencyExpiryDate": "Administrator Residency Expiry Date", "founderAcademicQualification": "Administrator Academic Qualification", "founderJobTitle": "Administrator Job Title", "founderEmployer": "Administrator Em<PERSON>loyer", "founderPassportPhoto": "Administrator Passport Photo", "founderPersonalPhoto": "Administrator Personal Photo", "foundingMembersList": "Room Administrators List", "removeFoundingMember": "Remove Room Administrator", "foundersMeetingPlace": "Place", "foundersMeetingEmirate": "Emirate", "foundersMeetingDate": "Date & Time", "founderDecisionDate": "Decision Date", "foundersMeetingAgenda": "Agenda", "exceptionCases": "Exception Requests", "foundersMeeting": "Administrators Meeting", "emiratesId": "Emirates ID", "editFoundingMember": "Edit ", "cloneFoundingMember": "<PERSON><PERSON> ", "dateOfBirth": "Date of Birth", "actions": "Actions", "id": "Id", "founderEmiratesId": "Room Administrator Emirates ID", "founderBOD": "BOD", "founderNameEn": "Name", "roomAdminName": "Name", "responseDate": "Response Date", "status": "Status", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Room Administrator age is less than 21 years old", "foundingMemberAgeIsLessThan40YearsOld": "Room Administrator age is less than 40 years old", "foundingMemberResidencyIsLessThan3Years": "Room Administrator residency is less than 3 years", "foundingMemberResidencyIsLessThan5Years": "Room Administrator residency is less than 5 years", "foundingMemberResidency": "This Room Administrator residency is less than 5 years, Please enter Exception Reason", "foundingMemberHasDiplomaticStatus": "Room Administrator has a diplomatic status", "UAELocal": "Get Information", "percentageText": "The  percentage of local members is", "membershipConditions": "Membership Conditions", "condition1": "The number of Room Administrators shall not be less than seven members", "condition2": "The percentage of the Room Administrators holding the nationality of the state shall not be less than 70% of the total number of room Administrators. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of Room Administrators.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The room Administrator must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The room Administrator must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "addFoundingMembersHelpMessage": "Once the room administrator responds to the confirmation request, you will be able to view his name and nationality, as well as his response and the response date.", "minimumNumberOfFoundingMembersHelpMessage": "Minimum number of room Administrators should not be less than 20.", "minimumAgeOfRoomAdminHelpMessage": "Minimum age of Room Administrators should not be less than 40.", "foundersMeetingAgendaHelpMessage": "Founder meeting agenda:", "foundersMeetingAgendaHelpMessage1": "Discussing the establishment of a worship place.", "foundersMeetingAgendaHelpMessage2": "Preparing the draft statute.", "foundersMeetingAgendaHelpMessage3": "Election of members of the temporary committee.", "foundingMemberAgeLessThan21": "This room administrator Age Is Less Than 21 Years Old, Please enter Exception Reason", "foundingMemberAgeLessThan40": "This room administrator Age Is Less Than 40 Years Old, Please enter Exception Reason", "socialSolidarityFundsHelpMessage": "room administrators of the fund upon establishment shall not be less than (25) members", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a worship place", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "editNPO": "Edit NPO Unified Number", "cloneNPO": "Clone NPO Unified Number", "removeNPO": "Remove NPO Unified Number", "npoUnifiedNumber": "NPO Unified Number", "npoUnifiedNumberOrNPOName": "NPO Unified Number or NPO name", "addNPOUnifiedNumber": "Add NPO Unified Number", "associationsOrNationalSocietiesFromTheUnionList": "Associations or National Societies from the Union List", "npoName": "NPO Name", "npoContactDetails": "NPO Contact Details", "associationsNationalSocieties": "Associations or National Societies from the Union List", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "unionFoundersMeetingHelpMessage": "To be able to submit a request for licensing and declaring a union, you must add a minimum number of (5) associations or national societies only. It is not allowed to combine associations and national societies when forming a union.", "ByLawApproval": "By-Law Approval"}, "adminJobDetails": {"title": "NMWR Administrator’s Job Details", "airportDepartment": "The department to which the administrator at the airport reports", "jobTitle": "The administrator’s job title", "jobAppointmentDate": "The administrator’s appointment date at the airport", "letterOfAuthorization": "A letter of authorization to represent the entity to which the worship room belongs"}, "foundingMembers": {"title": "Founding Members", "foundingMembersLessThan70Percent": "Founding Members holding the nationality of the State is less than (70%)", "exceptionReasonEn": "Exception Reason (EN)", "exceptionReasonAr": "Exception Reason (AR)", "foundingMembersLessThanSeven": "Number of Founding Members is less than (7) seven Members", "foundingMembersLessThanSevenHint": "You should add at least (3) founding members to proceed", "numberOfFounders": "Number of Founding", "founderEmiratesID": "Emirates ID", "founderDateOfBirth": "Founder Date of Birth", "addFoundingMember": "Add Founding Member", "FoundingMember": "Founding Member", "founderNameEnglish": "Founder Name (English)", "founderNameArabic": "Founder Name (Arabic)", "founderNationality": "Nationality", "founderEmail": "Founder <PERSON><PERSON>", "founderMobileNumber": "Founder Mobile Number", "founderEmirate": "Founder Emira<PERSON>", "agendaItems": "Agenda Items", "founderPassportNumber": "Founder Passport Number", "founderResidencyIssuanceDate": "Founder Residency Issuance Date", "founderResidencyExpiryDate": "Founder Residency Expiry Date", "founderAcademicQualification": "Founder Academic Qualification", "founderJobTitle": "Founder Job Title", "founderEmployer": "Founder Employer", "founderPassportPhoto": "Founder Passport Photo", "founderPersonalPhoto": "Founder Personal Photo", "foundingMembersList": "Founding Members List", "removeFoundingMember": "Remove Founding Member", "foundersMeetingPlace": "Place", "foundersMeetingEmirate": "Emirate", "foundersMeetingDate": "Date & Time", "founderDecisionDate": "Founder Decision Date", "foundersMeetingAgenda": "Agenda", "exceptionCases": "Exception Requests", "foundersMeeting": "Founders Meeting", "emiratesId": "Emirates ID", "editFoundingMember": "Edit ", "cloneFoundingMember": "<PERSON><PERSON> ", "dateOfBirth": "Founder Date of Birth", "actions": "Actions", "id": "Id", "founderEmiratesId": "Founder Emirates ID", "founderBOD": "BOD", "founderNameEn": "Name", "responseDate": "Response Date", "status": "Status", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Founding member age is less than 21 years old", "foundingMemberAgeIsLessThan40YearsOld": "Founding member age is less than 40 years old", "foundingMemberResidencyIsLessThan3Years": "Founding member residency is less than 3 years", "foundingMemberResidencyIsLessThan5Years": "Founding member residency is less than 5 years", "foundingMemberResidency": "This Founding member residency is less than 5 years, Please enter Exception Reason", "foundingMemberHasDiplomaticStatus": "Founding member has a diplomatic status", "UAELocal": "Get Information", "percentageText": "The  percentage of local members is", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "addFoundingMembersHelpMessage": "Once the founding member responds to the confirmation request, you will be able to view his name and nationality, as well as his response and the response date.", "minimumNumberOfFoundingMembersHelpMessage": "Minimum number of Founding Members should not be less than 20.", "minimumAgeOfFoundingMembersHelpMessage": "Minimum age of Founding Members should not be less than 40.", "foundersMeetingAgendaHelpMessage": "Founder meeting agenda:", "foundersMeetingAgendaHelpMessage1": "Discussing the establishment of a worship place.", "foundersMeetingAgendaHelpMessage2": "Preparing the draft statute.", "foundersMeetingAgendaHelpMessage3": "Election of members of the temporary committee.", "foundingMemberAgeLessThan21": "This Founding Member Age Is Less Than 21 Years Old, Please enter Exception Reason", "foundingMemberAgeLessThan40": "This Founding Member Age Is Less Than 40 Years Old, Please enter Exception Reason", "socialSolidarityFundsHelpMessage": "Founding members of the fund upon establishment shall not be less than (25) members", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a worship place", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "editNPO": "Edit NPO Unified Number", "cloneNPO": "Clone NPO Unified Number", "removeNPO": "Remove NPO Unified Number", "npoUnifiedNumber": "NPO Unified Number", "npoUnifiedNumberOrNPOName": "NPO Unified Number or NPO name", "addNPOUnifiedNumber": "Add NPO Unified Number", "associationsOrNationalSocietiesFromTheUnionList": "Associations or National Societies from the Union List", "npoName": "NPO Name", "npoContactDetails": "NPO Contact Details", "associationsNationalSocieties": "Associations or National Societies from the Union List", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "unionFoundersMeetingHelpMessage": "To be able to submit a request for licensing and declaring a union, you must add a minimum number of (5) associations or national societies only. It is not allowed to combine associations and national societies when forming a union.", "ByLawApproval": "By-Law Approval"}, "interimCommittee": {"Id": "Id", "temporaryCommitteeMembersMeetingPlace": "Meeting Place", "temporaryCommitteeMembersMeetingEmirate": "Emirate", "temporaryCommitteeMembersMeetingDateTime": "Date & Time", "temporaryCommitteeMember": "Temporary Committee Member", "temporaryCommitteeMemberPosition": "Position", "addCommitteeMember": "Add Committee Member", "committeeMemberList": "Committee Member List", "removeTemporaryCommitteeMember": "Remove Temporary Committee Member", "Title": "Interim Committee", "Actions": "Actions", "editMember": "Edit", "cloneMember": "<PERSON><PERSON>", "agendaItems": "Agenda Items", "removeMember": "Remove", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "temporaryCommitteeMemberEID": "Emirates ID", "temporaryCommitteeMemberName": "Name", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "interimCommitteeMembersHelpMessage": "The number of members of the interim committee shall not be less than three members.", "chairmanRequiredHelpMessage": "There must be at least one member who holds the position of Chairman of the Committee.", "interimCommitteeInformationHelpMessage": "Interim Committee meeting agenda", "interimCommitteeInformationHelpMessage1": "Define the administrative positions for the members", "interimCommitteeInformationHelpMessage2": "Appoint the represenative of the interim committee.", "Definetheadministrativepositions": "Define the administrative positions for the members", "AppointCommiteeRep": "Appoint the represenative of the interim committee", "Appointthecommissioner": "Appoint the represenative of the interim committee", "status": "Status"}, "membership": {"title": "Membership", "id": "Id", "membershipFees": "Membership Fees", "annualMembershipDueDate": "Annual Membership Due Date", "membershipConditionEn": "Membership Condition (EN)", "membershipConditionAr": "Membership Condition (AR)", "addCondition": "Add Membership Conditions", "membershipConditionsList": "Membership Conditions List", "Actions": "Actions", "cancel": "cancel", "editCondition": "Edit ", "cloneCondition": "<PERSON><PERSON> ", "removeCondition": "Remove ", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "status": "Status"}, "boardInformation": {"Id": "Id", "Title": "Board Information", "Actions": "Actions", "boardMembersExceed11": "The number of Board Members exceeds 11 members", "exceptionReasonEn": "Exception Reason (EN)", "exceptionReasonAr": "Exception Reason (AR)", "numberOfBoardMembers": "Number of Board Members", "frequencyOfBoardMeetings": "Frequency of Board Meetings", "BoardElectionCycle": "Board Election Frequency", "boardMemberReNomination": "Can Board Members Be Renominated for Another Term?", "numberOfPermissibleTerms": "Number of Permissible Terms", "localBoardMembersPercentage": "Percentage of UAE Local Board Members", "electionMethod": "Election Method", "nominationConditionEnglish": "Condition for Nomination (English)", "nominationConditionArabic": "Condition for Nomination (Arabic)", "addNominationCondition": "Add Condition", "nominationConditionsList": "Conditions for Nomination for membership in the Board of Directors List", "removeNominationCondition": "Remove Condition", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "addPosition": "Add Position", "boardAdminPositionsList": "Board Administrative Positions List", "editCondition": "Edit ", "cloneCondition": "<PERSON><PERSON> ", "removeCondition": "Remove ", "editPosition": "Edit ", "clonePosition": "<PERSON><PERSON> ", "removePosition": "Remove ", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Administrative Board Information", "boardOfDirectorsConditions": "Administrative Board Conditions", "boardOfDirectorsPositions": "Administrative Board Positions", "help": "Help", "status": "Status", "mustBeFiveMembersAtLeast": "The number of board members must not be less than 5."}, "uploadDocuments": {"title": "Documents", "uploadDocuments": "Upload Documents", "SectionTitle1": "Required Document for Initial Approval ", "SectionTitle2": "Required Document for Final Approval ", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "requiredDocuments": "Required Document for Initial Approval", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the NMWP logo at any time after submitting the information, however it is mandatory before the ministry's approval."}, "initialApprovalExtension": {"ReasonDetailsEnPopup": "Extension Reason Details (English)", "ReasonDetailsArPopup": "Extension Reason Details (Arabic)", "ReasonDetailsEn": "Extension Reason Details (English)", "ReasonDetailsAr": "Extension Reason Details (Arabic)", "Id": "Id", "Title": "Initial Approval Extension", "Actions": "Actions", "initialApprovalExtensionToolTip": "Initial Approval Extensions Of The Worship Place", "editInitialApprovalExtension": "Edit ", "cloneInitialApprovalExtension": "<PERSON><PERSON> ", "removeInitialApproval": "Remove ", "initialApprovalExtensions": "Initial Approval Extension", "help": "Help", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "helpMessage": "In order to submit the request, you must add at least five objectives", "extensionRequestReasonDetails": "Extension Request Reason", "reasonTitle": "Reason Title", "ExtensionRequestReason": "Extension Request Reason", "ReferenceNumber": "Referance Number", "RequestDate": "Request Date", "Status": "Request Status", "AddExtensionRequest": "Add Extension Request", "UpdateExtensionRequest": "Update Extension Request"}, "reviewAndSubmit": {"roomAdminName": "Name", "adminJobDetails": "NMWR Administrator’s Job Details", "airportDepartment": "The department to which the administrator at the airport reports", "jobTitle": "The administrator’s job title", "jobAppointmentDate": "The administrator’s appointment date at the airport", "letterOfAuthorization": "A letter of authorization to represent the entity to which the worship room belongs", "mobileNumber": "Mobile Number", "title": "Review & Submit", "BasicInformation": "Basic Information", "RoomAdministrator": "Room Administrator", "worshipPlaceName": "Worship Place Name", "worshipPlaceInformation": "Worship Place Information", "parentWorshipPlaceInfo": "Parent Worship Place Information", "religion": "Religion", "EmiratesId": "Emirates ID", "NameEn": "Name (EN)", "Name": "Name", "worshipRoomName": "Worship Room Name", "worshipRoomInfo": "Worship Room Information", "worshipRoomLocationType": "Type of Worship Room Location", "worshipRoomLocation": "Worship Room Location", "worshipRoomDetails": "Worship Room Details", "sect": "Sect", "belief": "Belief", "brief": "Brief about the Religion, Sect, or Belief", "religiousPractices": "Religious practices, worship, rituals and ceremonies", "programTimings": "Program and timings for performing worship, rituals and ceremonies", "controlsAndProcedures": "Controls and procedures for performing worship, rituals and ceremonies", "worshipPlaceType": "Type of Worship Place", "typeName": "Type Name", "parentHouseOfWorshipPlaceNameEn": "Parent House of Worship Place Name (English)", "parentHouseOfWorshipPlaceNameAr": "Parent House of Worship Place Name (Arabic)", "country": "Country", "proposedNameEn": "Proposed Name (English)", "proposedNameAr": "Proposed Name (Arabic)", "landlineNumber": "Landline Number", "poBox": "PO Box", "email": "Email", "website": "Website", "address": "Address", "geographicLocation": "Geographic Location", "emirate": "Emirate", "contactDetails": "Contact Details", "status": "Status", "PurposesAndActivitiesEn": "Purpose and Activity (English)", "PurposesAndActivitiesAr": "Purpose and Activity (Arabic)", "PurposesAndActivities": "Purposes and Activities", "foundingMembers": "Founding Members", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan40YearsOld": "Founding member age is less than 40 years old", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "MeetingPlace": "Meeting Place", "MeetingDate": "Date & Time", "agendaItems": "Agenda Items", "founderNameEn": "Name (English)", "founderNameAr": "Name (Arabic)", "nationality": "Nationality", "responseDate": "Response Date", "foundersMeeting": "Founders Meeting", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a worship place", "ByLawApproval": "By-Law Approval", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Date": "Date", "temporaryCommitteeMember": "Temporary Committee Member", "committeeMemberList": "Committee Member List", "interimCommittee": "Interim Committee", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "Definetheadministrativepositions": "Define the administrative positions for the members", "AppointCommiteeRep": "Appoint the represenative of the interim committee", "membership": "Membership", "membershipFees": "Membership Fees", "membershipConditionEn": "Membership Condition (EN)", "membershipConditionAr": "Membership Condition (AR)", "membershipConditionsList": "Membership Conditions List", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "boardInformation": "Board Information", "numberOfBoardMembers": "Number of Board Members", "frequencyOfBoardMeetings": "Frequency of Board Meetings", "BoardElectionCycle": "Board Election Frequency", "boardMemberReNomination": "Can Board Members Be Renominated for Another Term?", "NumberOfPermissibleTerms": "Number of Permissible Terms", "electionMethod": "Election Method", "nominationConditionEnglish": "Condition for Nomination (English)", "nominationConditionArabic": "Condition for Nomination (Arabic)", "nominationConditionsList": "Conditions for Nomination for membership in the Board of Directors List", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "boardAdminPositionsList": "Administrative Positions List", "exceptionRequests": "Exception Requests", "boardConditions": "Board Conditions", "boardPositions": "Board Positions", "requiredDocuments": "Required Document for Initial Approval", "uploadDocuments": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "initialApprovalExtension": "Initial Approval Extension", "ReasonDetailsEn": "Extension Reason Details (English)", "ReasonDetailsAr": "Extension Reason Details (Arabic)", "initialApprovalExtensions": "Initial Approval Extensions", "extensionRequestReasonDetails": "Extension Request Reason Details", "extensionRequestReasonEn": "Extension Request Reason (EN)", "extensionRequestReasonAr": "Extension Request Reason (AR)", "extensionRequestsList": "Extension Requests List", "referenceNumber": "Reference Number", "requestDate": "Request Date", "requestStatus": "Request Status", "downloadBylaws": "Download the By-laws", "getFoundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "localBoardMembersPercentage": "Local Board Members Percentage", "memberPosition": "Member Position", "exceptionCases": "Exception Cases", "isFoundingMemberAgeLessThan40": "Is the founding member under 40 years old? Please enter the reason for the exception.", "npoLegalForm": "NPO Legal Form", "localDecreeLawNumber": "Local Decree Law Number", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "associationClassification": "Association Classification", "npoName": "NPO Name", "NameAr": "Name (AR)", "npoContactDetails": "NPO Contact Details", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "GeographicLocation": "Geographic Location", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "Objectives": "Objectives", "boardOfDirectors": "Board of Directors", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "Agenda": "Agenda", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "ElectionMethod": "Election Method", "MemberIsExceeds11": "Member Exceeds 11", "boardOfDirectorsInformation": "Board of Directors Information", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "PreparingTheDraftStatute": "Preparing the draft statute", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "Status": "Status", "geographicalLocationInTheAirport": "Geographical Location in the Airport", "area": "Area (Sq. m)", "capacity": "Capacity", "worshipRoomDescriptionEn": "Room Description (English)", "worshipRoomDescriptionAr": "Room Description (Arabic)"}, "requestDetails": {"adminJobDetails": "Administrator’s Job Details", "airportDepartment": "The department to which the administrator at the airport reports", "jobTitle": "The administrator’s job title", "jobAppointmentDate": "The administrator’s appointment date at the airport", "title": "Review & Submit", "BasicInformation": "Basic Information", "RoomAdministrator": "Room Administrator", "worshipPlaceName": "Worship Place Name", "worshipPlaceInformation": "Worship Place Information", "parentWorshipPlaceInfo": "Parent Worship Place Information", "religion": "Religion", "EmiratesId": "Emirates ID", "NameEn": "Name (EN)", "worshipRoomName": "Worship Room Name", "worshipRoomInfo": "Worship Room Information", "worshipRoomLocationType": "Type of Worship Room Location", "worshipRoomLocation": "Worship Room Location", "worshipRoomDetails": "Worship Room Details", "sect": "Sect", "belief": "Belief", "brief": "Brief about the Religion, Sect, or Belief", "religiousPractices": "Religious practices, worship, rituals and ceremonies", "programTimings": "Program and timings for performing worship, rituals and ceremonies", "controlsAndProcedures": "Controls and procedures for performing worship, rituals and ceremonies", "worshipPlaceType": "Type of Worship Place", "typeName": "Type Name", "parentHouseOfWorshipPlaceNameEn": "Parent House of Worship Place Name (English)", "parentHouseOfWorshipPlaceNameAr": "Parent House of Worship Place Name (Arabic)", "country": "Country", "proposedNameEn": "Proposed Name (English)", "proposedNameAr": "Proposed Name (Arabic)", "landlineNumber": "Landline Number", "poBox": "PO Box", "email": "Email", "website": "Website", "address": "Address", "geographicLocation": "Geographic Location", "emirate": "Emirate", "contactDetails": "Contact Details", "status": "Status", "PurposesAndActivitiesEn": "Purpose and Activity (English)", "PurposesAndActivitiesAr": "Purpose and Activity (Arabic)", "PurposesAndActivities": "Purposes and Activities", "foundingMembers": "Founding Members", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan40YearsOld": "Founding member age is less than 40 years old", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "MeetingPlace": "Meeting Place", "MeetingDate": "Date & Time", "agendaItems": "Agenda Items", "founderNameEn": "Name (English)", "founderNameAr": "Name (Arabic)", "nationality": "Nationality", "responseDate": "Response Date", "foundersMeeting": "Founders Meeting", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a worship place", "ByLawApproval": "By-Law Approval", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Date": "Date", "temporaryCommitteeMember": "Temporary Committee Member", "committeeMemberList": "Committee Member List", "interimCommittee": "Interim Committee", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "Definetheadministrativepositions": "Define the administrative positions for the members", "AppointCommiteeRep": "Appoint the represenative of the interim committee", "membership": "Membership", "membershipFees": "Membership Fees", "membershipConditionEn": "Membership Condition (EN)", "membershipConditionAr": "Membership Condition (AR)", "membershipConditionsList": "Membership Conditions List", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "boardInformation": "Board Information", "numberOfBoardMembers": "Number of Board Members", "frequencyOfBoardMeetings": "Frequency of Board Meetings", "BoardElectionCycle": "Board Election Frequency", "boardMemberReNomination": "Can Board Members Be Renominated for Another Term?", "NumberOfPermissibleTerms": "Number of Permissible Terms", "electionMethod": "Election Method", "nominationConditionEnglish": "Condition for Nomination (English)", "nominationConditionArabic": "Condition for Nomination (Arabic)", "nominationConditionsList": "Conditions for Nomination for membership in the Board of Directors List", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "boardAdminPositionsList": "Administrative Positions List", "exceptionRequests": "Exception Requests", "boardConditions": "Board Conditions", "boardPositions": "Board Positions", "requiredDocuments": "Required Document for Initial Approval", "uploadDocuments": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "initialApprovalExtension": "Initial Approval Extension", "ReasonDetailsEn": "Extension Reason Details (English)", "ReasonDetailsAr": "Extension Reason Details (Arabic)", "initialApprovalExtensions": "Initial Approval Extensions", "extensionRequestReasonDetails": "Extension Request Reason Details", "extensionRequestReasonEn": "Extension Request Reason (EN)", "extensionRequestReasonAr": "Extension Request Reason (AR)", "extensionRequestsList": "Extension Requests List", "referenceNumber": "Reference Number", "requestDate": "Request Date", "requestStatus": "Request Status", "downloadBylaws": "Download the By-laws", "getFoundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "localBoardMembersPercentage": "Local Board Members Percentage", "memberPosition": "Member Position", "exceptionCases": "Exception Cases", "isFoundingMemberAgeLessThan40": "Is the founding member under 40 years old? Please enter the reason for the exception.", "npoLegalForm": "NPO Legal Form", "localDecreeLawNumber": "Local Decree Law Number", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "associationClassification": "Association Classification", "npoName": "NPO Name", "NameAr": "Name (AR)", "npoContactDetails": "NPO Contact Details", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "GeographicLocation": "Geographic Location", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "Objectives": "Objectives", "boardOfDirectors": "Board of Directors", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "Agenda": "Agenda", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "ElectionMethod": "Election Method", "MemberIsExceeds11": "Member Exceeds 11", "boardOfDirectorsInformation": "Board of Directors Information", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "PreparingTheDraftStatute": "Preparing the draft statute", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "ObjectivesEn": "Objectives (English)", "ObjectivesAR": "Objectives (Arabic)", "boardOfTrustees": "Board Of Trustees", "Means of Achieving Objective (English)": "Means of Achieving Objective (English)", "Means of Achieving Objective (Arabic)": "Means of Achieving Objective (Arabic)", "NPO_LICENSE_DECLARATION": "Non-Profit Organization Declaration Request", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "COMMENT": "Comment", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section of ", "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save", "geographicalLocationInTheAirport": "Geographical Location in the Airport", "area": "Area (Sq. m)", "capacity": "Capacity", "worshipRoomDescriptionEn": "Room Description (English)", "worshipRoomDescriptionAr": "Room Description (Arabic)", "mobileNumber": "Mobile Number"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "Issue License For Non-Muslims Worship Place", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "Issue License For Non-Muslims Worship Place Founding Members Confirmation"}, "congratulations": "Congratulations", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "fundraisingService": {"title": "Request to Issue Fundraising Permit", "title-extend": "Request To Extend Fundraising Permit", "desc": "", "forms": {"legalTypePage": {"title": "Request to Issue Fundraising Permit", "description": "", "selectNpoLegalForm": "Select Service Type"}, "entityDetails": {"title": "Entity Details", "sectionTitle": "Entity Details Requesting the Permit", "entityDetailsRequestingThePermit": "Entity Details Requesting the Permit", "npoLegalForm": "NPO Legal Form", "issuanceDate": "Issuance Date", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "EntityNameEn": "Entity Name (English)", "EntityNameAr": "Entity Name (Arabic)", "EntityCategory": "Entity Category", "MainActivity": "Main Activity", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "edit": "Edit ", "clone": "<PERSON><PERSON> ", "remove": "Remove ", "emirate": "Emirate", "add": "Add", "id": "Id", "actions": "Actions", "status": "Status", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "help": "Help", "helpMessage": "", "search": "Search", "accountName": "Select Entity"}, "fundraisingSpecifications": {"title": "Fundraising Details", "newFundraisingPermitException": "Exception Reason for Submitting New Fundraising Permit (more than 4 permits per year)", "fundraisingPermitsMoreThanFourHint": "You should add at least (3) founding members to proceed", "exceptionReasonEn": "Exception Reason (English)", "exceptionReasonAr": "Exception Reason (Arabic)", "purposeOfFundraising": "Fundraising Details", "purposeOfFundraisingEn": "Purpose of Fundraising (English)", "purposeOfFundraisingAr": "Purpose of Fundraising (Arabic)", "fundraisingDuration": "Fundraising Duration", "fundraisingStartDate": "Fundraising Start Date", "fundraisingEndDate": "Fundraising End Date", "fundraisingLocations": "Fundraising Location List", "fundraisingLocation": "Fundraising Location", "addFundraisingLocation": "Add Fundraising Location", "editFundraisingLocation": "Edit Fundraising Location", "editCashFundraisingType": "Edit Cash Fundraising Type", "editCashCollectionMethod": "Edit Cash Collection Method", "editInkindFundraising": "Edit Inkind Fundraising Type", "fundraisingEmirate": "Fundraising Emirate", "fundraisingAddress": "Fundraising Address", "fundraisingResources": "Fundraising Resources", "fundraisingType": "Fundraising Type", "fundraisingTargetAmount": "Fundraising Target Amount", "fundraisingTargetAmount(AED)": "Fundraising Target Amount (AED)", "cashFundraisingTypes": "Cash Fundraising Types", "methodsOfCollectingCashDonations": "Methods of Collecting Cash Donations", "addCashFundraisingType": "Add Cash Fundraising Type", "addNewCashCollectionMethod": "Add New Cash Collection Method", "typeOfCashDonations": "Type of Cash Donations", "typeOfCashDonationsEn": "Type of Cash Donations (English)", "typeOfCashDonationsAr": "Type of Cash Donations (Arabic)", "currencyType": "Currency Type", "approximateValue(AED)": "Approximate Value in (AED)", "Actions": "Actions", "select": "Select", "cashCollectionMethod": "Cash Collection Method", "cashCollectionMethodEn": "Cash Collection Method (EN)", "cashCollectionMethodAr": "Cash Collection Method (AR)", "descriptionEn": "Description (EN)", "descriptionAr": "Description (AR)", "inKindFundraisingTypes": "In-Kind Fundraising Types", "addInkindFundraisingType": "Add In-Kind Fundraising Type", "typeOfInkindDonations": "Type of In-kind Donations", "typeOfInkindDonationsEn": "Type of In-kind Donations (EN)", "typeOfInkindDonationsAr": "Type of In-kind Donations (AR)", "countOrQuantity": "Count/Quantity", "methodsOfCollectingInkindDonations": "Methods of Collecting In-kind Donations", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "conditions": "Conditions", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "targetAmountError": "Approximate Values must be equal to Target Amount added."}, "npoAssignee": {"title": "Fundraising NPO Assignee", "npoLicensedForFundraising": "NPO Licensed for Fundraising", "selectNpoLicensedForFundraising": "Select NPO Licensed for Fundraising", "percentageOfAdminAndOperatingExpenses": "Percentage of Administrative and Operating Expenses", "enterPercentageOfAdminAndOperatingExpenses": "Enter Percentage  of Administrative and Operating Expenses", "NpoNameEn": "Association Name (English)", "NpoNameAr": "Association Name (Arabic)", "NpoLocation": "Association Location", "licensingEntity": "Licensing Entity", "licenseValidity": "License Validity", "startDate": "Start Date", "endDate": "End Date", "responsiblePersonName": "Responsible Person Name", "responsiblePersonNationality": "Nationality", "responsiblePersonPhone": "Mobile Number", "responsiblePersonEmail": "Email Address"}, "beneficiaryDetails": {"title": "Fundraising Beneficiary(s) Details", "donationStartDate": "Submitting Donations From Date", "donationEndDate": "Submitting Donations To Date", "beneficiariesOfFundRaising": "Beneficiary(s) of Fundraising", "addFundraisingBeneficiary": "Add Fundraising Beneficiary", "editBeneficiary": "Edit Fundraising Beneficiary", "locaion": "Location", "selectBeneficiaryLocation": "Select Beneficiary Location", "beneficiaryLocation": "Beneficiary Location", "country": "Country", "selectCountry": "Select Country", "beneficiaryCategoryEn": "Beneficiary Category (EN)", "beneficiaryCategoryAr": "Beneficiary Category (AR)", "descriptionEn": "Description (EN)", "descriptionAr": "Description (AR)", "addBeneficiary": "Add Beneficiary", "Actions": "Actions", "status": "Status", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "conditions": "Conditions", "searchPlaceholder": "type your search keyword ...", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)"}, "previousPermits": {"title": "Previous Fundraising Permits", "permitNumber": "Permit Number", "establishmentName": "Establishment Name", "legalForm": "Legal Form", "fundraisingLocation": "Fundraising Location", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "actions": "Actions", "conditions": "Conditions", "noDataFound": "No Data Found", "view": "View", "manage": "Manage"}, "permitExtensionDetails": {"title": "Permit Extension Details", "permitExtensionReasons": "Permit Extension Reasons", "permitExtensionReasonEn": "Permit Extension Reasons (English)", "permitExtensionReasonAr": "Permit Extension Reasons (Arabic)", "permitExtensionDuration": "Permit Extension Duration", "permitExtensionStartDate": "Permit Extension Start Date", "permitExtensionEndDate": "Permit Extension End Date"}, "appealToRejectionDetails": {"title": "Appeal To Rejection Details", "appealReasons": "Appeal Reasons", "appealReasonEn": "Appeal Reasons (English)", "appealReasonAr": "Appeal Reasons (Arabic)"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "responsibleNpo": "Responsible NPO for Collecting Fundraising Approval", "letterCopy": "Copy of Official Letter Addressed to the Ministry", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the document that identifies the Responsible NPO for Collecting Fundraising Approval.", "letterCopyMessage": "You may attach the Copy of Official Letter Addressed to the Ministry."}, "reviewAndSubmit": {"permitExtensionDetails": "Permit Extension Details", "permitExtensionReasons": "Permit Extension Reasons", "permitExtensionReasonEn": "Permit Extension Reasons (English)", "permitExtensionReasonAr": "Permit Extension Reasons (Arabic)", "permitExtensionDuration": "Permit Extension Duration", "permitExtensionStartDate": "Permit Extension Start Date", "permitExtensionEndDate": "Permit Extension End Date", "appealToRejectionDetails": "Appeal To Rejection Details", "appealReasons": "Appeal Reasons", "appealReasonEn": "Appeal Reasons (English)", "appealReasonAr": "Appeal Reasons (Arabic)", "previousPermits": "Previous Fundraising Permits", "permitNumber": "Permit Number", "establishmentName": "Establishment Name", "legalForm": "Legal Form", "fundraisingLocation": "Fundraising Location", "startDate": "Start Date", "endDate": "End Date", "actions": "Actions", "conditions": "Conditions", "beneficiaryDetails": "Fundraising Beneficiary(s) Details", "donationStartDate": "Submitting Donations From Date", "donationEndDate": "Submitting Donations To Date", "beneficiariesOfFundRaising": "Beneficiary(s) of Fundraising", "addFundraisingBeneficiary": "Add Fundraising Beneficiary", "editBeneficiary": "Edit Fundraising Beneficiary", "locaion": "Location", "beneficiaryLocation": "Beneficiary Location", "country": "Country", "selectCountry": "Select Country", "beneficiaryCategoryEn": "Beneficiary Category (EN)", "beneficiaryCategoryAr": "Beneficiary Category (AR)", "descriptionEn": "Description (EN)", "descriptionAr": "Description (AR)", "addBeneficiary": "Add Beneficiary", "npoAssignee": "Fundraising NPO Assignee", "selectNpoLicensedForFundraising": "NPO Licensed for Fundraising", "percentageOfAdminAndOperatingExpenses": "Percentage of Administrative and Operating Expenses", "NpoNameEn": "Association Name (English)", "NpoNameAr": "Association Name (Arabic)", "NpoLocation": "Association Location", "licensingEntity": "Licensing Entity", "licenseValidity": "License Validity", "responsiblePersonName": "Responsible Person Name", "responsiblePersonNationality": "Nationality", "responsiblePersonPhone": "Mobile Number", "responsiblePersonEmail": "Email Address", "fundraisingSpecifications": "Fundraising Details", "newFundraisingPermitException": "Exception Reason for Submitting New Fundraising Permit (more than 4 permits per year)", "purposeOfFundraising": "Fundraising Details", "purposeOfFundraisingEn": "Purpose of Fundraising (English)", "purposeOfFundraisingAr": "Purpose of Fundraising (Arabic)", "fundraisingDuration": "Fundraising Duration", "fundraisingStartDate": "Fundraising Start Date", "fundraisingEndDate": "Fundraising End Date", "fundraisingLocations": "Fundraising Location List", "addFundraisingLocation": "Add Fundraising Location", "editFundraisingLocation": "Edit Fundraising Location", "fundraisingEmirate": "Fundraising Emirate", "fundraisingAddress": "Fundraising Address", "fundraisingResources": "Fundraising Resources", "fundraisingType": "Fundraising Type", "fundraisingTargetAmount": "Fundraising Target Amount", "fundraisingTargetAmount(AED)": "Fundraising Target Amount (AED)", "cashFundraisingTypes": "Cash Fundraising Types", "methodsOfCollectingCashDonations": "Methods of Collecting Cash Donations", "addFundraisingType": "Add Fundraising Type", "addNewCashCollectionMethod": "Add New Cash Collection Method", "typeOfCashDonationsEn": "Type of Cash Donations (English)", "typeOfCashDonationsAr": "Type of Cash Donations (Arabic)", "currencyType": "Currency Type", "approximateValue(AED)": "Approximate Value in (AED)", "select": "Select", "cashCollectionMethodEn": "Cash Collection Method (EN)", "cashCollectionMethodAr": "Cash Collection Method (AR)", "inKindFundraisingTypes": "In-Kind Fundraising Types", "typeOfInkindDonationsEn": "Type of In-kind Donations (EN)", "typeOfInkindDonationsAr": "Type of In-kind Donations (AR)", "countOrQuantity": "Count/Quantity", "methodsOfCollectingInkindDonations": "Methods of Collecting In-kind Donations", "entityDetails": "Entity Details", "entityDetailsRequestingThePermit": "Entity Details Requesting the Permit", "MainActivity": "Main Activity", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "edit": "Edit ", "clone": "<PERSON><PERSON> ", "remove": "Remove ", "emirate": "Emirate", "add": "Add", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "helpMessage": "", "EntityCategory": "Entity Category", "npoLegalForm": "NPO Legal Form", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "nationalityType": "Nationality Type", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "exceptionRequests": "Exception Requests", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "Id": "Id", "status": "Status", "responseDate": "Response Date", "yes": "Yes", "no": "No", "id": "Id", "help": "Help", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "category": "Category"}, "requestDetails": {"previousPermits": "Previous Permits", "permitNumber": "Permit Number", "establishmentName": "Establishment Name", "legalForm": "Legal Form", "fundraisingLocation": "Fundraising Location", "startDate": "Start Date", "endDate": "End Date", "actions": "Actions", "conditions": "Conditions", "beneficiaryDetails": "Beneficiary Details", "donationStartDate": "Submitting Donations From Date", "donationEndDate": "Submitting Donations To Date", "beneficiariesOfFundRaising": "Beneficiary(s) of Fundraising", "addFundraisingBeneficiary": "Add Fundraising Beneficiary", "editBeneficiary": "Edit Fundraising Beneficiary", "locaion": "Location", "beneficiaryLocation": "Beneficiary Location", "country": "Country", "selectCountry": "Select Country", "beneficiaryCategoryEn": "Beneficiary Category (EN)", "beneficiaryCategoryAr": "Beneficiary Category (AR)", "descriptionEn": "Description (EN)", "descriptionAr": "Description (AR)", "addBeneficiary": "Add Beneficiary", "npoAssignee": "Fundraising NPO Assignee", "selectNpoLicensedForFundraising": "NPO Licensed for Fundraising", "percentageOfAdminAndOperatingExpenses": "Percentage of Administrative and Operating Expenses", "NpoNameEn": "Association Name (English)", "NpoNameAr": "Association Name (Arabic)", "NpoLocation": "Association Location", "licensingEntity": "Licensing Entity", "licenseValidity": "License Validity", "responsiblePersonName": "Responsible Person Name", "responsiblePersonNationality": "Nationality", "responsiblePersonPhone": "Mobile Number", "responsiblePersonEmail": "Email Address", "fundraisingSpecifications": "Fundraising Details", "newFundraisingPermitException": "Exception Reason for Submitting New Fundraising Permit (more than 4 permits per year)", "fundraisingPermitsMoreThanFourHint": "You should add at least (3) founding members to proceed", "purposeOfFundraising": "Fundraising Details", "purposeOfFundraisingEn": "Purpose of Fundraising (English)", "purposeOfFundraisingAr": "Purpose of Fundraising (Arabic)", "fundraisingDuration": "Fundraising Duration", "fundraisingStartDate": "Fundraising Start Date", "fundraisingEndDate": "Fundraising End Date", "fundraisingLocations": "Fundraising Location List", "addFundraisingLocation": "Add Fundraising Location", "editFundraisingLocation": "Edit Fundraising Location", "fundraisingEmirate": "Fundraising Emirate", "fundraisingAddress": "Fundraising Address", "fundraisingResources": "Fundraising Resources", "fundraisingType": "Fundraising Type", "fundraisingTargetAmount": "Fundraising Target Amount", "fundraisingTargetAmount(AED)": "Fundraising Target Amount (AED)", "cashFundraisingTypes": "Cash Fundraising Types", "methodsOfCollectingCashDonations": "Methods of Collecting Cash Donations", "addFundraisingType": "Add Fundraising Type", "addNewCashCollectionMethod": "Add New Cash Collection Method", "typeOfCashDonationsEn": "Type of Cash Donations (English)", "typeOfCashDonationsAr": "Type of Cash Donations (Arabic)", "currencyType": "Currency Type", "approximateValue(AED)": "Approximate Value in (AED)", "select": "Select", "cashCollectionMethodEn": "Cash Collection Method (EN)", "cashCollectionMethodAr": "Cash Collection Method (AR)", "inKindFundraisingTypes": "In-Kind Fundraising Types", "typeOfInkindDonationsEn": "Type of In-kind Donations (EN)", "typeOfInkindDonationsAr": "Type of In-kind Donations (AR)", "countOrQuantity": "Count/Quantity", "methodsOfCollectingInkindDonations": "Methods of Collecting In-kind Donations", "entityDetails": "Entity Details", "entityDetailsRequestingThePermit": "Entity Details Requesting the Permit", "MainActivity": "Main Activity", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "edit": "Edit ", "clone": "<PERSON><PERSON> ", "remove": "Remove ", "emirate": "Emirate", "add": "Add", "Sort by": "Sort by", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Date (Oldest)": "Date (Oldest)", "Date (Newest)": "Date (Newest)", "helpMessage": "", "EntityCategory": "Entity Category", "npoLegalForm": "NPO Legal Form", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "nationalityType": "Nationality Type", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "exceptionRequests": "Exception Requests", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "Id": "Id", "status": "Status", "responseDate": "Response Date", "yes": "Yes", "no": "No", "id": "Id", "help": "Help", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "category": "Category", "fundraisingService": "Request to Issue Fundraising Permit", "GeographicLocation": "Geographic Location", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section for ", "COMMENT": "Comment", "congratulations": "The request has been submitted successfully"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "joiningAndAffiliatingAssociationsService": {"title": "Affiliate, Subscribe, or Join Associations or Regional and International Entities ", "desc": "", "forms": {"entityDetails": {"title": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date"}, "externalEntityDetails": {"title": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Specified Joining Date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "typeName": "Type Name"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the NPO logo at any time after submitting the information, however it is mandatory before the ministry's approval."}, "reviewAndSubmit": {"entityDetails": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Start date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category", "typeName": "Type Name"}, "requestDetails": {"title": "Review & Submit", "joiningAndAffiliatingAssociationsService": "Affiliate, Subscribe, or Join Associations or Regional and International Entities", "entityDetails": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Start date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectivesEn": "Objectives (English)", "ObjectivesAR": "Objectives (Arabic)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Meeting Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective (English)": "Means of Achieving Objective (English)", "Means of Achieving Objective (Arabic)": "Means of Achieving Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founding Meeting", "NPO_LICENSE_DECLARATION": "Non-Profit Organization Declaration Request", "NPO_ByDecree": "NPO By Decree Registration", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section for ", "COMMENT": "Comment", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "fundServiceObjectives": "Fund Activities and Objectives", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "category": "Category", "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save", "typeName": "Type Name"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "organizingEventsAndActivities": {"title": "Organize Activities / Events (Inside UAE)", "desc": "", "forms": {"entityDetails": {"title": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "worshipPlaceNameEn": "Worship Place Name (EN)", "worshipPlaceNameAr": "Worship Place Name (AR)", "religion": "Religion", "sect": "Sect", "worshipPlaceType": "Type of Worship Place"}, "activityAndEventDetails": {"title": "Activity / Event Details", "requestType": "Request Type", "npoActivityOrEventAssociation": "NPO Activity or Event Association", "selectBranch": "Select Branch", "targetGroupOfTheActivityOrEvent": "Target Group of the Activity or Event", "natureOfTheActivityOrEvent": "Nature of the Activity or Event", "thePurposeOfTheActivityOrEvent": "The Purpose of the Activity or Event", "workingPapersSummary": "A summary of the work papers that will be presented during the activity or event"}, "participantDetails": {"title": "Participant Details", "willHostParticipants": "Are you going to host any participants?", "Participants": "Participants", "insideUae": "Inside UAE", "outsideUae": "Outside UAE", "addParticipantsInsideUae": "Add Participants from inside UAE", "addParticipantsOutsideUae": "Add Participants from outside UAE", "addParticipant": "Add Participant", "idNumber": "ID Number", "dateOfBirth": "Date Of Birth", "name": "Name", "purposeOfHosting": "Purpose of Hosting", "actions": "Actions", "fullName": "Full Name", "nationality": "Nationality", "passportNumber": "Passport Number", "eidNumber": "EID Number", "getInformation": "Get Information", "editParticipantInsideUae": "Edit Participants from inside UAE", "editParticipantOutsideUae": "Edit Participants from outside UAE", "passportCopy": "Passport Copy"}, "executionDateAndPlace": {"title": "Execution Date and Place", "executionDate": "Execution Date", "executionPlace": "Execution Place", "startDate": "Start Date", "endDate": "End Date", "placeName": "Place Name", "address": "Address", "geographicLocation": "Geographic Location"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the NPO logo at any time after submitting the information, however it is mandatory before the ministry's approval."}, "reviewAndSubmit": {"worshipPlaceNameEn": "Worship Place Name (EN)", "worshipPlaceNameAr": "Worship Place Name (AR)", "religion": "Religion", "sect": "Sect", "worshipPlaceType": "Type of Worship Place", "executionDateAndPlace": "Execution Date and Place", "executionDate": "Execution Date", "executionPlace": "Execution Place", "startDate": "Start Date", "endDate": "End Date", "placeName": "Place Name", "address": "Address", "geographicLocation": "Geographic Location", "participantDetails": "Participant Details", "willHostParticipants": "Are you going to host any participants?", "insideUae": "Inside UAE", "outsideUae": "Outside UAE", "participantsInsideUae": "Participants Inside UAE", "participantsOutsideUae": "Participants Outside UAE", "idNumber": "ID Number", "name": "Name", "purposeOfHosting": "Purpose of Hosting", "actions": "Actions", "fullName": "Full Name", "nationality": "Nationality", "passportNumber": "Passport Number", "eidNumber": "EID Number", "activityAndEventDetails": "Activity / Event Details", "npoActivityOrEventAssociation": "NPO Activity or Event Association", "branch": "Branch", "targetGroupOfTheActivityOrEvent": "Target Group of the Activity or Event", "natureOfTheActivityOrEvent": "Nature of the Activity or Event", "thePurposeOfTheActivityOrEvent": "The Purpose of the Activity or Event", "workingPapersSummary": "A summary of the work papers that will be presented during the activity or event", "entityDetails": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Specified Joining Date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category", "passportCopy": "Passport Copy"}, "requestDetails": {"page-title": "Organize Activities / Events (Inside UAE)", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "worshipPlaceNameEn": "Worship Place Name (EN)", "worshipPlaceNameAr": "Worship Place Name (AR)", "religion": "Religion", "sect": "Sect", "worshipPlaceType": "Type of Worship Place", "executionDateAndPlace": "Execution Date and Place", "executionDate": "Execution Date", "executionPlace": "Execution Place", "startDate": "Start Date", "endDate": "End Date", "placeName": "Place Name", "address": "Address", "geographicLocation": "Geographic Location", "participantDetails": "Participant Details", "willHostParticipants": "Are you going to host any participants?", "insideUae": "Inside UAE", "outsideUae": "Outside UAE", "participantsInsideUae": "Participants Inside UAE", "participantsOutsideUae": "Participants Outside UAE", "idNumber": "ID Number", "name": "Name", "purposeOfHosting": "Purpose of Hosting", "actions": "Actions", "fullName": "Full Name", "nationality": "Nationality", "passportNumber": "Passport Number", "eidNumber": "EID Number", "activityAndEventDetails": "Activity / Event Details", "npoActivityOrEventAssociation": "NPO Activity or Event Association", "branch": "Branch", "targetGroupOfTheActivityOrEvent": "Target Group of the Activity or Event", "natureOfTheActivityOrEvent": "Nature of the Activity or Event", "thePurposeOfTheActivityOrEvent": "The Purpose of the Activity or Event", "workingPapersSummary": "A summary of the work papers that will be presented during the activity or event", "entityDetails": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Specified Joining Date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category", "passportCopy": "Passport Copy"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "activitiesAndEventsParticipation": {"title": "Participate in Activities and Events Request (Inside/Outside UAE)", "desc": "", "forms": {"entityDetails": {"title": "Requesting Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date"}, "organizerEntityDetails": {"title": "Organizer En<PERSON><PERSON> Details", "EntityName": "Entity Name", "EntitySector": "Entity Category"}, "activityAndEventDetails": {"title": "Activity/Event Details", "activityAndEventTitle": "Activity/Event Name", "activityAndEventType": "Activity/Event Type", "activityAndEventDesc": "Activity/Event Description", "dateAndLocation": "Date & Location", "startDate": "Start Date", "endDate": "End Date", "location": "Location", "emirate": "Emirate", "country": "Country", "address": "Location of the activity/event", "geographicLocation": "Geographic Location"}, "participationDetails": {"title": "Participation Details", "natureOfParticipationDetails": "Nature of Participation Details", "npoMembersOrEmployeesParticipations": "NPO Internal Participants", "addParticipantMember": "Add Participant Member", "searchForMember": "Search for Member", "searchForEmployee": "Search for Employee", "addParticipant": "Add Internal Participants", "addExternalParticipant": "Add External Participant", "membershipNumber": "Membership Number", "name": "Name", "participantName": "Participant Name", "participantType": "Participant Type", "participantEmiratesId": "Participant Emirates ID", "externalParticipantEmiratesId": "External Participant Emirates ID", "externalParticipantDateOfBirth": "External Participant DOB", "participantDateOfBirth": "Participant DOB", "participantNationality": "Participant Nationality", "status": "Status", "actions": "Actions", "nationality": "Nationality", "passportNumber": "Passport Number", "eidNumber": "EID Number", "dateOfBirth": "Date Of Birth", "externalParticipants": "External Participants", "edit": "Edit ", "getInformation": "Get Information", "searchByMemebrParticipant": "Search for Member", "searchByEmplyeeParticipant": "Search for Employee"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the NPO logo at any time after submitting the information, however it is mandatory before the ministry's approval."}, "reviewAndSubmit": {"participationDetails": "Participation Details", "natureOfParticipationDetails": "Nature of Participation Details", "npoMembersOrEmployeesParticipations": "NPO Internal Participants ", "addParticipantMember": "Add Participant Member", "searchForMember": "Search for Member", "searchForEmployee": "Search for Employee", "addParticipant": "Add Participant", "addExternalParticipant": "Add External Participant", "membershipNumber": "Membership Number", "participantName": "Participant Name", "participantType": "Participant Type", "participantEmiratesId": "Participant Emirates ID", "participantDateOfBirth": "Participant DOB", "participantNationality": "Participant Nationality", "externalParticipants": "External Participants", "activityAndEventTitle": "Activity/Event Name", "activityAndEventType": "Activity/Event Type", "activityAndEventDesc": "Activity/Event Description", "dateAndLocation": "Date & Location", "location": "Location", "emirate": "Emirate", "country": "Country", "organizerEntityDetails": "Organizer En<PERSON><PERSON> Details", "EntitySector": "Entity Category", "executionDateAndPlace": "Execution Date and Place", "executionDate": "Execution Date", "executionPlace": "Execution Place", "startDate": "Start Date", "endDate": "End Date", "placeName": "Place Name", "address": "Location of the activity/event", "geographicLocation": "Geographic Location", "idNumber": "ID Number", "name": "Name", "actions": "Actions", "fullName": "Full Name", "nationality": "Nationality", "passportNumber": "Passport Number", "eidNumber": "EID Number", "activityAndEventDetails": "Activity / Event Details", "npoActivityOrEventAssociation": "NPO Activity or Event Association", "natureOfTheActivityOrEvent": "Nature of the Activity or Event", "thePurposeOfTheActivityOrEvent": "The Purpose of the Activity or Event", "workingPapersSummary": "A summary of the work papers that will be presented during the activity or event", "entityDetails": "Requesting Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Specified Joining Date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category", "npoLegalForm": "NPO Legal Form"}, "requestDetails": {"participate-title": "Participate in Activities and Events Request (Inside/Outside UAE)", "entityDetails": "Requesting Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Specified Joining Date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectivesEn": "Objectives (English)", "ObjectivesAR": "Objectives (Arabic)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Meeting Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective (English)": "Means of Achieving Objective (English)", "Means of Achieving Objective (Arabic)": "Means of Achieving Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founding Meeting", "NPO_LICENSE_DECLARATION": "Non-Profit Organization Declaration Request", "NPO_ByDecree": "NPO By Decree Registration", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section for ", "COMMENT": "Comment", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "fundServiceObjectives": "Fund Activities and Objectives", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "category": "Category", "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save", "participationDetails": "Participation Details", "natureOfParticipationDetails": "Nature of Participation Details", "npoMembersOrEmployeesParticipations": "NPO Internal Participants ", "addParticipantMember": "Add Participant Member", "searchForMember": "Search for Member", "searchForEmployee": "Search for Employee", "addParticipant": "Add Internal Participants", "addExternalParticipant": "Add External Participant", "membershipNumber": "Membership Number", "participantName": "Participant Name", "participantType": "Participant Type", "participantEmiratesId": "Participant Emirates ID", "participantDateOfBirth": "Participant DOB", "participantNationality": "Participant Nationality", "externalParticipants": "External Participants", "activityAndEventTitle": "Activity/Event Name", "activityAndEventType": "Activity/Event Type", "activityAndEventDesc": "Activity/Event Description", "dateAndLocation": "Date & Location", "location": "Location", "emirate": "Emirate", "country": "Country", "organizerEntityDetails": "Organizer En<PERSON><PERSON> Details", "EntitySector": "Entity Category", "executionDateAndPlace": "Execution Date and Place", "executionDate": "Execution Date", "executionPlace": "Execution Place", "startDate": "Start Date", "endDate": "End Date", "placeName": "Place Name", "address": "Location of the activity/event", "geographicLocation": "Geographic Location", "idNumber": "ID Number", "name": "Name", "actions": "Actions", "fullName": "Full Name", "nationality": "Nationality", "passportNumber": "Passport Number", "eidNumber": "EID Number", "activityAndEventDetails": "Activity / Event Details", "npoActivityOrEventAssociation": "NPO Activity or Event Association", "natureOfTheActivityOrEvent": "Nature of the Activity or Event", "thePurposeOfTheActivityOrEvent": "The Purpose of the Activity or Event", "workingPapersSummary": "A summary of the work papers that will be presented during the activity or event", "NpoLegalForm": "NPO Legal Form", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "openingBranchRequest": {"title": "Request for Approval of Opening NPO Branch", "desc": "", "forms": {"entityDetails": {"title": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date"}, "newBranchDetails": {"title": "New Branch Details", "newBranchDetails": "New Branch Details", "branchNameAr": "Branch Name (Arabic)", "branchNameEn": "Branch Name (English)", "branchEmirate": "Branch Emirate", "branchManager": "Branch Manager", "selectBranchManager": "Select Branch Manager", "help": "Help"}, "branchMembersList": {"title": "Branch Members List", "branchMembersList": "Branch Members List", "membershipNumber": "Membership No", "emiratesId": "Emirates ID", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "gender": "Gender", "nationality": "Nationality", "emirate": "Emirate", "membershipStatus": "Membership Status"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the NPO logo at any time after submitting the information, however it is mandatory before the ministry's approval."}, "reviewAndSubmit": {"branchMembersList": "Branch Members List", "membershipNumber": "Membership No", "emiratesId": "Emirates ID", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "gender": "Gender", "nationality": "Nationality", "emirate": "Emirate", "membershipStatus": "Membership Status", "newBranchDetails": "New Branch Details", "branchNameAr": "Branch Name (Arabic)", "branchNameEn": "Branch Name (English)", "branchEmirate": "Branch Emirate", "branchManager": "Branch Manager", "selectBranchManager": "Select Branch Manager", "entityDetails": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Start date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category"}, "requestDetails": {"NpoLegalForm": "NPO Legal Form", "newBranchDetails": "New Branch Details", "branchNameAr": "Branch Name (Arabic)", "branchNameEn": "Branch Name (English)", "branchEmirate": "Branch Emirate", "branchManager": "Branch Manager", "selectBranchManager": "Select Branch Manager", "branchMembersList": "Branch Members List", "membershipNumber": "Membership No", "emiratesId": "Emirates ID", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "gender": "Gender", "nationality": "Nationality", "emirate": "Emirate", "membershipStatus": "Membership Status", "title": "Review & Submit", "details-title": "Request for Approval of Opening NPO Branch", "entityDetails": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Start date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectivesEn": "Objectives (English)", "ObjectivesAR": "Objectives (Arabic)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Meeting Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective (English)": "Means of Achieving Objective (English)", "Means of Achieving Objective (Arabic)": "Means of Achieving Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founding Meeting", "NPO_LICENSE_DECLARATION": "Non-Profit Organization Declaration Request", "NPO_ByDecree": "NPO By Decree Registration", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section for ", "COMMENT": "Comment", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "fundServiceObjectives": "Fund Activities and Objectives", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "category": "Category", "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "nocRequestService": {"title": "Request for Receive Donations NOC", "desc": "", "forms": {"legalTypePage": {"title": "Request for Receive Donations NOC", "selectNpoLegalForm": "Select NPO Legal Form"}, "npoDetails": {"title": "NPO Details", "npoDetails": "NPO Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date"}, "donorDetails": {"title": "Donor Details", "donorDetails": "Donor Details", "donorType": "Donor Type", "fullName": "Full Name", "nationality": "Nationality", "placeOfBirth": "Place of Birth", "currentResidence": "Current Residence", "mobile": "Mobile Number", "email": "Email Address", "passportNumber": "Passport Number", "passportType": "Passport Type", "passportExpiryDate": "Passport Expiry Date", "jobTitle": "Job Title", "employer": "Employer", "companyName": "Company Name", "country": "Country", "city": "City", "licensingAuthority": "Licensing Authority", "commercialLicenseNumber": "Commercial License Number", "licenseIssuanceDate": "License Issuance Date", "licenseExpiryDate": "License Expiry Date", "phoneNumber": "Phone Number", "entityName": "Entity Name", "businessDomain": "Business Domain", "licenseNumber": "License Number", "npoCategory": "Main Category", "otherPassportType": "Please enter Passport Type", "emiratesId": "Emirates ID", "dateOfBirth": " Date of Birth", "getInformation": "Get Information", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "unifiedNumber": "Unified Number", "emirate": "Emirate", "entityType": "Entity Type", "entityOrCompanyName": "Entity / Company Name", "unifiedNumberOrNpoName": "Unified Number / NPO Name", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "mainCategory": "Main Category", "headquartersEmirate": "Headerquarters Emirate", "licensingEntity": "Licensing Entity", "actions": "Actions"}, "typeOfDonations": {"title": "Type Of Donations", "typeOfDonations": "Type Of Donations", "selectCurrencyType": "Select Currency Type", "typeOfCashDonations": "Cash Donations", "totalCashDonationsAmount": "Total Cash Donations Amount in AED", "typeOfInkindDonations": "In-kind Donations", "totalInkindDonationsAmount": "Total In-kind Donations Amount in AED", "grandTotalDonationsAmount": "Grand Total Donation Amount in AED", "grandTotalDonations": "Grand Total Donation", "addCashDonationType": "Add Cash Donation Type", "addInkindDonationType": "Add In-kind Donation Type", "editCashDonationType": "Edit Cash Donation Type", "editInkindDonationType": "Edit In-Kind Donation Type", "selectCashDonationType": "Select Cash Donation Type", "selectInkindDonationType": "Select In-Kind Donation Type", "cashDonationType": "Cash Donation Type", "inkindDonationType": "In-Kind Donation Type", "cashDonationTypeEn": "Cash Donation Type (EN)", "cashDonationTypeAr": "Cash Donation Type (AR)", "inkindDonationTypeEn": "In-Kind Donation Type (EN)", "inkindDonationTypeAr": "In-Kind Donation Type (AR)", "currencyType": "Currency Type", "enterCurrencyType": "Enter Currency Type", "enterCountOrQuantuty": "Enter Count or Quantity", "approximateValueAed": "Approximate Value in AED", "actions": "Actions", "countOrQuantity": "Count / Quantity"}, "npoBankAccountDetails": {"title": "NPO Bank Account Details", "npoBankAccountDetails": "NPO Bank Account Details", "selectBank": "Select Bank", "bank": "Bank", "bankAccount": "Bank Account", "selectBankAccount": "Select Bank Account", "accountOwnerNameEn": "Account Owner Name (English)", "accountOwnerNameAr": "Account Owner Name (Arabic)", "branchName": "Branch Name", "bankAddress": "Bank Address", "ibanNumber": "IBAN Number", "accountType": "Account Type", "accountOpenDate": "Account Open Date", "currencyType": "Currency Type"}, "donorBankAccountDetails": {"title": "Donor Bank Account Details", "donorBankAccountDetails": "Donor Bank Account Details", "country": "Country", "bankName": "Bank Name", "accountNumber": "Account Number", "ibanNumber": "IBAN Number"}, "donationPurpose": {"title": "Donation Purpose", "donationPurpose": "Donation Purpose"}, "donationDateAndDuration": {"title": "Donation Date/Duration", "donationDateAndDuration": "Donation Date/Duration", "dateOfReceivingDonations": "Date of Receiving the Donations", "fromDate": "From date", "toDate": "To Date"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the NPO logo at any time after submitting the information, however it is mandatory before the ministry's approval."}, "reviewAndSubmit": {"unifiedNumberOrNpoName": "Unified Number / NPO Name", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoDeclarationDecision": "NPO Declaration Decision Link", "mainCategory": "Main Category", "headquartersEmirate": "Headerquarters Emirate", "licensingEntity": "Licensing Entity", "inkindDonationTypeEn": "In-Kind Donation Type (EN)", "inkindDonationTypeAr": "In-Kind Donation Type (AR)", "cashDonationTypeEn": "Cash Donation Type (EN)", "cashDonationTypeAr": "Cash Donation Type (AR)", "approximateValueAed": "Approximate Value in AED", "countOrQuantity": "Count / Quantity", "npoDetails": "NPO Details", "bank": "Bank", "bankAccount": "Bank Account", "EntityCategory": "Entity Category", "NpoLegalForm": "NPO Legal Form", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "donorDetails": "Donor Details", "donorType": "Donor Type", "fullName": "Full Name", "entityType": "Entity Type", "nationality": "Nationality", "placeOfBirth": "Place of Birth", "currentResidence": "Current Residence", "mobile": "Mobile Number", "email": "Email Address", "passportNumber": "Passport Number", "passportType": "Passport Type", "passportExpiryDate": "Passport Expiry Date", "jobTitle": "Job Title", "employer": "Employer", "companyName": "Company Name", "country": "Country", "city": "City", "licensingAuthority": "Licensing Authority", "commercialLicenseNumber": "Commercial License Number", "licenseIssuanceDate": "License Issuance Date", "licenseExpiryDate": "License Expiry Date", "phoneNumber": "Phone Number", "entityName": "Entity Name", "businessDomain": "Business Domain", "licenseNumber": "License Number", "npoCategory": "Main Category", "typeOfDonations": "Type Of Donations", "typeOfCashDonations": "Cash Donations", "totalCashDonationsAmount": "Total Cash Donations Amount in AED", "typeOfInkindDonations": "In-kind Donations", "totalInkindDonationsAmount": "Total In-kind Donations Amount in AED", "grandTotalDonationsAmount": "Grand Total Donation Amount in AED", "grandTotalDonations": "Grand Total Donation", "npoBankAccountDetails": "NPO Bank Account Details", "selectBank": "Select Bank", "selectBankAccount": "Select Bank Account", "accountOwnerNameEn": "Account Owner Name (English)", "accountOwnerNameAr": "Account Owner Name (Arabic)", "branchName": "Branch Name", "bankAddress": "Bank Address", "ibanNumber": "IBAN Number", "accountType": "Account Type", "accountOpenDate": "Account Open Date", "currencyType": "Currency Type", "donorBankAccountDetails": "Donor Bank Account Details", "bankName": "Bank Name", "accountNumber": "Account Number", "donationPurpose": "Donation Purpose", "donationDateAndDuration": "Donation Date/Duration", "dateOfReceivingDonations": "Date of Receiving the Donations", "fromDate": "From date", "toDate": "To Date", "branchMembersList": "Branch Members List", "membershipNumber": "Membership No", "emiratesId": "Emirates ID", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "gender": "Gender", "emirate": "Emirate", "entityOrCompanyName": "Entity / Company Name", "membershipStatus": "Membership Status", "branchNameAr": "Branch Name (Arabic)", "branchNameEn": "Branch Name (English)", "branchEmirate": "Branch Emirate", "branchManager": "Branch Manager", "selectBranchManager": "Select Branch Manager", "entityDetails": "Entity Details", "npoLegalForm": "NPO Legal Form", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Start date", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category"}, "requestDetails": {"nocRequestServiceTitle": "Request for Receive Donations NOC", "unifiedNumberOrNpoName": "Unified Number / NPO Name", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoDeclarationDecision": "NPO Declaration Decision Link", "mainCategory": "Main Category", "headquartersEmirate": "Headerquarters Emirate", "licensingEntity": "Licensing Entity", "inkindDonationTypeEn": "In-Kind Donation Type (EN)", "inkindDonationTypeAr": "In-Kind Donation Type (AR)", "cashDonationTypeEn": "Cash Donation Type (EN)", "cashDonationTypeAr": "Cash Donation Type (AR)", "approximateValueAed": "Approximate Value in AED", "countOrQuantity": "Count / Quantity", "npoDetails": "NPO Details", "bank": "Bank", "bankAccount": "Bank Account", "EntityCategory": "Entity Category", "NpoLegalForm": "NPO Legal Form", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "donorDetails": "Donor Details", "donorType": "Donor Type", "fullName": "Full Name", "nationality": "Nationality", "placeOfBirth": "Place of Birth", "currentResidence": "Current Residence", "mobile": "Mobile Number", "email": "Email Address", "entityType": "Entity Type", "passportNumber": "Passport Number", "passportType": "Passport Type", "passportExpiryDate": "Passport Expiry Date", "jobTitle": "Job Title", "employer": "Employer", "companyName": "Company Name", "country": "Country", "city": "City", "licensingAuthority": "Licensing Authority", "commercialLicenseNumber": "Commercial License Number", "licenseIssuanceDate": "License Issuance Date", "licenseExpiryDate": "License Expiry Date", "phoneNumber": "Phone Number", "entityName": "Entity Name", "businessDomain": "Business Domain", "licenseNumber": "License Number", "npoCategory": "Main Category", "typeOfDonations": "Type Of Donations", "typeOfCashDonations": "Cash Donations", "totalCashDonationsAmount": "Total Cash Donations Amount in AED", "typeOfInkindDonations": "In-kind Donations", "totalInkindDonationsAmount": "Total In-kind Donations Amount in AED", "grandTotalDonationsAmount": "Grand Total Donation Amount in AED", "grandTotalDonations": "Grand Total Donation", "npoBankAccountDetails": "NPO Bank Account Details", "selectBank": "Select Bank", "selectBankAccount": "Select Bank Account", "accountOwnerNameEn": "Account Owner Name (English)", "accountOwnerNameAr": "Account Owner Name (Arabic)", "branchName": "Branch Name", "bankAddress": "Bank Address", "ibanNumber": "IBAN Number", "accountType": "Account Type", "accountOpenDate": "Account Open Date", "currencyType": "Currency Type", "donorBankAccountDetails": "Donor Bank Account Details", "bankName": "Bank Name", "accountNumber": "Account Number", "donationPurpose": "Donation Purpose", "donationDateAndDuration": "Donation Date/Duration", "dateOfReceivingDonations": "Date of Receiving the Donations", "fromDate": "From date", "toDate": "To Date", "branchMembersList": "Branch Members List", "membershipNumber": "Membership No", "emiratesId": "Emirates ID", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "gender": "Gender", "emirate": "Emirate", "membershipStatus": "Membership Status", "branchNameAr": "Branch Name (Arabic)", "branchNameEn": "Branch Name (English)", "branchEmirate": "Branch Emirate", "branchManager": "Branch Manager", "selectBranchManager": "Select Branch Manager", "entityDetails": "Entity Details", "npoLegalForm": "NPO Legal Form", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Start date", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category", "NPO_LICENSE_DECLARATION": "Non-Profit Organization Declaration Request", "NPO_ByDecree": "NPO By Decree Registration", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section for ", "COMMENT": "Comment", "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "openingNewBankAccount": {"title": "Opening New Bank Account Request", "desc": "", "forms": {"entityDetails": {"title": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "NpoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "worshipPlaceNameAr": "Worship Place Name (AR)", "worshipPlaceNameEn": "Worship Place Name (EN)", "religion": "Religion", "sect": "Sect", "worshipPlaceType": "Type of Worship Place"}, "bankInformation": {"title": "Bank Information", "bankInformation": "Bank Information", "selectBank": "Select Bank", "bank": "Bank", "accountCurrency": "Account <PERSON><PERSON><PERSON><PERSON>"}, "authorizationMatrix": {"title": "Authorization Matrix", "authorizationMatrix": "Authorization Matrix", "authorizationMatrixType": "Authorization Matrix Type", "authorizedSignatureGroup": "Authorized Signature Group", "authorizedSignatories": "Authorized Signatories", "boardTermExpiryDate": "Board Term Expiry Date", "setAuthorizationMatrixType": "Set Authorization Matrix Type", "addAuthorizedSignatureGroup": "Add Authorized Signature Group", "authorizedSignatureGroupList": "Authorized Signature Group List", "addAuthorizedSignatory": "Add Authorized Signatory", "authorizedSignatoriesList": "Authorized Signatories List", "amountThreshold": "Amount <PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "position": "Position", "name": "Name", "isBoardMember": "Is Board Member", "actions": "Actions", "assignedGroup": "Assigned Group", "editAuthorizedSignatory": "Edit Authorized Signatory", "groupName": "Group Name", "soloSignatories": "Authorized Signatories (Solo Signatory)", "dualSignatories": "Authorized Signatories (Dual Signatory)", "soloAndDualSignatories": "Authorized Signatories (Solo and Duo Signatory)", "groupsSignatories": "Authorized Signatories (Groups Signatory)"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size", "membershipConditions": "Membership Conditions", "condition1": "The number of founding members shall not be less than seven members", "condition2": "The percentage of the founding members holding the nationality of the state shall not be less than 70% of the total number of founding members. Persons who do not hold the nationality of the state may participate in establishing associations in accordance with the following controls.", "condition2.1": "Their number should not exceed 30% of the total number of founding members.", "condition2.2": "The member does not hold diplomatic status.", "condition2.3": "He must have a valid residence permit in the country for a period of no less than three years.", "condition3": "The founding member must be of the age of majority in accordance with the legislation in force in the country", "condition4": "The founding member must be of good conduct and good reputation, and has not previously been sentenced to a freedom-restricting penalty for a felony or misdemeanor that violates honor or trust unless he has been rehabilitated.", "help": "Help", "helpMessage": "You may attach the NPO logo at any time after submitting the information, however it is mandatory before the ministry's approval."}, "reviewAndSubmit": {"groupName": "Group Name", "soloSignatories": "Authorized Signatories (Solo Signatory)", "dualSignatories": "Authorized Signatories (Dual Signatory)", "soloAndDualSignatories": "Authorized Signatories (Solo and Duo Signatory)", "groupsSignatories": "Authorized Signatories (Groups Signatory)", "name": "Name", "position": "Position", "isBoardMember": "Is Board Member", "assignedGroup": "Assigned Group", "amountThreshold": "Amount <PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "bank": "Bank", "authorizationMatrix": "Authorization Matrix", "authorizationMatrixType": "Authorization Matrix Type", "authorizedSignatureGroup": "Authorized Signature Group", "authorizedSignatories": "Authorized Signatories", "boardTermExpiryDate": "Board Term Expiry Date", "setAuthorizationMatrixType": "Set Authorization Matrix Type", "addAuthorizedSignatureGroup": "Add Authorized Signature Group", "authorizedSignatureGroupList": "Authorized Signature Group List", "addAuthorizedSignatory": "Add Authorized Signatory", "authorizedSignatoriesList": "Authorized Signatories List", "bankInformation": "Bank Information", "selectBank": "Select Bank", "accountCurrency": "Account <PERSON><PERSON><PERSON><PERSON>", "worshipPlaceNameAr": "Worship Place Name (AR)", "worshipPlaceNameEn": "Worship Place Name (EN)", "religion": "Religion", "sect": "Sect", "worshipPlaceType": "Type of Worship Place", "branchMembersList": "Branch Members List", "membershipNumber": "Membership No", "emiratesId": "Emirates ID", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "gender": "Gender", "nationality": "Nationality", "emirate": "Emirate", "membershipStatus": "Membership Status", "newBranchDetails": "New Branch Details", "branchNameAr": "Branch Name (Arabic)", "branchNameEn": "Branch Name (English)", "branchEmirate": "Branch Emirate", "branchManager": "Branch Manager", "selectBranchManager": "Select Branch Manager", "entityDetails": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Start date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category"}, "requestDetails": {"groupName": "Group Name", "soloSignatories": "Authorized Signatories (Solo Signatory)", "dualSignatories": "Authorized Signatories (Dual Signatory)", "soloAndDualSignatories": "Authorized Signatories (Solo and Duo Signatory)", "groupsSignatories": "Authorized Signatories (Groups Signatory)", "name": "Name", "position": "Position", "isBoardMember": "Is Board Member", "assignedGroup": "Assigned Group", "amountThreshold": "Amount <PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "bank": "Bank", "authorizationMatrix": "Authorization Matrix", "authorizationMatrixType": "Authorization Matrix Type", "authorizedSignatureGroup": "Authorized Signature Group", "authorizedSignatories": "Authorized Signatories", "boardTermExpiryDate": "Board Term Expiry Date", "setAuthorizationMatrixType": "Set Authorization Matrix Type", "addAuthorizedSignatureGroup": "Add Authorized Signature Group", "authorizedSignatureGroupList": "Authorized Signature Group List", "addAuthorizedSignatory": "Add Authorized Signatory", "authorizedSignatoriesList": "Authorized Signatories List", "bankInformation": "Bank Information", "selectBank": "Select Bank", "accountCurrency": "Account <PERSON><PERSON><PERSON><PERSON>", "worshipPlaceNameAr": "Worship Place Name (AR)", "worshipPlaceNameEn": "Worship Place Name (EN)", "religion": "Religion", "sect": "Sect", "worshipPlaceType": "Type of Worship Place", "branchMembersList": "Branch Members List", "membershipNumber": "Membership No", "emiratesId": "Emirates ID", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "gender": "Gender", "nationality": "Nationality", "emirate": "Emirate", "membershipStatus": "Membership Status", "newBranchDetails": "New Branch Details", "branchNameAr": "Branch Name (Arabic)", "branchNameEn": "Branch Name (English)", "branchEmirate": "Branch Emirate", "branchManager": "Branch Manager", "selectBranchManager": "Select Branch Manager", "entityDetails": "Entity Details", "EntityCategory": "Entity Category", "NpoNameEn": "NPO Name (English)", "NpoNameAr": "NPO Name (Arabic)", "npoLegalForm": "NPO Legal Form", "NpoDeclarationDecision": "NPO Declaration Decision Link", "MainCategory": "Main Category", "HeadquarterEmirate": "Headquarter Emirate", "LicensingEntity": "Licensing Entity", "LicenseNumber": "License Number", "LicenseIssuanceDate": "License Issuance Date", "LicenseExpiryDate": "License Expiry Date", "externalEntityDetails": "External Entity Details", "requestType": "Request Type", "specifiedDate": "Start date", "entityName": "Entity Name", "entityLegalForm": "Entity Legal Form", "headquartersCountry": "Headquarters Country", "theNatureOfTheEntityMainActivities": "The Nature of the Entity's Main Activities", "objectivesAndGoalsOfTheEntity": "Objectives and Goals of the Entity", "goalsAndObjectivesCompatibility": "The extent to which the goals and objectives of the external party are compatible with the goals and objectives of the association/institution", "ministryComments": "Ministry Comments", "issuanceDate": "Issuance Date", "proposedNameEn": "Proposed Name (EN)", "proposedNameAr": "Proposed Name (AR)", "NameEn": "Name (EN)", "NameAr": "Name (AR)", "Landline": "Landline", "POBox": "P.O. Box", "FaxNumber": "Fax Number", "Email": "Email", "Website": "Website", "Address": "Address", "Emirate": "Emirate", "permanentAdvance": "Permanent Advance", "fundsAllocation": "Funds Allocation", "fundsAllocationAmount": "Funds Allocation Amount", "associationsNationalSocieties": "Associations or National Societies from the Union List", "title": "Review & Submit", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "landlineNumber": "Landline Number", "ObjectiveEn": "Objective (EN)", "ObjectiveAr": "Objective (AR)", "MeansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "MeansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)", "BasicInformation": "Basic Information", "Objectives": "Objectives", "foundingMembers": "Founding Members", "interimCommittee": "Interim Committee", "membership": "Membership & Enrollment", "boardOfDirectors": "Board of Directors", "uploadDocuments": "Documents", "exceptionCases": "Exception Cases", "IsFoundingMembersHoldingTheNationalityIsLessThan70": "Is Founding Members Holding the Nationality Less Than 70?", "ExceptionReasonFor70En": "Reason for Exception (English)", "ExceptionReasonFor70Ar": "Reason for Exception (Arabic)", "IsNumberOfFoundingMembersIsLessThan7Members": "Is Number of Founding Members Less Than 7?", "nationalityType": "Nationality Type", "foundingMemberAgeIsLessThan21YearsOld": "Is Founding Member Age Less Than 21?", "foundingMemberResidencyIsLessThan3Years": "Is Founding Member Residency Less Than 3 Years?", "foundingMemberHasDiplomaticStatus": "Does the Founding Member Have Diplomatic Status?", "founderEmiratesID": "Emirates ID", "dateOfBirth": "Date of Birth", "exceptionReasonAr": "Exception Reason (AR)", "exceptionReasonEn": "Exception Reason (EN)", "boardOfTrustees": "Board Of Trustees", "memberPosition": "Member Position", "MeetingPlace": "Place", "Date": "Date", "Agenda": "Agenda", "membershipConditionEn": "Membership Condition (English)", "membershipConditionAr": "Membership Condition (Arabic)", "MembershipFees": "Membership Fees", "AnnualMembershipDueDate": "Annual Membership Due Date", "EnrollmentFees": "Enrollment Fees", "adminPositionTitleEnglish": "Position Title (EN)", "adminPositionTitleArabic": "Position Title (AR)", "nominationConditionEnglish": "Nomination Condition (English)", "nominationConditionArabic": "Nomination Condition (Arabic)", "FrequencyOfMonthlyBoardMeetings": "Frequency of Monthly Board Meetings", "localBoardMembersPercentage": "Local Board Members Percentage", "ElectionMethod": "Election Method", "NumberOfPermissibleTerms": "Number of Permissible Terms", "BoardElectionCycle": "Board Election Frequency", "MemberIsExceeds11": "Member Exceeds 11", "exceptionRequests": "Exception Requests", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsConditions": "Board of Directors Conditions", "boardOfDirectorsPositions": "Board of Directors Positions", "CanBeRenominated": "Can Be Renominated", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "foundingMember": "Founding Member", "administrativePositionTitleAr": "Administrative Position Title (Arabic)", "administrativePositionTitleEn": "Administrative Position Title (English)", "conditionForNominationAr": "Condition for Nomination (Arabic)", "conditionForNominationEn": "Condition for Nomination (English)", "frequencyOfMeetings": "Frequency of Meetings", "numberOfMembers": "Number of Members", "frequencyOfAppointments": "Frequency of Appointments", "boardOfTrusteesInformation": "Board of Trustees Information", "boardOfTrusteesConditions": "Board of Trustees Conditions", "boardOfTrusteesPositions": "Board of Trustees Positions", "boardOfTrusteesMembers": "Board of Trustees Members", "Objective (English)": "Objective (English)", "Objective (Arabic)": "Objective (Arabic)", "Means of Achieving Objective": "Means of Achieving Objective", "Id": "Id", "temporaryCommitteeMemberEID": "EID", "temporaryCommitteeMemberName": "Name", "temporaryCommitteeMemberPosition": "Position", "status": "Status", "founderNameEnglish": "Name (EN)", "founderNameArabic": "Name (AR)", "founderNationality": "Nationality", "responseDate": "Response Date", "npoEstablishmentNameEN": "NPO Establishment Name EN", "npoEstablishmentNameAr": "NPO Establishment Name Ar", "npoEstablishmentDate": "NPO Establishment Date", "npoEstablishmentLegalFormEN": "NPO Establishment Legal Form EN", "npoEstablishmentLegalFormAr": "NPO Establishment Legal Form Ar", "AccountId": "Account Id", "npoUnifiedNumber": "NPO Unified Number", "yes": "Yes", "no": "No", "interimCommitteeMembers": "Interim Committee Members", "interimCommitteeInformation": "Interim Committee Information", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "foundersMeeting": "Founders Meeting", "targetGroupsAssociation": "Target groups of the association's activities", "targetGroupsNationalSociety": "Target groups of the national society activities", "addTargetGroup": "Add Target Group", "targetGroupNameEn": "Target Group Name (English)", "targetGroupNameAr": "Target Group Name (Arabic)", "FundServices": "Fund Services", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "Amount": "Amount", "DiscussingTheEstablishmentOfPublicBenefitInstitution": "Discussing the establishment of a public benefit institution", "PreparingTheDraftStatute": "Preparing the draft statute", "ElectionOfMembersOfTheTemporaryCommittee": "Election of members of the temporary committee", "Definetheadministrativepositions": "Define the administrative positions for the members", "Appointthecommissioner": "Appoint the commissioner of the interim committee", "id": "Id", "AllocationCycle": "Allocation Cycle", "FundsAmount": "Funds Amount", "DescriptionOfInKindFundsAr": "Description Of In-Kind Funds (Arabic)", "DescriptionOfInKindFundsEn": "Description Of In-Kind Funds (English)", "NatureOfFundsAllocated": "Nature Of Funds Allocated", "foundingMemberEID": "Founding Member EID", "addAllocationOfFoundationFunds": "Add Fund Allocation", "editAllocationOfFoundationFunds": "Edit Fund Allocation", "Actions": "Actions", "help": "Help", "allocationOfFoundationFunds": "Allocation of Foundation Funds", "totalFundsAmountIs": "Total funds amount is", "agendaItems": "Agenda Items", "Position": "Position", "founderDecisionDate": "Founder Decision Date", "fundbelongsDetails": "Details of the entity to which the fund belongs", "EntityName": "Entity Name", "EntityNameEn": "Entity Name (EN)", "EntityNameAr": "Entity Name (AR)", "EntityType": "Entity Type", "NumberOfEmployees": "Number of Employees", "FundServiceObjectiveEn": "Fund Activities and Objectives (EN)", "FundServiceObjectiveAr": "Fund Activities and Objectives (AR)", "MeansOfAchievingObjective": "Means of Achieving Objective", "fundServiceObjectives": "Fund Activities and Objectives", "category": "Category", "ObjectivesEn": "Objectives (English)", "ObjectivesAR": "Objectives (Arabic)", "Means of Achieving Objective (English)": "Means of Achieving Objective (English)", "Means of Achieving Objective (Arabic)": "Means of Achieving Objective (Arabic)", "NPO_LICENSE_DECLARATION": "Non-Profit Organization Declaration Request", "NPO_ByDecree": "NPO By Decree Registration", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section for ", "COMMENT": "Comment", "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "requestToJoinNpo": {"title": "Request To Join N<PERSON>", "desc": "", "forms": {"memberInformation": {"title": "Memeber Information", "memberInformation": "Memeber Information", "memberPersonalDetails": "Member Personal Details", "emiratesId": "Member Emirates ID", "emirate": "Emirate", "memberNameEn": "Member Name (English)", "memberNameAr": "Member Name (Arabic)", "gender": "Member Gender", "dateOfBirth": "Date of Birth", "nationality": "Member Nationality", "passportNumber": "Passport Number", "residencyIssuanceDate": "Residency Issuance Date", "residencyExpiryDate": "Residency Expiry Date", "unifiedNumber": "Unified Number", "mobile": "Mobile Number", "address": "Address", "email": "Email", "qualificationAndProfession": "Qualification & Profession", "qualification": "Qualification", "employer": "Employer", "jobTitle": "Job Title", "branchName": "Branch Name"}, "membershipInformation": {"title": "Membership Information", "membershipInformation": "Membership Information", "membershipDetails": "Membership Details", "membershipNumber": "Membership No", "membershipValidityAndStatus": "Membership Validity & Status", "membershipIssueDate": "Membership Issue Date", "membershipExpiryDate": "Membership Expiry Date", "membershipStatus": "Membership Status"}, "requestHistory": {"title": "Request History"}, "uploadDocuments": {"title": "Documents", "logoEn": "NPO Logo (English)", "logoAr": "NPO Logo (Arabic)", "npoLogo": "NPO Logo", "acceptedFiles": "File types accepted: JPG, PNG", "maxFileSize": "Maximum file size"}, "reviewAndSubmit": {"membershipInformation": "Membership Information", "membershipDetails": "Membership Details", "membershipNumber": "Membership No", "membershipValidityAndStatus": "Membership Validity & Status", "membershipIssueDate": "Membership Issue Date", "membershipExpiryDate": "Membership Expiry Date", "membershipStatus": "Membership Status", "memberInformation": "Memeber Information", "memberPersonalDetails": "Member Personal Details", "emiratesId": "Member Emirates ID", "emirate": "Emirate", "nameEn": "Member Name (English)", "nameAr": "Member Name (Arabic)", "gender": "Member Gender", "dateOfBirth": "Date of Birth", "nationality": "Member Nationality", "passportNumber": "Passport Number", "residencyIssuanceDate": "Residency Issuance Date", "residencyExpiryDate": "Residency Expiry Date", "unifiedNumber": "Unified Number", "mobile": "Mobile Number", "address": "Address", "email": "Email", "qualificationAndProfession": "Qualification & Profession", "qualification": "Qualification", "employer": "Employer", "jobTitle": "Job Title", "branchName": "Branch Name", "title": "Review & Submit", "requestToJoinNpo": "Request To Join N<PERSON>", "downloadBylaws": "Download the By-laws", "getFundingMembersConfirmation": "Get Founding Members Confirmation", "submitRequest": "Submit Request", "submitRequestedUpdates": "Submit Requested Updates", "saveAsDraft": "Save as Draft", "cancelRequest": "Cancel Request", "uploadDocuments": "Documents", "LogoEn": "<PERSON><PERSON> (English)", "LogoAr": "<PERSON><PERSON> (Arabic)", "status": "Status", "responseDate": "Response Date", "yes": "Yes", "no": "No", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)"}, "requestDetails": {"membershipInformation": "Membership Information", "membershipDetails": "Membership Details", "membershipNumber": "Membership No", "membershipValidityAndStatus": "Membership Validity & Status", "membershipIssueDate": "Membership Issue Date", "membershipExpiryDate": "Membership Expiry Date", "membershipStatus": "Membership Status", "memberInformation": "Memeber Information", "memberPersonalDetails": "Member Personal Details", "emiratesId": "Member Emirates ID", "emirate": "Emirate", "nameEn": "Member Name (English)", "nameAr": "Member Name (Arabic)", "gender": "Member Gender", "dateOfBirth": "Date of Birth", "nationality": "Member Nationality", "passportNumber": "Passport Number", "residencyIssuanceDate": "Residency Issuance Date", "residencyExpiryDate": "Residency Expiry Date", "unifiedNumber": "Unified Number", "mobile": "Mobile Number", "address": "Address", "email": "Email", "qualificationAndProfession": "Qualification & Profession", "qualification": "Qualification", "employer": "Employer", "jobTitle": "Job Title", "branchName": "Branch Name", "title": "Review & Submit", "requestToJoinNpo": "Request To Join N<PERSON>", "NPO_LICENSE_DECLARATION": "Non-Profit Organization Declaration Request", "NPO_ByDecree": "NPO By Decree Registration", "APPLICATION_NUMBER": "Application Number", "APPLICATION_STATUS": "Application Status", "SERVICE_NAME": "Service Name", "SUBMIT_DATE": "Submit Date", "APPLICATION_SUMMARY": "Application Summary", "EDIT_INFORMATION": "Edit Information", "DOWNLOAD_NPO_BY_LAW": "Download NPO By-Law", "DOWNLOAD_NPO_DECLARATION_DECISION": "Download NPO declaration decision", "DOWNLOAD_NPO_LICENSE": "Download NPO license", "ERROR_IN": "Error in", "MODIFICATION_REQUIRED_IN": "Modification required in", "INCLUDE_SECTION": "Within the section for ", "COMMENT": "Comment", "ServiceTitle": "Service Title (English)", "ServiceTitleAR": "Service Title (Arabic)", "id": "Id", "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "feedBack": {"Title": "<PERSON><PERSON><PERSON>", "index": "Index", "fieldName": "Field Name", "sectionName": "Section Name", "reviewerComments": "Reviewer Comments", "action": "Action", "goToSection": "Go to Section"}}, "approval": {"npoLegalFormTitle": "NPO Legal Form", "academicQualification": "Academic Qualification", "selectAcademicQualification": "Select Academic Qualification", "jobTitle": "Job Title", "selectJobTitle": "job title", "employer": "Employer", "selectEmployer": "employer", "qualificationInformation": "Qualification Information", "personalInformation": "Personal Information", "passportPhoto": "Passport Photo", "personalPhoto": "Personal Photo", "confirm": "Confirm", "reject": "Reject", "download": "Download NPO By-Law", "residencyExpiryDate": "Residency Expiry Date", "residencyIssuanceDate": "Residency Issuance Date", "emirate": "Emirate", "passportNumber": "Passport Number", "nationality": "Nationality", "mobileNumber": "Mobile Number", "email": "Email Address", "nameEnglish": "Name (English)", "nameArabic": "Name (Arabic)", "dob": "Date Of Birth", "emiratedIdNumber": "Emirates ID Number", "tableProposedNameAr": "Proposed Name (Arabic)", "tableProposedNameEn": "Proposed Name (English)", "tableId": "ID", "npoLegalFormAndProposedNames": "NPO Proposed Names", "foundingMemberConfirmation": "NPO Founding Members Confirmation"}, "congratulations": "The request has been submitted successfully", "submitType1": "Draft Saved Successfully", "submitType2": "The application data has been completed, awaiting confirmation from the founding members to submit the application successfully.", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response during working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "backToServices": "Back to Services", "Save": "Save"}, "generalForm": {"Service Form": "Service Form", "description": "Description", "issue971Title": "Request to issue a 971 from the community membership card", "reportingAbuseTitle": "Reporting an Abuse", "licensingNonGovPodCenterTitle": "Licensing of Non-Government People of Determination Care and Rehabilitation Center", "RequestAdTitle": "Request to publish an advertisement for family counseling centers", "renewfamilyCounselingLicenseTitle": "Request to renew the license of family counseling centers", "renewNonGovPodCenterTitle": "Request to renew the license of non-governmental institutions to rehabilitate people with disabilities Disability 'people of determination'", "licensingPrivateFamilyCounselingCenterTitle": "Request a license for private family counseling centers"}, "toWhomApply": {"title": "Issue To Whom It May Concern Regarding Social Support ", "desc": "A To Whom It May Concern Certificate is issued to people and agencies, to find out whether the people who are inquired about them receive social assistance from the MOCD or not. You must create a personal profile to benefit from this service.", "preferredEmail": "Email (the certificate will be sent to this email)", "preferredMobile": "Preferred Mobile Number (the certificate will be sent to this number)", "entityAddressed": "Entity Addressed", "otherEntity": "Other Entities", "typeOfCertificate": "Type of Certificate", "otherEntityAddressed": "Other Entity Addressed"}}, "submitResult": {"congratulations": "Congratulations", "submitType1": "Draft Saved Successfully", "submitType2": "You have successfully submitted your application to get the founding members confirmations", "submitType3": "You have successfully submitted your application", "reviewMessage": "We are reviewing your application, expect a response within 5 working days.", "appReferenceNumber": "Application Reference Number", "emailNotification": "You will be notified of any change to the status of your application via email", "smsNotification": "and SMS", "applicationStatus": "The status of your application is available in", "myApplications": "My Applications", "myCases": "My Inquiries/Suggestions", "backToServices": "Back to Services", "Save": "Save", "SubmissionFailed": "Submission failed", "SubmissionFailedDesc": "There was an issue with your application submission"}, "alert": {"deleteTitle": "Delete Row", "saveTheRequestAsDraftTitle": "Confirm Draft", "saveTheRequestAsSubmittTitle": "Confirm Submit", "title": "Are you sure?", "confirmDeleteFile": "Are you sure you want to delete this file?", "text": "You are about to submit the form.", "icon": "warning", "showCancelButton": true, "confirmButtonColor": "#3085d6", "cancelButtonColor": "#d33", "cancelButtonText": "No", "confirmButtonText": "Yes", "cancelDeleteButtonText": "Cancel", "confirmDeleteButtonText": "Delete"}, "notify": {"deleteMessage": "Are you sure you want to delete this row? This action cannot be undone.", "saveTheRequestAsDraftMessage": "Are you sure you want to save the request as draft?", "saveTheRequestAsSubmitMessage": "Are you sure you want to submit the request ?", "saveTheRequestAsConfirmMessage": "Are you sure you want to submit the request to get the founding members confirmation?", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "comingSoon": "Coming soon...", "validForm": "Valid form.", "invalidForm": "Please fill the required fields", "invalidFileSize": "Invalid File Size.", "invalidFileFormat": "Invalid File Format.", "noInternet": "No internet connection", "consentText": "Please agree to the terms and conditions", "draftSaved": "Draft saved successfully", "submitted": "Request submitted successfully", "sameApplicantApplyAnother": "Applicant and Another have same contact details, please select apply for my self.", "invalidObjectives": "Please add at least 3 objectives", "invalidMembers": "The number of founders must not be less than 7 members.", "invalidEmiratesIdDetails": "Invalid Emirates ID Details.", "anotherSameAsApplicant": "The user you trying to apply to is same as the user logged in, please select me instead of another.", "emirateIdExists": "Emirates ID already exists", "invalidAge": "Members must be at least 21 years old.", "invalidAge18": "Guardian must be at least 18 years old.", "sameAsGuardian": "The child's and guardian's Emirates IDs are the same. Please update the details.", "memberAdded": "Member added successfully", "invalidAgeToApply": "The child is more than five years old.Registration has not yet started in the care and rehabilitation centers - People of Determination.You will be notified later.", "isNotNational": "According to our system's requirements, only UAE Nationals or son of UAE national woman are eligible to apply to this service. Select 'Yes' to continue or 'No' to terminate the application.", "invalidEmirate": "This service is not available for Abu Dhabi residents", "sameAsApplicant": "The child's Emirates ID number entered is identical to the Emirates ID number used to log in via UAEPASS. Only the child's guardian can apply to this service - you must log in with the guardian's UAEPASS before starting the service.", "familyMembersNotUpdated": "Please update family members details", "fileDeletedSuccess": "File deleted successfully", "proposedNameExistsError": "this proposed name is already exist", "targetGroupExistsError": "Target group already exists", "conditionExistsError": "this condition is already exist", "beneficiaryExistsError": "Beneficiary already exists", "objectiveIsAlearyExist": "The entered objective already exists.", "positionExistsError": "this position is already exist", "complaintReopened": "Complaint reopened successfully", "submitTheRequest": "Are you sure you want to submit the request?", "DataNotCorrect": "Data is not correct.", "invalidDateFormat": "Invalid date format.", "ThisMemberIsAlreadyAdded": "This member is already added.", "minFundIs5Million": "The minimum funds amount is 5 million", "npoListWillBeAssociationsOrNationalSocieties": "Please ensure that the same legal form is assigned to all Associations or National Societies from the Union", "unionCategoryMismatch": "Please ensure that the same main classification is assigned to all Associations or National Societies from the Union.", "invalidNPONumber": "Invalid Data.", "NPONumberIsAlreadyExists": "The NPO Name/Number already exists.", "FundServicesIsAlearyExist": "The fund service already exists.", "purposesAndActivitieIsAlreadyExist": "Purpose and activity already exists", "participantExistsError": "Participant already exists.", "cashDonationTypeExistsError": "Cash Donation Type already exists", "inkindDonationTypeExistsError": "In-Kind Donation Type already exists", "authorizedSignatoryExistsError": "The Authorized Signatory already exists.", "authorizedGroupExistsError": "The group already exists.", "exampleOfActivitiesAlreadyExists": "Example Of Activity Already Exists"}, "userPages": {"applications": {"title": "Applications"}, "drafts": {"title": "Drafts"}, "socialCases": {"title": "Social Cases"}, "readyToPay": {"title": "Applications Ready To Pay"}, "financial": {"title": "Financial Transactions"}, "cases": {"title": "Inquiries / Suggestions"}, "documents": {"title": "Documents", "DocumentNumber": "DocumentNumber", "Status": "Status", "IssueDate": "IssueDate", "ExpiryDate": "ExpiryDate", "confirmText": "Dear Valued Customer... The system will create a new application for study that will allow you to upload the required documents in order to change the disability type. If your request to change disability type is approved, the previous request will be cancelled. If your request to change the disability type is rejected, the previous request will remain in the “Approved” status.", "ChangeDisability": "Change Disability"}, "myEstablishments": {"title": "My Establishments", "unifiedNumber": "Unified Number", "establishmentName": "Establishment Name", "legalForm": "Legal Form", "emirate": "Emirate", "licenseExpiryDate": "License Expiry Date", "status": "Status", "actions": "Actions", "viewEstablishment": "View Establishment", "editEstablishment": "Edit Establishment Details", "noDataFound": "No Data Found"}}, "validationMessage": {"required": "This field is required", "invalidDate": "Invalid Date", "minlength": "The minimum length is not met", "maxlength": "The maximum length is exceeded", "min": "The minimum value is not met. The minimum value is MinimumValue", "max": "The maximum value is exceeded. The maximum value is MaximumValue", "email": "Invalid email address", "pattern": "The pattern is not matched", "arabicOnly": "This field accepts Arabic letters only", "englishOnly": "This field accepts English letters only", "customEmail": "Invalid email format", "customMobile": "Invalid mobile format, mobile must start with 971 5", "invalidFaxNumber": "Fax number must start with 9716 or 9719", "invalidLandlineNumberStartWith6": "Landline number must start with 9716", "invalidLandlineNumberStartWith9": "Landline number must start with 9719", "invalidLandlineNumberStartWith5": "Landline number must start with 9715", "invalidLandlineNumberLength": "Invalid landline number", "invalidEmiratesId": "Invalid Emirates ID", "matDatetimePickerMax": "The date and time speicied value is invalid, please select a valid date and time.", "matDatetimePickerMin": "Interim committee meeting must be on or after the founders meeting and no later than today.", "matDatepickerMax": "The date specified must be smaller than or equal to the date.", "minLength": " This field accepts 3 characters as minimum", "minLengthIs2": " This field accepts 2 characters as minimum", "maxLength": "This field accepts 500 characters as minimum", "containsNumbers": "This field accepts only letters", "invalidPatternEn": "This field accepts English characters only", "invalidPatternAr": "This field accepts Arabic characters only", "invalidCharacters": "Only Arabic, English letters, and spaces are allowed.", "invalidNumber": "The value should be greater than 0", "invalidIban": "Invalid IBAN format", "invalidBankAccountFormat": "Invalid Bank Account format", "invalidText": "Please enter valid text.", "invalidPattern2En": "This field accepts English characters", "invalidPattern2Ar": "This field accepts Arabic characters", "dateRangeInvalid": "End date must be after start date.", "startDateInvalidRange": "Start date must be before End date.", "endDateInvalidRange": "End date must be after Start date.", "matDatepickerMin": "Date must be greater than or equal today", "invalidPatternNotNumOnly": "Accept arabic or english letters not number only, and spaces are allowed.", "lengthMemberError": "You must select a branch emirate with 7 or more members ", "internationalPhone": "International phone number is not correct", "accountMismatch": "The account number does not match the IBAN number."}, "npoNationalRegistry": {"dashboard": {"title": "NPO National Registry", "npoLegalFormTitle": "UAE NPO / Legal Form", "npoInUae": "NPO In UAE", "npoEmiratesCategory": "NPO Emirates Category", "npoCategoryPerEmirate": "NPO Category Per Emirate", "npoCountPerCategory": "NPO Count Per Category Across UAE"}, "npoList": {"title": "NPO List", "name": "Name", "legalForm": "Legal Form", "unifiedNumber": "Unified Number", "declarationDate": "Declaration Date", "emirate": "Emirate", "licensingAuthority": "Licensing Authority", "mainCategory": "Main Category", "licenseStatus": "License Status", "joinUs": "Join Us", "viewDetails": "View Details"}, "npoDetails": {"basicInformation": {"title": "Basic Information", "npoName": "NPO Name", "npoLegalForm": "NPO Legal Form", "npoContactDetails": "NPO Contact Details", "name": "Name", "localDecreeLawNumber": "Local Decree/Law Number", "issuanceDate": "Issuance Date", "associationClassification": "Association Classification", "landlineNumber": "Landline Number", "email": "Email", "website": "Website", "address": "Address", "geographicLocation": "Geographic Location", "emirate": "Emirate"}, "objectives": {"title": "Objectives", "objectivesList": "Objectives List", "objectiveEn": "Objective (EN)", "objectiveAr": "Objective (AR)", "meansOfAchievingObjectiveEn": "Means of Achieving Objective (EN)", "meansOfAchievingObjectiveAr": "Means of Achieving Objective (AR)"}, "membership": {"title": "Membership", "membershipInformation": "Membership Information", "membershipConditions": "Membership Conditions", "membershipFees": "Membership Fees", "enrollmentFees": "Enrollment Fees", "category": "Category", "membershipConditionEn": "Membership Condition (EN)", "membershipConditionAr": "Membership Condition (AR)"}, "boardOfDirectors": {"title": "Board of Directors", "boardOfDirectorsInformation": "Board of Directors Information", "numberOfBoardMembers": "Number of Board Members", "boardOfDirectorsPositions": "Board of Directors Positions", "adminPositionTitleArabic": "Administrative Position Title (AR)", "adminPositionTitleEnglish": "Administrative Position Title (EN)"}}}, "AED": "AED", "Save": "Save", "Show per page": "Show per page", "ViewAndEditApplication": "View/Edit Application", "View Application": "View Application", "Delete Application": "Delete Application", "New": "New", "Application Overview": "Application Overview", "Keep track of your applications and tasks": "Keep track of your applications and tasks", "Here you can easily access your applications, payments, and receive smart suggestions based on your data from UAE Pass": "Here you can easily access your applications, payments, and receive smart suggestions based on your data from UAE Pass", "Search for anything": "Search for anything", "Go to Main Website": "Go to Main Website", "All Services": "All Services", "Once you complete an application it will available here.": "Once you complete an application it will available here.", "You have no applications": "You have no applications", "missingRequiredFields": "Missing Required Fields", "missingRequiredDocs": "Missing Required Documents"}